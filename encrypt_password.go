package main

import (
    "crypto/aes"
    "crypto/cipher"
    "crypto/rand"
    "encoding/base64"
    "fmt"
    "io"
    "os"
)

func main() {
    if len(os.Args) < 2 {
        fmt.Println("Usage: go run encrypt_password.go <password>")
        os.Exit(1)
    }

    password := os.Args[1]
    // Development key - same as in your codebase
    key := []byte("abcdefghijklmnopqrstuvwxyz123456")

    encrypted, err := encryptAESGCM(password, key)
    if err != nil {
        fmt.Printf("Error encrypting password: %v\n", err)
        os.Exit(1)
    }

    fmt.Printf("Encrypted password: %s\n", encrypted)
}

func encryptAESGCM(plaintext string, key []byte) (string, error) {
    block, err := aes.NewCipher(key)
    if err != nil {
        return "", err
    }

    gcm, err := cipher.NewGCM(block)
    if err != nil {
        return "", err
    }

    nonce := make([]byte, gcm.NonceSize())
    if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
        return "", err
    }

    cipherBytes := gcm.Seal(nonce, nonce, []byte(plaintext), nil)
    return base64.StdEncoding.EncodeToString(cipherBytes), nil
}