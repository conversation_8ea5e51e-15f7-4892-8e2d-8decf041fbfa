package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/cloudwatch"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"go.opentelemetry.io/contrib/instrumentation/github.com/aws/aws-lambda-go/otellambda"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/fn/backfill/env"
	helpers "github.com/drumkitai/drumkit/fn/ingestion"
)

// cloudwatchAPI defines the contract for CloudWatch interactions.
type cloudwatchAPI interface {
	GetMetricData(
		ctx context.Context,
		params *cloudwatch.GetMetricDataInput,
		optFns ...func(*cloudwatch.Options),
	) (*cloudwatch.GetMetricDataOutput, error)
}

// backfiller defines the interface for a provider-specific backfill implementation.
type backfiller interface {
	backfill(ctx context.Context, user models.User, job models.BackfillJobPayload) (bool, error)
}

type throttler interface {
	IsThrottled(ctx context.Context) (bool, error)
}

var errThrottled = errors.New("system is throttled")

// LambdaHandler holds the dependencies for the lambda function.
type LambdaHandler struct {
	throttler          throttler
	getUserByID        func(ctx context.Context, id uint) (models.User, error)
	updateBackfillLock func(ctx context.Context, user *models.User, nt models.NullTime) error
	backfillers        map[string]backfiller
}

func main() {
	if err := sentry.Initialize(); err != nil {
		panic(err)
	}

	ctx := context.Background()

	if err := env.Load(ctx); err != nil {
		panic(err)
	}

	if env.Vars.AppEnv == "dev" {
		log.Debug(ctx, "running in local mode")
		if err := runLocal(ctx); err != nil {
			panic(fmt.Errorf("local run failed: %w", err))
		}
		return
	}

	defer func() {
		if r := recover(); r != nil {
			sentry.GetHubFromContext(ctx).Recover(r)
		}
	}()

	if err := rds.Open(
		ctx,
		env.Vars.EnvConfig,
		rds.WithAppEnv(env.Vars.AppEnv),
		rds.WithAWSSecretsManager(env.Vars.AppEnv == "staging" || env.Vars.AppEnv == "prod"),
		rds.WithApplicationName("drumkit-backfill"),
	); err != nil {
		panic(err)
	}

	if err := redis.Init(ctx, env.Vars.RedisURL); err != nil {
		panic(err)
	}

	cfg, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		panic(fmt.Sprintf("failed to load aws config, %v", err))
	}

	// Real AWS Clients
	sqsClient := sqs.NewFromConfig(cfg)
	cwClient := cloudwatch.NewFromConfig(cfg)
	var s3Uploader s3backup.Archiver
	if env.Vars.S3BucketName != "" {
		s3Uploader, err = s3backup.New(ctx, env.Vars.S3BucketName)
		if err != nil {
			panic(fmt.Sprintf("failed to create s3 uploader, %v", err))
		}
	}

	// Real Database Access Functions
	userRepo := userDB.GetByID
	serviceRepo := rds.GetServiceByID
	emailRepo := emailDB.GetEmailByExternalID
	updateLockFunc := userDB.UpdateBackfillLock

	// Instantiate business logic components
	processorFunctionName := "beacon-processor"
	if env.Vars.AppEnv == "staging" {
		processorFunctionName = "beacon-processor-staging"
	}
	throttlerCfg := SqsThrottlerConfig{
		RDSClusterIdentifier:            env.Vars.RDSClusterIdentifier,
		ProcessorSQSQueueURL:            env.Vars.ProcessorSQSQueueURL,
		ProcessorFunctionName:           processorFunctionName,
		ThrottleRDSMaxConnections:       env.Vars.ThrottleRDSMaxConnections,
		ThrottleProcessorMaxConcurrency: env.Vars.ThrottleProcessorMaxConcurrency,
	}
	throttler := NewSqsThrottler(cwClient, redis.RDB, throttlerCfg)

	// Instantiate Backfiller Implementations
	outlookBf := newOutlookBackfiller(sqsClient, sqsClient, s3Uploader, throttler, serviceRepo, emailRepo)
	gmailBf := newGmailBackfiller(sqsClient, sqsClient, s3Uploader, throttler, serviceRepo, emailRepo)

	// Assemble the final handler with all real dependencies
	h := &LambdaHandler{
		throttler:          throttler,
		getUserByID:        userRepo,
		updateBackfillLock: updateLockFunc,
		backfillers: map[string]backfiller{
			string(models.GmailEmailProvider):   gmailBf,
			string(models.OutlookEmailProvider): outlookBf,
		},
	}

	lambda.Start(otellambda.InstrumentHandler(h.handlerWithSentry))
}

func (h *LambdaHandler) handlerWithSentry(ctx context.Context, sqsEvent events.SQSEvent) (err error) {
	sentry.WithHub(ctx, func(ctx context.Context) {
		defer sentry.GetHubFromContext(ctx).Flush(sentry.FlushTimeout)
		defer log.Flush(ctx)

		ctx = log.NewFromEnv(ctx)

		defer func() {
			if err != nil {
				log.Error(ctx, "returning error", zap.Error(err))
			}
		}()

		err = h.handle(ctx, sqsEvent)
	})
	return
}

func (h *LambdaHandler) handle(ctx context.Context, sqsEvent events.SQSEvent) error {
	for _, message := range sqsEvent.Records {
		ctx := log.With(ctx, zap.String("sqsMessageId", message.MessageId))
		if err := h.processMessage(ctx, message); err != nil {
			if errors.Is(err, errThrottled) {
				log.WarnNoSentry(ctx, "backfill job throttled, will be retried after visibility timeout")
				return err
			}

			log.Error(ctx, "failed to process backfill message", zap.Error(err))
			return err
		}
	}
	return nil
}

// processMessage processes a single SQS message for a backfill job.
// For fatal errors where an SQS retry is unnecessary (e.g., a malformed
// message body), the error is logged, and the function returns nil to
// acknowledge the message and prevent redelivery.
func (h *LambdaHandler) processMessage(ctx context.Context, message events.SQSMessage) error {
	var job models.BackfillJobPayload
	if err := json.Unmarshal([]byte(message.Body), &job); err != nil {
		log.Error(ctx, "failed to unmarshal backfill job", zap.Error(err))
		return nil
	}

	ctx = log.With(ctx, zap.Uint("userId", job.UserID), zap.String("provider", job.Provider))

	throttled, err := h.throttler.IsThrottled(ctx)
	if err != nil {
		return fmt.Errorf("failed to check throttling status: %w", err)
	}
	if throttled {
		log.WarnNoSentry(ctx, "system is throttled, delaying backfill job")
		return errThrottled
	}

	user, err := h.getUserByID(ctx, job.UserID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.Warn(ctx, "user not found for backfill job, discarding")
			return nil
		}
		return fmt.Errorf("failed to get user %d: %w", job.UserID, err)
	}

	if !helpers.ShouldBackfill(ctx, &user) && !job.IsContinuation {
		log.Warn(
			ctx,
			"backfill already in progress for a non-continuation job, discarding",
			zap.Duration("since", time.Since(user.BackfillStartTime.Time)),
			zap.Uint("userId", user.ID),
		)
		return nil
	}

	// Acquire lock for the user's backfill, if it's not a continuation
	// For continuations, the lock is already held from the initial job.
	if !job.IsContinuation {
		if err := h.updateBackfillLock(ctx, &user, models.NullTime{Time: time.Now(), Valid: true}); err != nil {
			return fmt.Errorf("failed to acquire backfill lock for user %d: %w", user.ID, err)
		}
	}

	bf, ok := h.backfillers[strings.ToLower(job.Provider)]
	if !ok {
		log.Warn(ctx, "unknown provider for backfill job", zap.String("provider", job.Provider))
		return nil
	}

	isComplete, err := bf.backfill(ctx, user, job)
	if err != nil {
		// The backfiller implementation should handle retries. If an error makes it
		// this far, it's likely persistent, so we clear the lock to allow a manual
		// re-trigger later if necessary.
		log.Error(ctx, "backfill failed, clearing lock", zap.Error(err))
		if clearErr := h.updateBackfillLock(ctx, &user, models.NullTime{}); clearErr != nil {
			log.Error(ctx, "failed to clear backfill lock after failure", zap.Error(clearErr))
		}
		return err
	}

	if isComplete {
		log.Info(ctx, "backfill completed, clearing lock")
		if err := h.updateBackfillLock(ctx, &user, models.NullTime{}); err != nil {
			return fmt.Errorf("failed to clear backfill lock for user %d: %w", user.ID, err)
		}
	} else {
		log.Info(ctx, "backfill chunk completed, continuation job enqueued")
	}

	return nil
}
