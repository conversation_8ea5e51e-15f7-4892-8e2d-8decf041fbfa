package main

import (
	"context"
	"errors"
	"fmt"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	outlookhelpers "github.com/drumkitai/drumkit/common/outlook"
	"github.com/drumkitai/drumkit/fn/backfill/env"
	helpers "github.com/drumkitai/drumkit/fn/ingestion"
)

// OutlookBackfiller holds the dependencies for the Outlook backfill process.
type OutlookBackfiller struct {
	processorSqs           helpers.SQSAPI
	backfillSqs            helpers.SQSAPI
	s3                     s3backup.Archiver
	throttler              throttler
	newMsClient            func(context.Context, string, string, *models.User, ...oauth.Option) (msclient.Client, error)
	getService             func(context.Context, uint) (models.Service, error)
	getEmail               func(context.Context, string) (*models.Email, error)
	enqueueContinuationJob func(
		ctx context.Context,
		sqs helpers.SQSAPI,
		queueURL string,
		provider string,
		user *models.User,
		opts ...helpers.EnqueueBackfillOption,
	) error
	syncWindow func(
		ctx context.Context,
		client msclient.Client,
		user *models.User,
		service *models.Service,
		startTime time.Time,
		endTime time.Time,
	) error
}

// newOutlookBackfiller creates a new OutlookBackfiller with production dependencies.
func newOutlookBackfiller(
	processorSqs, backfillSqs helpers.SQSAPI,
	s3 s3backup.Archiver,
	throttler throttler,
	getSvc func(context.Context, uint) (models.Service, error),
	getEmail func(context.Context, string) (*models.Email, error),
) *OutlookBackfiller {
	b := &OutlookBackfiller{
		processorSqs:           processorSqs,
		backfillSqs:            backfillSqs,
		s3:                     s3,
		throttler:              throttler,
		newMsClient:            msclient.New[*models.User],
		getService:             getSvc,
		getEmail:               getEmail,
		enqueueContinuationJob: helpers.EnqueueBackfillJob,
	}
	b.syncWindow = b.syncMessagesInWindow
	return b
}

// Backfill performs the backfill operation for Outlook. It satisfies the backfiller interface.
func (b *OutlookBackfiller) backfill(
	ctx context.Context,
	user models.User,
	job models.BackfillJobPayload,
) (bool, error) {
	log.Info(ctx, "starting outlook backfill")
	client, err := b.newMsClient(ctx, env.Vars.MicrosoftClientID, env.Vars.MicrosoftClientSecret, &user)
	if err != nil {
		return false, fmt.Errorf("failed to create outlook client: %w", err)
	}

	service, err := b.getService(ctx, user.ServiceID)
	if err != nil {
		return false, fmt.Errorf("failed to get service for user %d: %w", user.ID, err)
	}

	endTime := time.Now()
	if job.EndTime != nil {
		endTime = *job.EndTime
	}
	startTime := endTime.Add(-time.Duration(env.Vars.DefaultBackfillHours) * time.Hour)
	if job.StartTime != nil {
		startTime = *job.StartTime
	}

	log.Info(ctx, "processing window", zap.String("start", startTime.String()), zap.String("end", endTime.String()))

	const chunkSize = 24 * time.Hour
	const safeExitTime = 30 * time.Second

	// The backfill process works backwards from the end time in discrete chunks.
	// This allows the process to be resumable. If the job fails, is throttled,
	// or times out, it can be re-enqueued with a new time range that picks up
	// where the previous job left off.
	for t := endTime; t.After(startTime); {
		// Before processing a chunk, check if the function is about to time out.
		// If so, enqueue a continuation job for the remaining window and exit.
		deadline, ok := ctx.Deadline()
		if ok {
			remainingTime := time.Until(deadline)
			if remainingTime < safeExitTime {
				log.Info(
					ctx,
					"approaching timeout, creating continuation job",
					zap.Duration("remaining", remainingTime),
				)

				if err := b.enqueueContinuationJob(
					ctx,
					b.backfillSqs,
					env.Vars.BackfillSQSQueueURL,
					job.Provider,
					&user,
					helpers.WithTimeWindow(startTime, t),
					helpers.WithContinuation(),
				); err != nil {
					return false, fmt.Errorf("failed to enqueue continuation job: %w", err)
				}

				log.Info(ctx, "successfully enqueued continuation job")
				return false, nil
			}
		}

		chunkStartTime := t.Add(-chunkSize)
		if chunkStartTime.Before(startTime) {
			chunkStartTime = startTime
		}

		log.Info(ctx, "processing chunk", zap.String("start", chunkStartTime.String()), zap.String("end", t.String()))

		err := b.syncWindow(
			ctx,
			client,
			&user,
			&service,
			chunkStartTime,
			t,
		)
		if err != nil {
			if errors.Is(err, errThrottled) {
				// If we are throttled, re-enqueue a job for the entire remaining window
				// to be processed later and exit.
				log.Info(ctx, "throttled during chunk sync, creating continuation job for remaining window")
				if enqueueErr := b.enqueueContinuationJob(
					ctx,
					b.backfillSqs,
					env.Vars.BackfillSQSQueueURL,
					job.Provider,
					&user,
					helpers.WithTimeWindow(startTime, t),
					helpers.WithContinuation(),
				); enqueueErr != nil {
					return false, fmt.Errorf("failed to enqueue continuation job after being throttled: %w", enqueueErr)
				}
				log.Info(ctx, "successfully enqueued continuation job after throttling")
				// Return false (job not complete) and no error, letting the new job handle the rest.
				return false, nil
			}

			// For any other error, log it and continue to the next chunk.
			log.Error(ctx, "failed to sync window chunk", zap.Error(err))
		}

		t = chunkStartTime
	}

	log.Info(ctx, "finished processing entire window")
	return true, nil
}

// syncMessagesInWindow retrieves message IDs for a given time window, fetches the full
// message details in batches, and enqueues them for processing. It will return
// a special error, errThrottled, if the system is currently throttled.
func (b *OutlookBackfiller) syncMessagesInWindow(
	ctx context.Context,
	client msclient.Client,
	user *models.User,
	service *models.Service,
	startTime time.Time,
	endTime time.Time,
) error {
	msgs, err := client.ListMessagesBetweenDates(ctx, startTime, endTime)
	if err != nil {
		return fmt.Errorf("failed to list messages between dates: %w", err)
	}

	if len(msgs) == 0 {
		return nil
	}

	log.Info(ctx, "found messages for backfill", zap.Int("count", len(msgs)))

	const batchSize = 50
	for i := 0; i < len(msgs); i += batchSize {
		throttled, err := b.throttler.IsThrottled(ctx)
		if err != nil {
			return fmt.Errorf("failed to check for throttling: %w", err)
		}
		if throttled {
			log.WarnNoSentry(ctx, "system is throttled, stopping message sync for this chunk")
			return errThrottled
		}

		end := i + batchSize
		if end > len(msgs) {
			end = len(msgs)
		}
		batch := msgs[i:end]
		for _, msgSummary := range batch {
			if !msclient.ShouldProcessMessage(ctx, msgSummary, user.EmailAddress) {
				continue
			}

			if env.Vars.SkipDuplicates {
				_, err = b.getEmail(ctx, msgSummary.ID)
				if err == nil {
					log.Info(ctx, "skipping duplicate message")
					continue
				} else if !errors.Is(err, gorm.ErrRecordNotFound) {
					log.ErrorNoSentry(ctx, "error querying email, continuing", zap.Error(err))
				}
			}

			if err := outlookhelpers.ProcessAndEnqueueOutlookMessage(
				ctx,
				client,
				b.processorSqs,
				b.s3,
				user,
				service,
				msgSummary,
				env.Vars.ProcessorSQSQueueURL,
			); err != nil {
				// The helper function returns fatal errors, non-fatal ones are just logged.
				log.Error(
					ctx,
					"failed to process and enqueue message",
					zap.Error(err),
					zap.String("msgId", msgSummary.ID),
				)
			}
		}
	}

	return nil
}
