package main

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"
	"os"
	"strings"
	"sync"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/aws/aws-sdk-go-v2/service/sqs/types"
	"go.uber.org/zap"
	"google.golang.org/api/gmail/v1"

	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	gmmock "github.com/drumkitai/drumkit/common/integrations/email/gmailclient/mock"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	msmock "github.com/drumkitai/drumkit/common/integrations/email/msclient/mock"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/fn/backfill/env"
)

// newMockGmailClient creates a new mock Gmail client using the existing common mock.
func newMockGmailClient(
	_ context.Context, _ string, _ string, _ *models.User, _ ...oauth.Option,
) (gmailclient.Client, error) {
	return &gmmock.Service{
		Messages: map[string]*gmail.Message{
			"mock_gmail_msg_1": mockGmailMessage("mock_gmail_msg_1"),
			"mock_gmail_msg_2": mockGmailMessage("mock_gmail_msg_2"),
		},
	}, nil
}

// newMockMsClient creates a new mock Microsoft client using the existing common mock.
func newMockMsClient(
	_ context.Context, _ string, _ string, _ *models.User, _ ...oauth.Option,
) (msclient.Client, error) {
	return &msmock.Client{
		Messages: map[string]msclient.Message{
			"mock_outlook_msg_1": {ID: "mock_outlook_msg_1", Subject: "Mock Outlook Subject 1"},
			"mock_outlook_msg_2": {ID: "mock_outlook_msg_2", Subject: "Mock Outlook Subject 2"},
		},
		Subscriptions: make(map[string]msclient.Subscription),
	}, nil
}

// mockContinuationThrottler is a mock throttler for local development.
// It simulates periodic throttling to allow for continuous testing of backpressure and continuation logic.
// It will throttle on every `throttleOnCall`-th call and then reset its counter.
type mockContinuationThrottler struct {
	mu             sync.Mutex
	calls          int
	throttleOnCall int // if <= 0, never throttles
}

// IsThrottled implements the periodic throttling logic.
func (t *mockContinuationThrottler) IsThrottled(ctx context.Context) (bool, error) {
	t.mu.Lock()
	defer t.mu.Unlock()

	t.calls++
	// Throttle when the call count reaches the configured number.
	if t.throttleOnCall > 0 && t.calls >= t.throttleOnCall {
		log.Warn(ctx, "mockContinuationThrottler: throttling triggered", zap.Int("calls", t.calls))
		t.calls = 0 // Reset for next cycle.
		return true, nil
	}

	log.Info(ctx, "mockContinuationThrottler: not throttled", zap.Int("calls", t.calls))
	return false, nil
}

func mockGmailMessage(id string) *gmail.Message {
	return &gmail.Message{
		Id:       id,
		ThreadId: fmt.Sprintf("mock_thread_%s", id),
		Payload: &gmail.MessagePart{
			MimeType: "multipart/alternative",
			Headers: []*gmail.MessagePartHeader{
				{Name: "Subject", Value: fmt.Sprintf("Mock Subject for %s", id)},
				{Name: "From", Value: `"Mock Sender" <<EMAIL>>`},
				{Name: "To", Value: `"Mock Recipient" <<EMAIL>>`},
				{Name: "Message-ID", Value: fmt.Sprintf("<%<EMAIL>>", id)},
			},
			Parts: []*gmail.MessagePart{
				{
					MimeType: "text/plain",
					Body: &gmail.MessagePartBody{
						Data: base64.URLEncoding.EncodeToString([]byte("This is a mock email body.")),
					},
				},
			},
		},
	}
}

func runLocal(ctx context.Context) error {
	log.Info(ctx, "running in local mode")

	// Dependencies for local run
	if err := rds.Open(ctx, env.Vars.EnvConfig); err != nil {
		return fmt.Errorf("failed to open rds: %w", err)
	}

	if err := redis.Init(ctx, env.Vars.RedisURL); err != nil {
		return fmt.Errorf("failed to init redis: %w", err)
	}

	// Create mock or real clients based on environment
	var (
		newGmailClient func(context.Context, string, string, *models.User, ...oauth.Option) (gmailclient.Client, error)
		newMsClient    func(context.Context, string, string, *models.User, ...oauth.Option) (msclient.Client, error)
	)

	if env.Vars.LiveRun {
		log.Info(ctx, "LIVE_RUN is true, using real Gmail and Outlook clients")
		newGmailClient = gmailclient.New[*models.User]
		newMsClient = msclient.New[*models.User]
	} else {
		log.Info(ctx, "LIVE_RUN is false, using mock clients")
		newGmailClient = newMockGmailClient
		newMsClient = newMockMsClient
	}

	// Use a single, stateful throttler for local development.
	// To test backfill continuation, set throttleOnCall to 3.
	// This simulates:
	// 1. Main handler gatekeeper check -> PASS
	// 2. Backfiller chunk 1 check -> PASS
	// 3. Backfiller chunk 2 check -> FAIL (triggers continuation)
	throttler := &mockContinuationThrottler{throttleOnCall: 3}

	// Dependencies
	cfg, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		return fmt.Errorf("failed to load aws config for localstack: %w", err)
	}

	endpointURL := os.Getenv("SQS_ENDPOINT_URL")
	if endpointURL == "" {
		endpointURL = "http://localhost:4566" // Default for LocalStack
	}

	sqsClient := sqs.NewFromConfig(cfg, func(o *sqs.Options) {
		o.BaseEndpoint = aws.String(endpointURL)
	})

	var s3Uploader s3backup.Archiver
	if env.Vars.S3BucketName != "" {
		s3Uploader, err = s3backup.New(ctx, env.Vars.S3BucketName)
		if err != nil {
			return fmt.Errorf("failed to create s3 uploader: %w", err)
		}
	}
	userRepo := userDB.GetByID
	serviceRepo := rds.GetServiceByID
	emailRepo := emailDB.GetEmailByExternalID
	updateLockFunc := userDB.UpdateBackfillLock

	// Instantiate Backfiller Implementations with potentially mocked clients
	outlookBf := newOutlookBackfiller(sqsClient, sqsClient, s3Uploader, throttler, serviceRepo, emailRepo)
	outlookBf.newMsClient = newMsClient

	gmailBf := newGmailBackfiller(sqsClient, sqsClient, s3Uploader, throttler, serviceRepo, emailRepo)
	gmailBf.newGmailClient = newGmailClient

	// Assemble the final handler with all local dependencies
	h := &LambdaHandler{
		throttler:          throttler,
		getUserByID:        userRepo,
		updateBackfillLock: updateLockFunc,
		backfillers: map[string]backfiller{
			string(models.GmailEmailProvider):   gmailBf,
			string(models.OutlookEmailProvider): outlookBf,
		},
	}

	// Start concurrent pollers for both queues
	var wg sync.WaitGroup
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	errs := make(chan error, 2) // Channel to collect errors from pollers

	processorQueueURL := env.Vars.ProcessorSQSQueueURL
	backfillQueueURL := env.Vars.BackfillSQSQueueURL

	// Launch a poller for the processor queue to simulate the downstream service
	if processorQueueURL == "" {
		log.Warn(ctx, "PROCESSOR_SQS_QUEUE_URL is not set. Output messages will not be consumed locally.")
	} else {
		log.Debug(ctx, "running local processor poller", zap.String("queueURL", processorQueueURL))
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := runLocalProcessorPoller(ctx, sqsClient, processorQueueURL); err != nil {
				log.Error(ctx, "processor poller failed", zap.Error(err))
				errs <- err
			}
		}()
	}

	// Launch the main poller for the backfill jobs
	if backfillQueueURL == "" {
		log.Warn(ctx, "BACKFILL_SQS_QUEUE_URL is not set. Backfill jobs will not be consumed locally.")
	} else {
		log.Debug(ctx, "running local backfill poller", zap.String("queueURL", backfillQueueURL))
		wg.Add(1)
		go func() {
			defer wg.Done()
			if err := runLocalBackfillPoller(ctx, h, sqsClient, backfillQueueURL); err != nil {
				log.Error(ctx, "backfill poller failed", zap.Error(err))
				errs <- err
			}
		}()
	}

	wg.Wait()
	close(errs)

	// Check if any poller returned an error.
	for err := range errs {
		if err != nil {
			return err // Return the first error encountered.
		}
	}

	log.Debug(ctx, "all pollers finished")
	return nil
}

// runLocalBackfillPoller polls the backfill SQS queue and passes messages to the handler.
func runLocalBackfillPoller(ctx context.Context, h *LambdaHandler, sqsClient *sqs.Client, queueURL string) error {
	log.Debug(ctx, "polling for messages from backfill queue", zap.String("queueURL", queueURL))

	for {
		select {
		case <-ctx.Done():
			log.Info(ctx, "backfill poller shutting down")
			return nil
		default:
			// continue
		}

		result, err := sqsClient.ReceiveMessage(ctx, &sqs.ReceiveMessageInput{
			QueueUrl:            aws.String(queueURL),
			MaxNumberOfMessages: 10,
			WaitTimeSeconds:     20, // Use long polling
		})
		if err != nil {
			// If context is cancelled, the error is expected.
			if errors.Is(err, context.Canceled) {
				return nil
			}
			log.Error(ctx, "failed to receive message from local SQS", zap.Error(err))
			return err
		}

		if len(result.Messages) == 0 {
			continue
		}

		log.Info(ctx, "received backfill jobs", zap.Int("count", len(result.Messages)))

		var eventMessages []events.SQSMessage
		for _, sdkMsg := range result.Messages {
			eventMessages = append(eventMessages, events.SQSMessage{
				MessageId:     *sdkMsg.MessageId,
				ReceiptHandle: *sdkMsg.ReceiptHandle,
				Body:          *sdkMsg.Body,
				Attributes:    sdkMsg.Attributes,
			})
		}

		sqsEvent := events.SQSEvent{Records: eventMessages}
		err = h.handle(ctx, sqsEvent)

		if err != nil {
			// Don't exit the poller for throttling errors, just let the message be re-driven.
			if strings.Contains(err.Error(), "system is throttled") {
				log.Warn(ctx, "handler was throttled, message will be retried after visibility timeout", zap.Error(err))
				continue
			}
			log.Error(ctx, "handler failed to process message batch, will not delete", zap.Error(err))
			return err
		}

		log.Info(ctx, "handler processed batch successfully, deleting messages")
		var messagesToDelete []types.DeleteMessageBatchRequestEntry
		for _, msg := range result.Messages {
			messagesToDelete = append(messagesToDelete, types.DeleteMessageBatchRequestEntry{
				Id:            msg.MessageId,
				ReceiptHandle: msg.ReceiptHandle,
			})
		}
		_, err = sqsClient.DeleteMessageBatch(ctx, &sqs.DeleteMessageBatchInput{
			QueueUrl: aws.String(queueURL),
			Entries:  messagesToDelete,
		})
		if err != nil {
			log.Error(ctx, "failed to delete messages from local queue", zap.Error(err))
		}
	}
}

// runLocalProcessorPoller simulates a downstream service that consumes messages
// produced by the backfiller. It polls a given queue, logs the messages, and deletes them.
func runLocalProcessorPoller(ctx context.Context, sqsClient *sqs.Client, queueURL string) error {
	log.Debug(ctx, "polling for messages from processor queue", zap.String("queueURL", queueURL))

	for {
		select {
		case <-ctx.Done():
			log.Info(ctx, "processor poller shutting down")
			return nil
		default:
			// continue
		}

		result, err := sqsClient.ReceiveMessage(ctx, &sqs.ReceiveMessageInput{
			QueueUrl:            aws.String(queueURL),
			MaxNumberOfMessages: 10,
			WaitTimeSeconds:     5,
		})
		if err != nil {
			// If context is cancelled, the error is expected.
			if errors.Is(err, context.Canceled) {
				return nil
			}
			log.Error(ctx, "failed to receive message from local processor SQS", zap.Error(err))
			return err
		}

		if len(result.Messages) == 0 {
			continue
		}

		log.Info(ctx, "local processor consumed messages", zap.Int("count", len(result.Messages)))
		for _, msg := range result.Messages {
			log.Debug(ctx, "processed message body", zap.Stringp("body", msg.Body))
		}

		var messagesToDelete []types.DeleteMessageBatchRequestEntry
		for _, msg := range result.Messages {
			messagesToDelete = append(messagesToDelete, types.DeleteMessageBatchRequestEntry{
				Id:            msg.MessageId,
				ReceiptHandle: msg.ReceiptHandle,
			})
		}
		_, err = sqsClient.DeleteMessageBatch(ctx, &sqs.DeleteMessageBatchInput{
			QueueUrl: aws.String(queueURL),
			Entries:  messagesToDelete,
		})
		if err != nil {
			log.Error(ctx, "failed to delete messages from local processor queue", zap.Error(err))
		}
	}
}
