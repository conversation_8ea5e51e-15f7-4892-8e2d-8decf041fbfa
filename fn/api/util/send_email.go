package apiutil

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sfn"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/fn/api/env"
)

// Send generated emails on behalf of a user by triggering the `sendemail` lambda.
func SendEmail(ctx context.Context, user models.User, generatedEmails []*models.GeneratedEmail) []error {
	var errs []error

	for _, email := range generatedEmails {
		payload := map[string]any{
			"email": map[string]any{
				"generatedEmailID":   email.ID,
				"senderEmailAddress": user.EmailAddress,
				"emailProvider":      user.EmailProvider,
			},
			"triggerTime": email.ScheduleSend.Time,
		}

		payloadBytes, err := json.Marshal(payload)
		if err != nil {
			log.Error(ctx, "error marshaling payload", zap.Error(err))
			errs = append(errs, err)
			continue
		}

		if env.Vars.AppEnv != "dev" {
			err = sendEmailViaSFN(ctx, payloadBytes)
		} else {
			err = sendEmailViaHTTP(ctx, payloadBytes)
		}

		if err != nil {
			errs = append(errs, err)
		}
	}

	return errs
}

func sendEmailViaSFN(ctx context.Context, payloadBytes []byte) error {
	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
	if err != nil {
		log.Error(ctx, "error creating aws config", zap.Error(err))
		return err
	}

	sfnClient := sfn.NewFromConfig(cfg)

	input := &sfn.StartExecutionInput{
		StateMachineArn: aws.String(env.Vars.StateMachineARN),
		Input:           aws.String(string(payloadBytes)),
	}

	_, err = sfnClient.StartExecution(ctx, input)
	if err != nil {
		log.Error(ctx, "error executing step function", zap.Error(err))
		return err
	}

	return nil
}

func sendEmailViaHTTP(ctx context.Context, payloadBytes []byte) error {
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, "http://localhost:5002/email",
		strings.NewReader(string(payloadBytes)))
	if err != nil {
		log.Error(ctx, "error creating local POST request", zap.Error(err))
		return err
	}

	req.Header.Set("Content-Type", "application/json")

	client := otel.TracingHTTPClient()

	resp, err := client.Do(req)
	if err != nil {
		log.Error(ctx, "error sending local POST request", zap.Error(err))
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		log.Error(ctx, "unexpected status code from local POST request", zap.Int("statusCode", resp.StatusCode))
		return fmt.Errorf("unexpected status code: %d", resp.StatusCode)
	}

	return nil
}
