package apiutil

import (
	"context"
	"math"
	"sort"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type sortableRatesAndCarriers struct {
	rates    []float64
	carriers []string
}

func (s sortableRatesAndCarriers) Len() int {
	return len(s.rates)
}

func (s sortableRatesAndCarriers) Less(i, j int) bool {
	return s.rates[i] < s.rates[j]
}

func (s sortableRatesAndCarriers) Swap(i, j int) {
	s.rates[i], s.rates[j] = s.rates[j], s.rates[i]
	s.carriers[i], s.carriers[j] = s.carriers[j], s.carriers[i]
}

// For now we're using the same WeekData struct, where:
//   - MinRate is the 25th percentile for all values
//   - MinCarrier is the carrier that best represents the 25th percentile
//   - TotalRate is the 50th percentile for all values (Median)
//   - MaxRate is the 75th percentile for all values
//   - MaxCarrier is the carrier that best represents the 75th percentile
func CalculateFourWeekDataPercentile[T models.LaneHistoryRawDataAccessor](
	ctx context.Context,
	laneHistory []T,
	quoteLocationsDistance float64,
) map[int]*WeekData {

	if len(laneHistory) == 0 {
		return nil
	}
	latestDate := timeNowFunc().Truncate(24 * time.Hour)
	weekDataMap := make(map[int]*WeekData)

	getWeekIndex := func(date time.Time) int {
		duration := latestDate.Sub(date)
		days := int(duration.Hours() / 24)
		weekIndex := days / 7
		return weekIndex
	}

	for _, item := range laneHistory {
		date, err := item.GetPickupDate()
		if err != nil {
			log.Warn(ctx, "Failed to parse PickupDate", zap.Error(err))
			continue
		}

		weekIndex := getWeekIndex(date)
		if weekIndex >= 4 {
			continue
		}

		carrierCost := item.GetTotalCarrierCost()

		if carrierCost < carrierCostLowThreshold {
			continue
		}

		var totalDistance64 float64
		if itemDistance := item.GetTotalDistance(); itemDistance != 0 {
			totalDistance64 = float64(itemDistance)
		} else {
			log.Info(
				ctx,
				"Skipping lane history item because item distance is zero",
				zap.Any("item", item),
			)
			continue
		}

		// Calculate rate per mile using float64 types
		ratePerMile64 := float64(carrierCost) / totalDistance64

		// Skip if ratePerMile is 0, Inf, or NaN (handles zero cost or potential upstream Inf/NaN)
		if ratePerMile64 == 0 || math.IsInf(ratePerMile64, 0) || math.IsNaN(ratePerMile64) {
			log.Debug(
				ctx,
				"Skipping lane history item due to zero/Inf/NaN rate per mile",
				zap.Float64("ratePerMile", ratePerMile64),
				zap.Any("item", item),
			)
			continue
		}

		wd, exists := weekDataMap[weekIndex]
		if !exists {
			wd = &WeekData{
				Rates:    make([]float64, 0),
				Carriers: make([]string, 0),
				Count:    0,
			}
			weekDataMap[weekIndex] = wd
		}

		wd.Rates = append(wd.Rates, ratePerMile64)
		wd.Carriers = append(wd.Carriers, item.GetCarrierName())
		wd.Count++
	}

	for _, wd := range weekDataMap {
		if wd.Count == 0 {
			continue
		}

		sort.Sort(sortableRatesAndCarriers{
			rates:    wd.Rates,
			carriers: wd.Carriers,
		})

		// Once sorted by per mile rate, calculate relative total cost and filter out low values
		validCount := 0

		for i := 0; i < wd.Count; i++ {
			totalCost := wd.Rates[i] * quoteLocationsDistance

			if totalCost >= carrierCostLowThreshold {
				wd.Rates[validCount] = totalCost
				wd.Carriers[validCount] = wd.Carriers[i]
				validCount++
			}
		}

		if validCount == 0 {
			wd.Rates = []float64{}
			wd.Carriers = []string{}
			wd.Count = 0
			continue
		}

		wd.Rates = wd.Rates[:validCount]
		wd.Carriers = wd.Carriers[:validCount]
		wd.Count = validCount

		if wd.Count == 0 {
			continue
		}

		// Calculate percentile indexes based on converted rates
		p25Index := int(float64(wd.Count-1) * 0.25)
		p50Index := int(float64(wd.Count-1) * 0.50)
		p75Index := int(float64(wd.Count-1) * 0.75)

		wd.MinRate = wd.Rates[p25Index]
		wd.MinCarrier = wd.Carriers[p25Index]
		wd.TotalRate = wd.Rates[p50Index] // Used as median
		wd.MaxRate = wd.Rates[p75Index]
		wd.MaxCarrier = wd.Carriers[p75Index]
	}

	return weekDataMap
}

func OrderWeekDataPercentile(weekDataMap map[int]*WeekData) []WeekResponseItem {
	if weekDataMap == nil {
		return nil
	}

	latestDate := timeNowFunc().Truncate(24 * time.Hour)
	resp := []WeekResponseItem{}

	for weekIndex := 3; weekIndex > -1; weekIndex-- {
		wd, exists := weekDataMap[weekIndex]
		var medianRate, p25Rate, p75Rate float64
		var p25Carrier, p75Carrier string
		var count int

		if exists && wd.Count > 0 {
			medianRate = wd.TotalRate  // Using TotalRate field to store 50th percentile (median)
			p25Rate = wd.MinRate       // Using MinRate field to store 25th percentile
			p75Rate = wd.MaxRate       // Using MaxRate field to store 75th percentile
			p25Carrier = wd.MinCarrier // Using MinCarrier field to store 25th percentile carrier
			p75Carrier = wd.MaxCarrier // Using MaxCarrier field to store 75th percentile carrier
			count = wd.Count
		}

		weekEnd := latestDate.AddDate(0, 0, -7*weekIndex)
		weekStart := weekEnd.AddDate(0, 0, -6)

		res := WeekResponseItem{
			Week:              weekStart.Format("Jan 2") + " - " + weekEnd.Format("Jan 2"),
			Quotes:            count,
			AverageRate:       medianRate, // Storing median in AverageRate field
			LowestRate:        p25Rate,    // Storing 25th percentile in LowestRate field
			MaxRate:           p75Rate,    // Storing 75th percentile in MaxRate field
			LowestCarrierName: p25Carrier, // Storing 25th percentile carrier
			MaxRateCarrier:    p75Carrier, // Storing 75th percentile carrier
		}
		resp = append(resp, res)
	}

	return resp
}

// As values are already have a relative cost to their respective distances, all we have to do is
// add all values from all weeks in an array, sort and calculate final percentiles
func CalculateQuotePercentile(weekDataMap map[int]*WeekData, quoteMileDistance float64) *CalculatedQuote {
	if weekDataMap == nil {
		return nil
	}

	var allQuoteRates []float64
	for _, wd := range weekDataMap {
		allQuoteRates = append(allQuoteRates, wd.Rates...)
	}

	sort.Float64s(allQuoteRates)

	// Calculate percentile indexes
	allQuoteRatesCount := len(allQuoteRates)

	quoteCosts := &CalculatedQuote{
		AvgCost:        0,
		MinCost:        0,
		MaxCost:        0,
		AvgRatePerMile: 0,
		MinRatePerMile: 0,
		MaxRatePerMile: 0,
		AvgDistance:    quoteMileDistance,
		CountQuotes:    allQuoteRatesCount,
	}

	if allQuoteRatesCount > 0 {
		var p25Index, p50Index, p75Index int

		// Handle small datasets where standard percentile calculation breaks
		switch allQuoteRatesCount {
		case 1:
			// Single data point: all percentiles are the same
			p25Index = 0
			p50Index = 0
			p75Index = 0
		case 2:
			// Two data points: use min for p25, min for p50 (avg), max for p75
			p25Index = 0
			p50Index = 0 // we could calc average here instead but lets try this instead
			p75Index = 1
		default:
			// these formulae remain the same for every other case so we're good here.
			p25Index = int(float64(allQuoteRatesCount-1) * 0.25)
			p50Index = int(float64(allQuoteRatesCount-1) * 0.50)
			p75Index = int(float64(allQuoteRatesCount-1) * 0.75)
		}

		quoteCosts.AvgCost = allQuoteRates[p50Index]
		quoteCosts.MinCost = allQuoteRates[p25Index]
		quoteCosts.MaxCost = allQuoteRates[p75Index]

		if quoteMileDistance != 0 {
			quoteCosts.AvgRatePerMile = allQuoteRates[p50Index] / quoteMileDistance
			quoteCosts.MinRatePerMile = allQuoteRates[p25Index] / quoteMileDistance
			quoteCosts.MaxRatePerMile = allQuoteRates[p75Index] / quoteMileDistance
		}
	}

	return quoteCosts
}
