package apiutil

import (
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// HandleDBError returns common/rds query errors with either a 404 or 500 status code.
//
//   - 404 Not Found (with custom msg): if err is gorm.ErrRecordNotFound and userInput=true
//   - 500 Internal Server Error: otherwise
func HandleDBError(c *fiber.Ctx, err error, userInput bool, notFoundMsg string, fmtArgs ...any) error {
	if errors.Is(err, gorm.ErrRecordNotFound) {
		err := fmt.Errorf(notFoundMsg, fmtArgs...)
		if userInput {
			// Send 404 to the user because they provided the query parameters
			log.WarnNoSentry(c.UserContext(), "db record not found", zap.Error(err))

			return c.Status(http.StatusNotFound).SendString(err.Error())
		}

		// The query is based on an internal identifier that should exist: tell Sentry if not
		// Since the gorm callbacks specifically exclude gorm.ErrRecordNotFound, we have to report it explicitly here
		log.Error(c.UserContext(), notFoundMsg, zap.Error(err))
	}

	return c.SendStatus(http.StatusInternalServerError)
}

// Outlook API webhooks replace / with -, and + with _ in the thread ID, but Outlook client does not so we must
// re-map the ID (Gmail IDs are hexadecimal so they're unchanged by this)
func DecodeOutlookID(id string) (string, error) {
	// Outlook threadIDs may contain /'s
	decodedThreadID, err := url.PathUnescape(id)
	if err != nil {
		return "", err
	}

	return strings.ReplaceAll(strings.ReplaceAll(decodedThreadID, "/", "-"), "+", "_"), nil
}

func GetWarehouseCoreFromWarehouse(warehouse models.Warehouse) models.WarehouseCore {
	return models.WarehouseCore{
		WarehouseID:           warehouse.WarehouseID,
		WarehouseName:         warehouse.WarehouseName,
		WarehouseAddressLine1: warehouse.WarehouseAddressLine1,
		WarehouseAddressLine2: warehouse.WarehouseAddressLine2,
		WarehouseTimezone:     warehouse.WarehouseTimezone,
		WarehouseSource:       warehouse.Source,
	}
}
