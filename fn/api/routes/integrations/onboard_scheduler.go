package integrations

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/integrations/scheduling"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	warehouseDB "github.com/drumkitai/drumkit/common/rds/warehouses"
)

type OnboardSchedulerQuery struct {
	OnboardWarehouses bool `json:"onboardWarehouses"`
}

func OnboardScheduler(c *fiber.Ctx) error {
	var query OnboardSchedulerQuery
	var body models.OnboardSchedulerRequest

	if err := api.Parse(c, nil, &query, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	userServiceID := middleware.ServiceIDFromContext(c)

	ctx := log.With(c.UserContext())

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, body.Password, nil)
	if err != nil {
		log.Error(ctx, "Password encryption failed.", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	newIntegration := models.Integration{
		Name:              models.IntegrationName(body.Name),
		Type:              models.Scheduling,
		APIKey:            body.APIKey,
		EncryptedPassword: []byte(encryptedPassword),
		AppID:             body.AppID,
		ServiceID:         userServiceID,
		Username:          body.Username,
		Tenant:            body.Tenant,
		Note:              body.Note,
	}

	client, err := scheduling.New(ctx, newIntegration)
	if err != nil {
		log.Error(ctx, "error creating new integration", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	onBoardResp, err := client.OnboardScheduler(ctx)
	if err != nil && !errors.As(err, &errtypes.NotImplementedError{}) {
		log.Error(ctx, "error onboard integration", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	encryptedPassword, err = crypto.EncryptAESGCM(ctx, body.Password, nil)
	if err != nil {
		log.Error(ctx, "Password encryption failed during onboard", zap.Error(err))
		return c.SendStatus(http.StatusUnprocessableEntity)
	}

	newIntegration.Username = body.Username
	newIntegration.EncryptedPassword = []byte(encryptedPassword)
	newIntegration.AccessToken = onBoardResp.AccessToken
	newIntegration.AccessTokenExpirationDate = models.NullTime{
		Time:  onBoardResp.AccessTokenExpirationDate,
		Valid: true,
	}
	newIntegration.ServiceID = userServiceID
	newIntegration.AppID = body.AppID
	newIntegration.Tenant = body.Tenant
	newIntegration.Note = body.Note

	if err = integrationDB.Create(ctx, &newIntegration); err != nil {
		return c.SendStatus(http.StatusInternalServerError)
	}

	if query.OnboardWarehouses {
		var allWarehouses []models.Warehouse

		allWarehouses, err := client.GetAllWarehouses(ctx)
		if err != nil {
			return fmt.Errorf("unable to fetch all warehouses from scheduler: %w", err)
		}

		if len(allWarehouses) > 0 {
			err = warehouseDB.OnboardWarehouses(ctx, allWarehouses)
			if err != nil {
				return fmt.Errorf("unable to onboard warehouses: %w", err)
			}
		}
	}

	return c.SendStatus(http.StatusCreated)
}
