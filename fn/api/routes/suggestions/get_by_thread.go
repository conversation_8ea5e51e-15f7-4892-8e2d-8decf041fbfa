package suggestions

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
	suggestionDB "github.com/drumkitai/drumkit/common/rds/suggestedloadchanges"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	GetSuggestionsPath struct {
		ThreadID string `params:"threadID" validate:"required"`
		LoadID   uint   `params:"loadID" validate:"required"`
	}

	GetQuickQuoteSuggestionsPath struct {
		ThreadID string `params:"threadID" validate:"required"`
	}

	GetLoadBuildingSuggestionsPath struct {
		ThreadID string `params:"threadID" validate:"required"`
	}

	SuggestedLoadChangeResponse struct {
		ID                      uint                      `json:"id"`
		CreatedAt               time.Time                 `json:"createdAt"`
		Account                 string                    `json:"account"`
		EmailID                 uint                      `json:"emailID"`
		ThreadID                string                    `json:"threadID"`
		LoadID                  *uint                     `json:"loadID"`
		FreightTrackingID       string                    `json:"freightTrackingID"`
		Suggested               any                       `json:"suggested"`
		Applied                 any                       `json:"applied"`
		Status                  models.SuggestionStatus   `json:"status"`
		Pipeline                models.SuggestionPipeline `json:"pipeline"`
		Category                models.SuggestionCategory `json:"category"`
		LatestEmailIDFromThread uint                      `json:"latestEmailIDFromThread"`
	}

	LoadBuildingSuggestionResponse struct {
		ID                      uint                          `json:"id"`
		CreatedAt               time.Time                     `json:"createdAt"`
		Account                 string                        `json:"account"`
		FreightTrackingID       string                        `json:"freightTrackingID"`
		LoadID                  *uint                         `json:"loadID"`
		Status                  models.SuggestionStatus       `json:"status"`
		Pipeline                models.SuggestionPipeline     `json:"pipeline"`
		Category                models.SuggestionCategory     `json:"category"`
		LatestEmailIDFromThread uint                          `json:"latestEmailIDFromThread"`
		Suggested               *models.LoadChanges           `json:"suggested"`
		Attachment              *AttachmentCore               `json:"attachment"`
		Applied                 *models.LoadChanges           `json:"applied"`
		EmailID                 uint                          `json:"emailID"`
		QuoteRequestSuggestion  *QuickQuoteSuggestionResponse `json:"quoteRequestSuggestion"`
	}

	AttachmentCore struct {
		OriginalFileName    string `json:"originalFileName"`
		TransformedFileName string `json:"transformedFileName"`
	}

	SuggestedRequest struct {
		CustomerExternalTMSID string               `json:"customerExternalTMSID"`
		TransportType         models.TransportType `json:"transportType"`
		PickupCity            string               `json:"pickupCity"`
		PickupState           string               `json:"pickupState"`
		PickupZip             string               `json:"pickupZip"`
		PickupDate            *time.Time           `json:"pickupDate"`
		DeliveryCity          string               `json:"deliveryCity"`
		DeliveryState         string               `json:"deliveryState"`
		DeliveryZip           string               `json:"deliveryZip"`
		DeliveryDate          *time.Time           `json:"deliveryDate"`
		FuelSurchargePerMile  float64              `json:"fuelSurchargePerMile"`
		FuelSurchargeTotal    float64              `json:"fuelSurchargeTotal"`
		DistanceMiles         float64              `json:"distanceMiles"`
		Stops                 []models.Stop        `json:"stops"`
	}

	QuickQuoteSuggestionResponse struct {
		ID                      uint                      `json:"id"`
		CreatedAt               time.Time                 `json:"createdAt"`
		Account                 string                    `json:"account"`
		EmailID                 uint                      `json:"emailID"`
		Source                  models.IntegrationName    `json:"source"`
		SourceExternalID        string                    `json:"sourceExternalID"`
		SourceURL               string                    `json:"sourceURL"`
		Suggested               SuggestedRequest          `json:"suggested"`
		Status                  models.SuggestionStatus   `json:"status"`
		Pipeline                models.SuggestionPipeline `json:"pipeline"`
		Category                models.SuggestionCategory `json:"category"`
		Attachment              *AttachmentCore           `json:"attachment"`
		LatestEmailIDFromThread uint                      `json:"latestEmailIDFromThread"`
	}

	GetSuggestionsResponse struct {
		Suggestions []SuggestedLoadChangeResponse `json:"suggestions"`
	}
)

func GetSuggestionsByLoadAndThreadID(c *fiber.Ctx) error {
	var path GetSuggestionsPath

	err := api.Parse(c, &path, nil, nil)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	path.ThreadID, err = apiutil.DecodeOutlookID(path.ThreadID)
	if err != nil {
		log.Error(ctx, "error decoding threadID", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	load, err := loadDB.GetLoadByID(ctx, path.LoadID)
	if err != nil {
		log.WarnNoSentry(ctx, "error fetching load", zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}

	if !perms.HasLoadReadPermissions(c, load) {
		err := fmt.Errorf("not allowed to read load %d", load.ID)
		log.Info(ctx, "forbidden", zap.Error(err))

		return c.Status(http.StatusForbidden).SendString(err.Error())
	}

	// NOTE: We get at most 10 suggestions here.
	suggestions, err := suggestionDB.GetSuggestionsByThreadIDAndLoadID(ctx, path.ThreadID, path.LoadID)
	if err != nil {
		log.WarnNoSentry(ctx, "error fetching changes", zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}

	if len(suggestions) == 0 {
		return c.SendStatus(http.StatusNotFound)
	}

	var suggestedLoadChanges []SuggestedLoadChangeResponse

	for _, suggestion := range suggestions {
		latestEmailID, err := emailDB.GetLatestEmailIDForThread(ctx, suggestion.Email.ThreadID)
		if err != nil {
			log.Warn(ctx, "error fetching latest email ID for thread",
				zap.Error(err), zap.Uint("suggestionID", suggestion.ID))

			continue
		}

		newSuggestedLoadChange := SuggestedLoadChangeResponse{
			ID:                      suggestion.ID,
			CreatedAt:               suggestion.CreatedAt,
			Account:                 suggestion.Account,
			FreightTrackingID:       suggestion.FreightTrackingID,
			Status:                  suggestion.Status,
			Pipeline:                suggestion.Pipeline,
			Category:                suggestion.Category,
			LoadID:                  suggestion.LoadID,
			EmailID:                 suggestion.EmailID,
			ThreadID:                suggestion.ThreadID,
			LatestEmailIDFromThread: latestEmailID,
		}

		if suggestion.Pipeline != models.CheckCallPipeline {
			// TODO: deprecate once users are on the latest Vulcan/Vesta version
			if suggestion.Applied.Changes == nil {
				newSuggestedLoadChange.Applied = models.Changes{}
			} else {
				newSuggestedLoadChange.Applied = suggestion.Applied.Changes
			}

			if suggestion.Suggested.Changes == nil {
				newSuggestedLoadChange.Suggested = models.Changes{}
			} else {
				newSuggestedLoadChange.Suggested = suggestion.Suggested.Changes
			}
		} else {
			newSuggestedLoadChange.Applied = suggestion.Applied
			newSuggestedLoadChange.Suggested = suggestion.Suggested
		}

		suggestedLoadChanges = append(suggestedLoadChanges, newSuggestedLoadChange)
	}

	response := GetSuggestionsResponse{
		Suggestions: suggestedLoadChanges,
	}

	return c.Status(http.StatusOK).JSON(response)
}

// Separate from GetSuggestionsByThreadID because it's used by LB/Quote View on FE
func GetLoadBuildingSuggestionsByThreadID(c *fiber.Ctx) error {
	var path GetLoadBuildingSuggestionsPath

	err := api.Parse(c, &path, nil, nil)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	path.ThreadID, err = apiutil.DecodeOutlookID(path.ThreadID)
	if err != nil {
		log.Error(ctx, "error decoding threadID", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	suggestions, err := suggestionDB.GetLoadBuildingSuggestionsByThreadID(ctx, path.ThreadID)
	if err != nil {
		log.Warn(ctx, "error fetching load building suggestion", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if len(suggestions) == 0 {
		return c.SendStatus(http.StatusNotFound)
	}

	latestEmailID, err := emailDB.GetLatestEmailIDForThread(ctx, path.ThreadID)
	if err != nil {
		log.Warn(ctx, "error fetching latest email ID for thread",
			zap.Error(err), zap.Uint("suggestionID", suggestions[0].ID),
			zap.String("threadID", path.ThreadID))
	}

	var response []LoadBuildingSuggestionResponse
	for _, suggestion := range suggestions {
		var linkedQR *QuickQuoteSuggestionResponse
		if suggestion.QuoteRequestSuggestion != nil {
			res := QuoteRequestModelToResponse(*suggestion.QuoteRequestSuggestion, latestEmailID)
			linkedQR = &res
		}
		resp := LoadBuildingSuggestionResponse{
			ID:                      suggestion.ID,
			CreatedAt:               suggestion.CreatedAt,
			Account:                 suggestion.Account,
			FreightTrackingID:       suggestion.FreightTrackingID,
			Status:                  models.Pending,
			Pipeline:                models.LoadBuildingPipeline,
			Category:                models.LoadBuilding,
			LoadID:                  suggestion.LoadID,
			EmailID:                 suggestion.EmailID,
			LatestEmailIDFromThread: latestEmailID,
			Attachment: &AttachmentCore{
				OriginalFileName:    suggestion.S3Attachment.OriginalFileName,
				TransformedFileName: suggestion.S3Attachment.TransformedFileName},
			Suggested:              suggestion.Suggested.LoadChanges,
			Applied:                suggestion.Applied.LoadChanges,
			QuoteRequestSuggestion: linkedQR,
		}
		response = append(response, resp)
	}

	return c.Status(http.StatusOK).JSON(response)
}

func GetQuickQuoteSuggestionsByThreadID(c *fiber.Ctx) error {
	var path GetQuickQuoteSuggestionsPath

	err := api.Parse(c, &path, nil, nil)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	path.ThreadID, err = apiutil.DecodeOutlookID(path.ThreadID)
	if err != nil {
		log.Error(ctx, "error decoding threadID", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	quotes, err := quoteRequestDB.GetRequestByThreadID(ctx, path.ThreadID)
	if err != nil {
		log.WarnNoSentry(ctx, "error fetching quote request from quote DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if len(quotes) == 0 {
		return c.SendStatus(http.StatusNotFound)
	}

	latestEmailID, err := emailDB.GetLatestEmailIDForThread(ctx, path.ThreadID)
	if err != nil {
		log.Warn(ctx, "error fetching latest email ID for thread",
			zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}

	var response []QuickQuoteSuggestionResponse
	for _, quote := range quotes {

		result := QuoteRequestModelToResponse(quote, latestEmailID)
		response = append(response, result)

	}

	return c.Status(http.StatusOK).JSON(response)
}

func QuoteRequestModelToResponse(
	quote models.QuoteRequest,
	latestEmailID uint,
) (response QuickQuoteSuggestionResponse) {

	// if dates are not valid, don't send to front-end
	var pickupDate, deliveryDate *time.Time
	if quote.SuggestedRequest.PickupDate.Valid {
		pickupDate = &quote.SuggestedRequest.PickupDate.Time
	} else {
		pickupDate = nil
	}

	if quote.SuggestedRequest.DeliveryDate.Valid {
		deliveryDate = &quote.SuggestedRequest.DeliveryDate.Time
	} else {
		deliveryDate = nil
	}

	var stops []models.Stop
	if len(quote.SuggestedRequest.Stops) > 2 {
		stops = quote.SuggestedRequest.Stops
	} else {
		stops = []models.Stop{
			{
				StopType:   "pickup",
				StopNumber: 0,
				Address:    quote.SuggestedRequest.PickupLocation,
				ReadyTime:  quote.SuggestedRequest.PickupDate,
			},
			{
				StopType:    "dropoff",
				StopNumber:  1,
				Address:     quote.SuggestedRequest.DeliveryLocation,
				MustDeliver: quote.SuggestedRequest.DeliveryDate,
			},
		}
	}

	return QuickQuoteSuggestionResponse{
		ID:               quote.ID,
		CreatedAt:        quote.CreatedAt,
		Account:          quote.User.EmailAddress,
		EmailID:          quote.EmailID,
		Source:           quote.Source,
		SourceExternalID: quote.SourceExternalID,
		SourceURL:        quote.SourceURL,
		Attachment: &AttachmentCore{
			OriginalFileName:    quote.Attachment.OriginalFileName,
			TransformedFileName: quote.Attachment.TransformedFileName,
		},
		Suggested: SuggestedRequest{
			CustomerExternalTMSID: quote.SuggestedRequest.Customer.ExternalTMSID,
			PickupCity:            quote.SuggestedRequest.PickupLocation.City,
			PickupState:           quote.SuggestedRequest.PickupLocation.State,
			PickupZip:             quote.SuggestedRequest.PickupLocation.Zip,
			PickupDate:            pickupDate,
			DeliveryCity:          quote.SuggestedRequest.DeliveryLocation.City,
			DeliveryState:         quote.SuggestedRequest.DeliveryLocation.State,
			DeliveryZip:           quote.SuggestedRequest.DeliveryLocation.Zip,
			DeliveryDate:          deliveryDate,
			TransportType:         quote.SuggestedRequest.TransportType,
			FuelSurchargePerMile:  quote.SuggestedRequest.FuelSurchargePerMile,
			FuelSurchargeTotal:    quote.SuggestedRequest.FuelSurchargeTotal,
			DistanceMiles:         quote.SuggestedRequest.Distance,
			Stops:                 stops,
		},
		Status:                  quote.Status,
		Pipeline:                models.QuickQuotePipeline,
		Category:                models.QuickQuoteSuggestion,
		LatestEmailIDFromThread: latestEmailID,
	}
}
