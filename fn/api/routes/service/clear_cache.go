package service

import (
	"context"
	"errors"
	"fmt"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

type ClearServiceCacheRequest struct {
	InternalDrumkitKey string `json:"internalDrumkitKey"`
	ServiceID          uint   `json:"serviceId"`
}

// ClearServiceCache is used to purge Redis cache for a service id/service.
// It expects an internal Drumkit key for authorization and a service ID to attempt to delete the key in Redis.
func ClearServiceCache(c *fiber.Ctx) error {
	ctx := c.UserContext()
	var req ClearServiceCacheRequest

	req, err := validateClearServiceCacheRequest(ctx, c)
	if err != nil {
		return c.Status(fiber.StatusBadRequest).JSON(fiber.Map{
			"error": err.<PERSON>rror(),
		})
	}

	redisKey := fmt.Sprintf("service-%d", req.ServiceID)

	redisErr := redis.DeleteKey(ctx, redisKey)
	if redisErr != nil {
		if errors.Is(redisErr, redis.NilEntry) {
			log.Info(
				ctx,
				"Cache key not found in Redis or already cleared",
				zap.String("redisKey", redisKey),
				zap.Uint("serviceID", req.ServiceID),
			)
			return c.Status(fiber.StatusNotFound).JSON(fiber.Map{
				"message": fmt.Sprintf(
					"Cache key %s not found or already cleared for service ID %d",
					redisKey,
					req.ServiceID,
				),
			})
		}

		log.ErrorNoSentry(
			ctx,
			"Failed to delete service cache from Redis",
			zap.String("redisKey", redisKey),
			zap.Uint("serviceID", req.ServiceID),
			zap.Error(redisErr),
		)
		return c.Status(fiber.StatusInternalServerError).JSON(fiber.Map{
			"error": fmt.Sprintf("Failed to clear cache for service ID %d", req.ServiceID),
		})
	}

	log.Info(
		ctx,
		"Successfully cleared service cache from Redis",
		zap.String("redisKey", redisKey),
		zap.Uint("serviceID", req.ServiceID),
	)

	return c.Status(fiber.StatusOK).JSON(fiber.Map{
		"message": fmt.Sprintf("Cache cleared successfully for service ID %d", req.ServiceID),
	})
}

// validateClearServiceCacheRequest checks for a valid internal Drumkit key and a valid ServiceID.
func validateClearServiceCacheRequest(ctx context.Context, c *fiber.Ctx) (ClearServiceCacheRequest, error) {
	var req ClearServiceCacheRequest

	if err := c.BodyParser(&req); err != nil {
		return req, fmt.Errorf("invalid JSON body: %w", err)
	}

	isValid, err := helpers.ValidateInternalDrumkitKey(ctx, req.InternalDrumkitKey)
	if !isValid {
		return req, fmt.Errorf("invalid internal Drumkit key: %w", err)
	}

	if req.ServiceID == 0 {
		log.Warn(ctx, "Missing ServiceID in clear service cache request")
		return req, errors.New("missing ServiceID in request body")
	}

	return req, nil
}
