package order

import (
	"net/http"

	"github.com/gofiber/fiber/v2"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
	orderdb "github.com/drumkitai/drumkit/common/rds/order"
)

// CreateOrderRequest represents the request body for creating a new order
type CreateOrderRequest struct {
	models.OrderCoreInfo
	RequestedPickupDate   string `json:"requestedPickupDate"`
	RequestedDeliveryDate string `json:"requestedDeliveryDate"`
	Priority              string `json:"priority"`
	ServiceID             uint   `json:"serviceID"`
}

// CreateOrder creates a new order
func CreateOrder(c *fiber.Ctx) error {
	var req CreateOrderRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(http.StatusBadRequest).SendString("Invalid request body")
	}

	// Parse dates
	pickupDate, err := helpers.ParseDatetime(req.RequestedPickupDate)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("Invalid pickup date format")
	}

	deliveryDate, err := helpers.ParseDatetime(req.RequestedDeliveryDate)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("Invalid delivery date format")
	}

	orderObj := models.Order{
		OrderCoreInfo:         req.OrderCoreInfo,
		RequestedPickupDate:   pickupDate,
		RequestedDeliveryDate: deliveryDate,
		Priority:              req.Priority,
		ServiceID:             req.ServiceID,
	}

	if err := orderdb.CreateOrderInDB(c.UserContext(), &orderObj); err != nil {
		return c.Status(http.StatusInternalServerError).SendString("Failed to create order")
	}

	return c.Status(http.StatusCreated).JSON(orderObj)
}
