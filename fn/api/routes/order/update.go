package order

import (
	"net/http"
	"strconv"

	"github.com/gofiber/fiber/v2"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
	orderdb "github.com/drumkitai/drumkit/common/rds/order"
)

// UpdateOrderRequest represents the request body for updating an order
type UpdateOrderRequest struct {
	models.OrderCoreInfo
	RequestedPickupDate   string `json:"requestedPickupDate"`
	RequestedDeliveryDate string `json:"requestedDeliveryDate"`
	Priority              string `json:"priority"`
}

// UpdateOrder updates an existing order
func UpdateOrder(c *fiber.Ctx) error {
	id, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("Invalid order ID format")
	}

	orderObj, err := orderdb.GetOrderFromDB(c.UserContext(), uint(id))
	if err != nil {
		return c.Status(http.StatusNotFound).SendString("Order not found")
	}

	var req UpdateOrderRequest
	if err := c.BodyParser(&req); err != nil {
		return c.Status(http.StatusBadRequest).SendString("Invalid request body")
	}

	// Parse dates
	pickupDate, err := helpers.ParseDatetime(req.RequestedPickupDate)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("Invalid pickup date format")
	}

	deliveryDate, err := helpers.ParseDatetime(req.RequestedDeliveryDate)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("Invalid delivery date format")
	}

	// Update fields
	orderObj.OrderCoreInfo = req.OrderCoreInfo
	orderObj.ExternalOrderID = req.ExternalOrderID
	orderObj.OrderTrackingID = req.OrderTrackingID
	orderObj.PONums = req.PONums
	orderObj.RequestedPickupDate = pickupDate
	orderObj.RequestedDeliveryDate = deliveryDate
	orderObj.Priority = req.Priority

	if err := orderdb.UpdateOrderInDB(c.UserContext(), orderObj); err != nil {
		return c.Status(http.StatusInternalServerError).SendString("Failed to update order")
	}

	return c.JSON(orderObj)
}
