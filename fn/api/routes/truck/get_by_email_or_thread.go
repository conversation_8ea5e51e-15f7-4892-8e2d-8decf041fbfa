package truck

import (
	"errors"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	truckDB "github.com/drumkitai/drumkit/common/rds/truck"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	GetTruckListPath struct {
		ThreadID string `params:"threadID" validate:"required"`
		EmailID  string `params:"emailID" validate:"required"`
	}

	CoreTruckObject struct {
		ID                       uint               `json:"id"`
		PickupLocation           models.CoreAddress `json:"pickupLocation"`
		PickupDate               models.NullTime    `json:"pickupDate"`
		DropoffLocation          models.CoreAddress `json:"dropoffLocation"`
		DropoffDate              models.NullTime    `json:"dropoffDate"`
		Type                     models.TruckType   `json:"type"`
		Length                   float32            `json:"length,omitempty"`
		DropoffIsCarrierDomicile bool               `json:"dropoffIsCarrierDomicile,omitempty"`
	}

	GetTruckListResponse struct {
		ServiceName string                    `json:"serviceName"`
		Carrier     models.CarrierInformation `json:"carrier"`
		Trucks      []CoreTruckObject         `json:"trucks"`
		Errors      models.TruckListErrors    `json:"errors"`
	}
)

func GetByEmailOrThreadID(c *fiber.Ctx) error {
	var path GetTruckListPath

	err := api.Parse(c, &path, nil, nil)
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()
	userServiceID := middleware.ServiceIDFromContext(c)

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	trucklist, err := truckDB.GetTruckListByEmail(ctx, path.EmailID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.SendStatus(http.StatusNotFound)
		}
		log.WarnNoSentry(ctx, "error fetching truck list by email", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if trucklist.ServiceID != service.ID {
		log.Warn(ctx, "unauthorized: truck list from email does not belong to service")
		return c.SendStatus(http.StatusForbidden)
	}

	// if no trucks associated to the email were found, try looking up by thread id
	if len(trucklist.Trucks) == 0 {
		path.ThreadID, err = apiutil.DecodeOutlookID(path.ThreadID)
		if err != nil {
			log.Error(ctx, "error decoding threadID", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		trucklist, err = truckDB.GetTruckListByThreadID(ctx, path.ThreadID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return c.SendStatus(http.StatusNotFound)
			}
			log.WarnNoSentry(ctx, "error fetching truck list by thread id", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	response := GetTruckListResponse{
		ServiceName: service.Name,
		Carrier:     trucklist.Carrier,
		Trucks:      getTruckCoreListFromTruckList(*trucklist),
		Errors:      trucklist.Errors,
	}

	return c.Status(http.StatusOK).JSON(response)
}

func getTruckCoreListFromTruckList(trucklist models.TruckList) []CoreTruckObject {
	var trucks []CoreTruckObject

	for _, truck := range trucklist.Trucks {
		var truckObject CoreTruckObject

		// Applied truck values take precedence over suggestion values, since they match the user's input.
		// Since pickup date is a required field, having an empty applied value means we don't have user
		// input for that field yet e.g. no user submission. In this case, we use the suggestion field.
		if (truck.PickupDate.Applied == models.NullTime{}) {
			truckObject = CoreTruckObject{
				ID: truck.ID,
				PickupLocation: models.CoreAddress{
					City:  truck.PickupLocation.Suggestion.City,
					State: truck.PickupLocation.Suggestion.State,
				},
				PickupDate: truck.PickupDate.Suggestion,
				DropoffLocation: models.CoreAddress{
					City:  truck.DropoffLocation.Suggestion.City,
					State: truck.DropoffLocation.Suggestion.State,
				},
				DropoffDate:              truck.DropoffDate.Suggestion,
				Type:                     truck.Type.Suggestion,
				Length:                   truck.Length.Suggestion,
				DropoffIsCarrierDomicile: truck.DropoffIsCarrierDomicile,
			}
		} else {
			truckObject = CoreTruckObject{
				ID: truck.ID,
				PickupLocation: models.CoreAddress{
					City:  truck.PickupLocation.Applied.City,
					State: truck.PickupLocation.Applied.State,
				},
				PickupDate: truck.PickupDate.Applied,
				DropoffLocation: models.CoreAddress{
					City:  truck.DropoffLocation.Applied.City,
					State: truck.DropoffLocation.Applied.State,
				},
				DropoffDate:              truck.DropoffDate.Applied,
				Type:                     truck.Type.Applied,
				Length:                   truck.Length.Applied,
				DropoffIsCarrierDomicile: truck.DropoffIsCarrierDomicile,
			}
		}

		trucks = append(trucks, truckObject)
	}
	return trucks
}
