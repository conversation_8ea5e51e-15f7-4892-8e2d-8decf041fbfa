package user

import (
	"context"
	"crypto/rsa"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"math/big"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/golang-jwt/jwt/v4"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	LoginMicrosoftBody struct {
		// Microsoft requires that the API URL for Vesta (app ID: dfe5d4cc-66cd-4299-afa7-ed8ab8fb9fb8)
		// matches the URL listed in Azure (vesta.drumkit.ai), or Office.auth.getAccessToken()
		// fails with code 13006.
		//
		// So we use `devEmail`to support dev logins.
		DevEmail    string `json:"devEmail"`
		AccessToken string `json:"accessToken"`
	}

	LoginMicrosoftResponse struct {
		AccessToken     string      `json:"access_token"`
		TokenExpiration int64       `json:"token_expiration"` // In Unix time
		Email           string      `json:"email"`
		Role            models.Role `json:"role"`
		GroupID         uint        `json:"group_id"`
		ServiceID       uint        `json:"service_id"`
		TokenType       string      `json:"token_type"`
	}
)

// LoginMicrosoft handles Microsoft SSO login for Vesta (Drumkit Outlook Add-In).
// Note that unlike LoginGoogle, this endpoint is NOT called by Drumkit Portal; it is strictly for handling SSO
// auth within the add-in.
func LoginMicrosoft(c *fiber.Ctx) error {
	var body LoginMicrosoftBody

	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	var user models.User
	var err error
	var lookupEmail string

	if env.Vars.AppEnv == "dev" {
		// Dev login
		user, err = userDB.GetByEmail(ctx, body.DevEmail)
		lookupEmail = strings.ToLower(body.DevEmail)

	} else {
		// Staging/prod login
		claims, claimsErr := validateSSOToken(ctx, body.AccessToken)
		if claimsErr != nil {
			log.Error(ctx, "validateSSOToken error", zap.Error(claimsErr))
			return c.SendStatus(http.StatusUnauthorized)
		}

		// TODO: Temp, remove log after 5/5/2025
		log.Info(ctx, "validateSSOToken success", zap.Any("claims", claims))
		lookupEmail = strings.ToLower(claims.PreferredUsername)
		user, err = userDB.GetByExternalID(ctx, claims.Oid)
	}

	ctx = log.With(ctx, zap.String("userEmail", lookupEmail))

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(fmt.Sprintf("user %s not found", lookupEmail))
		}

		log.Error(ctx, "userDB error during login", zap.Error(err))

		return c.SendStatus(http.StatusInternalServerError)
	}

	// Re-watch inbox in case user is logging because their account went stale.
	// NOTE: We don't re-watch on-premise users because they don't have Gmail/Outlook tokens; The `WatchInbox`
	// Lambda pings on-premise hosts to re-subscribe to the user's inbox & refresh tokens.
	if !user.IsOnPrem {
		if err := watchOutlookInbox(ctx, &user); err != nil {
			// Fail-open. If initial watch fails, manually subscribe for the user
			log.Error(ctx, "watchOutlookInbox failed", zap.Error(err))
		}
	}

	accessToken, err := CreateDrumkitAccessToken(ctx, user.EmailAddress, user)
	if err != nil {
		log.Error(ctx, "generating drumkit access token failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusOK).JSON(LoginMicrosoftResponse{
		AccessToken:     *accessToken,
		Email:           user.EmailAddress,
		Role:            user.Role,
		ServiceID:       user.ServiceID,
		TokenExpiration: time.Now().Add(30 * 24 * time.Hour).Unix(),
		TokenType:       "Bearer",
	})
}

// Parse, validate, and verify the signature of a Microsoft single-sign on JWT
// https://learn.microsoft.com/en-us/entra/identity-platform/access-tokens
func validateSSOToken(ctx context.Context, tokenStr string) (claims MicrosoftClaims, err error) {
	jwks, err := fetchJWKS(ctx)
	if err != nil {
		return claims, fmt.Errorf("error fetching JWKS: %w", err)
	}

	alg, err := parseMethod(tokenStr)
	if err != nil {
		return claims, fmt.Errorf("error parsing 'alg' from JWT headers: %w", err)
	}

	keyFunc := func(token *jwt.Token) (any, error) {
		// Find the appropriate key in JWKS based on the token's key ID (kid)
		kid := token.Header["kid"].(string)
		claims, ok := token.Claims.(*MicrosoftClaims)
		if !ok {
			return nil, errors.New("token.Claims not of type MicrosoftClaims")
		}

		var publicKey *rsa.PublicKey

		for _, key := range jwks.Keys {
			// use the issuer property of the keys document to restrict the scope of keys
			// https://shorturl.at/CLMX9
			keyIss := strings.ReplaceAll(key.Issuer, "{tenantid}", claims.TenantID)
			if kid == key.Kid && keyIss == claims.Issuer {
				// Construct the RSA public key
				publicKey, err = parsePublicKey(key)
				if err != nil {
					return nil, err
				}
				break
			}
		}
		if publicKey == nil {
			return nil, errors.New("unable to find appropriate key")
		}
		return publicKey, nil
	}

	// Parse the token
	token, err := jwt.ParseWithClaims(tokenStr, &claims, keyFunc, jwt.WithValidMethods([]string{alg}))
	if err != nil {
		return claims, fmt.Errorf("error parsing token: %w", err)
	}

	if !token.Valid {
		return claims, fmt.Errorf("token is invalid: %w", err)
	}

	return claims, nil
}

// Fetch Microsoft JWT keys
func fetchJWKS(ctx context.Context) (*JWKS, error) {
	jwksURL := "https://login.microsoftonline.com/common/discovery/v2.0/keys"

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, jwksURL, nil)
	if err != nil {
		return nil, fmt.Errorf("error building JWKS request: %w", err)
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var jwks JWKS
	err = json.Unmarshal(body, &jwks)
	if err != nil {
		return nil, err
	}

	return &jwks, nil
}

func parsePublicKey(key JSONWebKeys) (*rsa.PublicKey, error) {
	nb, err := base64.RawURLEncoding.DecodeString(key.N)
	if err != nil {
		return nil, err
	}

	eb, err := base64.RawURLEncoding.DecodeString(key.E)
	if err != nil {
		return nil, err
	}

	return &rsa.PublicKey{
		N: new(big.Int).SetBytes(nb),
		E: int(new(big.Int).SetBytes(eb).Int64()),
	}, nil
}

func parseMethod(tokenStr string) (string, error) {
	parts := strings.Split(tokenStr, ".")
	if s := len(parts); s != 3 {
		return "", fmt.Errorf("invalid token segments: %d", s)
	}

	headerJSON, err := base64.RawURLEncoding.DecodeString(parts[0])
	if err != nil {
		return "", fmt.Errorf("error decoding header: %w", err)
	}

	var headerMap map[string]any
	if err := json.Unmarshal(headerJSON, &headerMap); err != nil {
		return "", fmt.Errorf("error parsing header JSON: %w", err)
	}

	alg, ok := headerMap["alg"].(string)
	if !ok {
		return "", errors.New("signing algorithm not found in token header")
	}

	return alg, nil
}
