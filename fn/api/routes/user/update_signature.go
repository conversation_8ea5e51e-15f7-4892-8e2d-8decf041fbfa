package user

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/multierr"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/fn/api/env"
)

type (
	UpdateUserSignatureBody struct {
		EmailSignatureHTML            *string         `json:"emailSignatureHTML"`
		EmailSignatureQuillDelta      json.RawMessage `json:"emailSignatureQuillDelta"`
		UseSignatureOnNewEmails       bool            `json:"useSignatureOnNewEmails"`
		UseSignatureOnRepliesForwards bool            `json:"useSignatureOnRepliesForwards"`
	}

	UpdateUserSignatureResponse struct {
		UserID                   uint            `json:"userID"`
		EmailSignatureHTML       string          `json:"emailSignature"`
		EmailSignatureQuillDelta json.RawMessage `json:"emailSignatureQuillDelta"`
		Message                  string          `json:"message"`
	}
)

func UpdateUserSignature(c *fiber.Ctx) error {
	var body UpdateUserSignatureBody

	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := c.UserContext()

	email := middleware.ClaimsFromContext(c).Email
	user, err := rds.GetUserByEmail(ctx, email)
	if err != nil {
		log.Error(ctx, "user query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if body.EmailSignatureHTML != nil {
		if err := updateUserSignature(ctx, &user, body); err != nil {
			log.Error(ctx, "error updating user signature", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}
	}

	return c.Status(http.StatusOK).JSON(UpdateUserSignatureResponse{
		UserID:                   user.ID,
		EmailSignatureHTML:       user.EmailSignature,
		EmailSignatureQuillDelta: user.EmailSignatureQuillDelta,
		Message:                  "User updated successfully",
	})
}

func updateUserSignature(ctx context.Context, user *models.User, body UpdateUserSignatureBody) error {
	signature := body.EmailSignatureHTML
	if signature == nil || *signature == "" {
		return nil
	}

	err := processImages(ctx, *user, signature, &body.EmailSignatureQuillDelta)
	if err != nil {
		return err
	}

	user.EmailSignature = *body.EmailSignatureHTML
	user.EmailSignatureQuillDelta = body.EmailSignatureQuillDelta
	user.UseSignatureOnNewEmails = body.UseSignatureOnNewEmails
	user.UseSignatureOnRepliesForwards = body.UseSignatureOnRepliesForwards

	return rds.UpdateSignatureSettings(ctx, user)
}

// processImages uploads inline images to S3 and replaces base64 URLs with S3 URLs in HTML and Quill Delta.
// This is done so mail clients don't strip inline images when rendering the user's signature.
func processImages(ctx context.Context, user models.User, html *string, quillDelta *json.RawMessage) error {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(*html))
	if err != nil {
		return err
	}

	// Quill Editor sends empty string as <body><p><br /></p></body> (DeltaJSON = {"ops":[{"insert":"\n"}]} )
	// when there is no content so handle that case here.
	if strings.TrimSpace(doc.Text()) == "" {
		*html = ""
		*quillDelta = nil

		return nil
	}

	s3Client, err := s3backup.New(ctx, s3backup.DrumkitPublicBucket)
	if err != nil {
		return fmt.Errorf("error creating S3 client: %w", err)
	}

	var errs error
	doc.Find("img").Each(func(i int, img *goquery.Selection) {
		err := func() (err error) {
			src, exists := img.Attr("src")
			if !exists || !strings.HasPrefix(src, "data:image") {
				return // Skip if no src or already an external URL
			}

			// Extract base64 image data (format: data:image/png;base64,iVBORw0KGgoAAAANSUhEUgA...)
			base64Data := strings.SplitN(src, ",", 2)
			if len(base64Data) < 2 {
				return
			}

			imageBytes, err := base64.StdEncoding.DecodeString(base64Data[1])
			if err != nil {
				return fmt.Errorf("error decoding base64 image %d: %w", i, err)
			}

			fileExt, err := getImageFileType(base64Data[0])
			if err != nil {
				return fmt.Errorf("error getting image %d file type: %w", i, err)
			}

			s3URL, err := s3Client.PublicUserAsset(ctx, user, env.Vars.AppEnv, "signature-images", fileExt, imageBytes)
			if err != nil {
				return fmt.Errorf("error uploading image %d to S3: %w", i, err)
			}

			// Replace src with S3 URL in HTML and Quill Delta
			img.SetAttr("src", s3URL)
			*quillDelta = bytes.ReplaceAll([]byte(*quillDelta), []byte(src), []byte(s3URL))

			return nil
		}()

		if err != nil {
			errs = multierr.Append(errs, err)
		}
	})

	if errs != nil {
		return fmt.Errorf("failed to process images in HTML signature: %w", errs)
	}

	// Get modified HTML
	finalHTML, err := doc.Html()
	if err != nil {
		return err
	}

	*html = finalHTML
	return nil
}

func getImageFileType(dataString string) (string, error) {
	re := regexp.MustCompile(`^data:image/([a-zA-Z0-9]+);base64.*`)
	matches := re.FindStringSubmatch(dataString)

	if len(matches) < 2 {
		return "", errors.New("not a valid image data string")
	}

	return matches[1], nil
}
