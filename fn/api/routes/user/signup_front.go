package user

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/common/services"
)

type (
	SignupFrontBody struct {
		Email   string `json:"email"`
		Name    string `json:"name"`
		FrontID string `json:"front_id"`
	}

	SignupFrontResponse struct {
		AccessToken     string `json:"access_token"`
		TokenExpiration int64  `json:"token_expiration"` // In Unix time
		Email           string `json:"email"`
		GroupID         uint   `json:"group_id"`
		ServiceID       uint   `json:"service_id"`
		TokenType       string `json:"token_type"`
	}
)

// Since Front doesn't require user-specific permissions, we don't need user passwords or tokens
func SignupFront(c *fiber.Ctx) error {
	var body SignupFrontBody

	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.String("email", body.Email))

	log.Info(ctx, "received front signup request")

	userEmailDomain := fmt.Sprintf("@%s", strings.ToLower(strings.Split(body.Email, "@")[1]))

	// Match user domain to a service
	service, err := rds.GetFrontServiceByDomain(ctx, userEmailDomain)
	if err != nil {
		// TODO: Should we enable service creation here for new users?
		// if errors.Is(err, gorm.ErrRecordNotFound) {
		// 	if err := assignOrCreateService(ctx, &user); err != nil {
		// 		return c.SendStatus(http.StatusInternalServerError)
		// 	}
		// }
		return c.Status(http.StatusInternalServerError).SendString("Front service not found")
	}

	ctx = log.With(ctx, zap.Uint("serviceID", service.ID))

	user := models.User{
		EmailAddress:  body.Email,
		EmailProvider: models.FrontEmailProvider,
		Name:          body.Name,
		FrontID:       body.FrontID,
		ServiceID:     service.ID,
	}

	userFound, err := userDB.GetByEmail(ctx, user.EmailAddress)
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return c.SendStatus(http.StatusInternalServerError)
	}

	user.ID = userFound.ID
	if userFound.ID == 0 {
		err = userDB.Create(ctx, &user)
	} else {
		err = userDB.Update(ctx, user)
	}

	if err != nil {
		log.Error(ctx, "error creating/updating user in DB", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	// Send internal onboarding callback for visibility
	if userFound.ID == 0 {
		go sentry.WithHub(ctx, func(ctx context.Context) {
			if err = services.PostInternalOnboardingCallback(ctx, &user, &service, true); err != nil {
				log.Warn(ctx, "error sending onboarding callback", zap.Error(err))
			}
		})
	}

	accessToken, err := CreateDrumkitAccessToken(ctx, user.EmailAddress, user)
	if err != nil {
		log.Error(ctx, "generating drumkit access token failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	return c.Status(http.StatusCreated).JSON(SignupGoogleResponse{
		AccessToken:     *accessToken,
		Email:           user.EmailAddress,
		ServiceID:       user.ServiceID,
		TokenExpiration: time.Now().Add(30 * 24 * time.Hour).Unix(),
		TokenType:       "Bearer",
	})
}
