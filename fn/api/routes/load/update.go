package load

import (
	"context"
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/integrations/tms/aljex"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

type (
	UpdateLoadPath struct {
		LoadID uint `params:"loadID" validate:"required"`
	}

	UpdateLoadBody struct {
		Load models.Load
	}

	UpdateLoadResponse struct {
		Load           models.Load           `json:"load"`
		LoadAttributes models.LoadAttributes `json:"loadAttributes"`
		Message        string                `json:"message,omitempty"`
	}
)

func UpdateLoad(c *fiber.Ctx) error {
	var path UpdateLoadPath
	var body UpdateLoadBody
	var existingLoad models.Load

	if err := api.Parse(c, &path, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	if err := api.Parse(c, nil, nil, &body.Load); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}
	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	existingLoad, err := loadDB.GetLoadByID(ctx, path.LoadID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("load with ID %d not found", path.LoadID))
		}

		log.Error(ctx, "loadDB query error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	switch existingLoad.TMS.Name {
	case models.Aljex:
		return updateAljexLoad(ctx, c, body.Load, existingLoad, existingLoad.TMS)

	default:
		return updateLoadInTMS(ctx, c, body.Load, existingLoad, existingLoad.TMS)
	}
}

func updateAljexLoad(
	ctx context.Context,
	c *fiber.Ctx,
	reqLoad,
	existingLoad models.Load,
	tmsIntegration models.Integration,
) error {
	var updatedLoad models.Load

	aljexClient, err := aljex.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating Aljex client", zap.Error(err))

		return c.Status(http.StatusServiceUnavailable).SendString("error connecting to TMS")
	}

	//nolint:contextcheck // fiber ctx passed in instead
	if !perms.HasLoadReadWritePermissions(c, existingLoad, aljexClient) {
		err := fmt.Errorf("not allowed to write to load %d", existingLoad.ID)
		log.Info(ctx, "forbidden", zap.Error(err))

		return c.Status(http.StatusForbidden).SendString(err.Error())
	}

	refreshedExistingLoad, _, err := aljexClient.GetLoad(ctx, existingLoad.FreightTrackingID)
	if err == nil {
		existingLoad = refreshedExistingLoad
	}

	if refreshedExistingLoad.Diff(reqLoad).IsEmpty() {
		return c.Status(http.StatusOK).JSON(UpdateLoadResponse{
			Message: "No changes detected, TMS not updated",
		})
	}

	reqLoad.FreightTrackingID = existingLoad.FreightTrackingID
	reqLoad.ServiceID = existingLoad.ServiceID

	var updatedAttrs models.LoadAttributes
	if updatedLoad, updatedAttrs, err = callAljexFunc(ctx, &reqLoad, &existingLoad, aljexClient); err != nil {
		log.Error(ctx, "error updating TMS", zap.Error(err))

		return c.Status(http.StatusServiceUnavailable).SendString("error while updating TMS")
	}

	if err = loadDB.UpsertLoad(ctx, &updatedLoad, &tmsIntegration); err != nil {
		log.Error(ctx, "error updating load in DB", zap.Error(err))
	}

	// Check if there are differences between the Aljex load before and after the update on the website.
	// There should be a diff IFF an *unsuccessful* update was made.
	diff := updatedLoad.Diff(reqLoad)

	if !diff.IsEmpty() {
		errMsg := diff.BuildErrMsg()

		response := UpdateLoadResponse{
			Load:           updatedLoad,
			LoadAttributes: updatedAttrs,
			Message:        errMsg,
		}

		log.Error(ctx,
			"aljex update didn't raise errors, but there's a mismatch between updates "+
				"requested and aljex load after update",
			zap.String("diff error message", errMsg),
			zap.Any("requested updates on load", reqLoad),
			zap.Any("updated load", updatedLoad),
		)

		return c.Status(http.StatusUnprocessableEntity).JSON(response)
	}

	response := UpdateLoadResponse{
		Load:           updatedLoad,
		LoadAttributes: updatedAttrs,
		Message:        "Updating load succeeded",
	}

	return c.Status(http.StatusOK).JSON(response)
}

func callAljexFunc(
	ctx context.Context,
	body, existingLoad *models.Load,
	aljexClient *aljex.Aljex,
) (models.Load, models.LoadAttributes, error) {

	var updatedLoad models.Load
	var updatedAttrs models.LoadAttributes
	var err error

	ld := existingLoad.Diff(*body)
	operatorChangeType := models.DetectTMSOperatorChange(existingLoad.Operator, body.Operator)

	// Perform Operator updates first so that operator is notified
	switch operatorChangeType {
	case models.TMSOperatorAssignment:
		updatedLoad, updatedAttrs, err = aljexClient.AssignOperator(
			ctx, body.FreightTrackingID, body.Operator)

	case models.TMSOperatorUpdate:
		updatedLoad, updatedAttrs, err = aljexClient.UpdateOperator(
			ctx, body.FreightTrackingID, body.Operator)
	}
	if err != nil {
		return updatedLoad, updatedAttrs, err
	}

	performLoadUpdate := len(ld.BillTo) > 0 || len(ld.Carrier) > 0 || len(ld.Consignee) > 0 ||
		len(ld.Customer) > 0 || len(ld.Pickup) > 0

	if performLoadUpdate || operatorChangeType == models.TMSOperatorRemoval {
		updatedLoad, updatedAttrs, err = aljexClient.UpdateLoad(
			ctx,
			existingLoad,
			body,
		)
	}

	return updatedLoad, updatedAttrs, err
}

func updateLoadInTMS(
	ctx context.Context,
	c *fiber.Ctx,
	body, existingLoad models.Load,
	tmsIntegration models.Integration,
) error {
	var updatedLoad models.Load

	client, err := tms.New(ctx, tmsIntegration)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))

		return c.Status(http.StatusServiceUnavailable).SendString("error connecting to TMS")
	}
	//nolint:contextcheck // fiber ctx passed in instead
	if !perms.HasLoadReadWritePermissions(c, existingLoad, client) {
		err := fmt.Errorf("not allowed to write to load %d", existingLoad.ID)
		log.Info(ctx, "forbidden", zap.Error(err))

		return c.Status(http.StatusForbidden).SendString(err.Error())
	}

	body.ExternalTMSID = existingLoad.ExternalTMSID
	updatedLoad, attrs, err := client.UpdateLoad(ctx, &existingLoad, &body)
	if err != nil {
		var ufErr errtypes.UserFacingError
		isUfErr := errors.As(err, &ufErr)

		// TODO: Filter out user-facing errors from Sentry after observation period
		log.Error(ctx, "error updating TMS", zap.Error(err))
		if isUfErr {
			return c.Status(http.StatusBadRequest).JSON(UpdateLoadResponse{
				Message: ufErr.Error(),
			})
		}

		return c.Status(http.StatusServiceUnavailable).SendString("error while updating TMS")
	}

	if err = loadDB.UpsertLoad(ctx, &updatedLoad, &tmsIntegration); err != nil {
		log.Error(ctx, "error updating load in DB", zap.Error(err))
	}

	// Mcleod automatically updates dispatcher, phone, email, name, and dot number when carrier updated.
	// It may also reset carrier rate data.
	// To avoid false positive LoadDiff errors, we manually set these fields in the body if the
	// carrier update succeeded
	if tmsIntegration.Name == models.McleodEnterprise &&
		updatedLoad.Carrier.ExternalTMSID == body.Carrier.ExternalTMSID {

		body.Carrier.Name = updatedLoad.Carrier.Name
		body.Carrier.DOTNumber = updatedLoad.Carrier.DOTNumber
		body.Carrier.Dispatcher = updatedLoad.Carrier.Dispatcher
		body.Carrier.Phone = updatedLoad.Carrier.Phone
		body.Carrier.Email = updatedLoad.Carrier.Email

		body.RateData.CarrierRateType = updatedLoad.RateData.CarrierRateType
		body.RateData.CarrierLineHaulCharge = updatedLoad.RateData.CarrierLineHaulCharge
		body.RateData.CarrierRateNumUnits = updatedLoad.RateData.CarrierRateNumUnits
		body.RateData.CarrierLineHaulRate = updatedLoad.RateData.CarrierLineHaulRate
		body.RateData.CarrierLineHaulUnit = updatedLoad.RateData.CarrierLineHaulUnit
		body.RateData.CarrierCost = updatedLoad.RateData.CarrierCost
		body.RateData.CarrierCostCurrency = updatedLoad.RateData.CarrierCostCurrency
	}

	// Check if there are differences between the load before and after the update.
	// There should be a diff IFF an *unsuccessful* update was made.
	diff := updatedLoad.Diff(body)

	if !diff.IsEmpty() {
		errMsg := diff.BuildErrMsg()

		response := UpdateLoadResponse{
			Load:           updatedLoad,
			LoadAttributes: attrs,
			Message:        errMsg,
		}

		log.Error(
			ctx,
			string(existingLoad.TMS.Name)+" update didn't raise errors, but there's a mismatch between updates "+
				"requested and load after update",
			zap.String("diff error message", errMsg),
			zap.Any("requested updates on load", body),
			zap.Any("updated load", updatedLoad),
		)

		return c.Status(http.StatusUnprocessableEntity).JSON(response)
	}

	response := UpdateLoadResponse{
		Load:           updatedLoad,
		LoadAttributes: attrs,
		Message:        "Updating load succeeded",
	}

	return c.Status(http.StatusOK).JSON(response)
}
