package load

import (
	"net/http"

	"github.com/gofiber/fiber/v2"
	"github.com/lib/pq"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	"github.com/drumkitai/drumkit/common/rds/emailtemplates"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	CarrierTemplatePath struct {
		LoadID uint `params:"loadID" validate:"required"`
	}

	PreparedTemplate struct {
		TemplateType models.EmailTemplateType `json:"templateType"`
		CC           pq.StringArray           `json:"cc"`
		Subject      string                   `json:"subject"`
		Body         string                   `json:"body"`
		IsGeneric    bool                     `json:"isGeneric"`
	}

	CarrierTemplateResponse struct {
		DispatchTemplate     PreparedTemplate `json:"dispatchTemplate"`
		PickupTemplate       PreparedTemplate `json:"pickupTemplate"`
		AfterPickupTemplate  PreparedTemplate `json:"afterPickupTemplate"`
		InTransitTemplate    PreparedTemplate `json:"inTransitTemplate"`
		DropoffTemplate      PreparedTemplate `json:"dropoffTemplate"`
		AfterDropoffTemplate PreparedTemplate `json:"afterDropoffTemplate"`
	}
)

func GetCarrierTemplates(c *fiber.Ctx) error {
	userServiceID := middleware.ServiceIDFromContext(c)
	ctx := c.UserContext()

	var path CarrierTemplatePath

	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	user, err := userDB.GetByEmail(ctx, middleware.ClaimsFromContext(c).Email)
	if err != nil {
		return apiutil.HandleDBError(c, err, false, "user %s not found", middleware.ClaimsFromContext(c).Email)
	}

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "could not get service", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	ctx = log.With(ctx,
		zap.Uint("userID", user.ID),
		zap.Uint("serviceID", service.ID),
		zap.String("serviceName", service.Name),
		zap.String("emailAddress", user.EmailAddress),
		zap.String("emailProvider", string(user.EmailProvider)),
		zap.Uint("loadID", path.LoadID),
	)

	templateTypes := []models.EmailTemplateType{
		models.TrackAndTraceCarrierDispatch,
		models.TrackAndTraceCarrierPickup,
		models.TrackAndTraceCarrierAfterPickup,
		models.TrackAndTraceCarrierInTransit,
		models.TrackAndTraceCarrierDropoff,
		models.TrackAndTraceCarrierAfterDropoff,
	}

	templates, err := emailtemplates.GetEmailTemplatesByTypesAndServiceID(ctx, service.ID, templateTypes)
	if err != nil {
		log.Error(ctx, "failed to get email templates", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	load, err := loadDB.GetLoadByID(ctx, path.LoadID)
	if err != nil {
		log.Error(ctx, "failed to get load", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	response := CarrierTemplateResponse{}

	prepareTemplate := func(templateType models.EmailTemplateType, template models.EmailTemplate) PreparedTemplate {
		subject, body, err := models.PrepareEmailTemplate(templateType, load)
		if err != nil {
			log.Error(ctx, "failed to prepare email template", zap.Error(err))
			return PreparedTemplate{
				TemplateType: templateType,
				Subject:      template.Subject,
				Body:         template.Body,
				IsGeneric:    false,
			}
		}

		cc := pq.StringArray{""}
		if len(user.CC) > 0 {
			cc = user.CC
		}

		return PreparedTemplate{
			TemplateType: templateType,
			CC:           cc,
			Subject:      subject,
			Body:         body,
			IsGeneric:    template.Subject == "" && template.Body == "",
		}
	}

	getTemplateOrGeneric := func(templateType models.EmailTemplateType) PreparedTemplate {
		for _, t := range *templates {
			if t.TemplateType == templateType {
				return prepareTemplate(templateType, t)
			}
		}

		genericTemplate := models.GenericEmailTemplates[templateType]
		return prepareTemplate(templateType, genericTemplate)
	}

	response.DispatchTemplate = getTemplateOrGeneric(models.TrackAndTraceCarrierDispatch)
	response.PickupTemplate = getTemplateOrGeneric(models.TrackAndTraceCarrierPickup)
	response.AfterPickupTemplate = getTemplateOrGeneric(models.TrackAndTraceCarrierAfterPickup)
	response.InTransitTemplate = getTemplateOrGeneric(models.TrackAndTraceCarrierInTransit)
	response.DropoffTemplate = getTemplateOrGeneric(models.TrackAndTraceCarrierDropoff)
	response.AfterDropoffTemplate = getTemplateOrGeneric(models.TrackAndTraceCarrierAfterDropoff)

	return c.Status(http.StatusOK).JSON(response)
}
