package load

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	exceptionsDB "github.com/drumkitai/drumkit/common/rds/exceptions"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

type (
	GetExceptionPath struct {
		LoadID uint `params:"loadID" validate:"required"`
	}

	GetExceptionHistory = []models.Exception
)

func GetExceptionsHistory(c *fiber.Ctx) error {
	var path GetCheckCallPath

	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}
	ctx := c.UserContext()

	load, err := loadDB.GetLoadByID(ctx, path.LoadID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("load %d not found", path.LoadID))
		}

		log.Error(ctx, "get exceptions loadDB failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	if !perms.HasLoadReadPermissions(c, load) {
		err := fmt.Errorf("not allowed to read load %d", load.ID)
		return c.Status(http.StatusForbidden).SendString(err.Error())
	}

	client, err := tms.New(ctx, load.TMS)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))

		return c.Status(http.StatusServiceUnavailable).SendString("error connecting to TMS")
	}

	history, err := client.GetExceptionHistory(ctx, load.ID, load.FreightTrackingID)
	if err != nil {
		log.Error(ctx, "TMS error getting exception history", zap.Error(err))

		return c.Status(http.StatusServiceUnavailable).SendString("error getting exceptions from TMS")
	}

	if err = exceptionsDB.BatchUpsertExceptions(ctx, history); err != nil {
		// Fail-open
		log.ErrorNoSentry(ctx, "error upserting exceptions",
			zap.Error(err),
			zap.String("freight_tracking_id", load.FreightTrackingID))
	}

	return c.Status(http.StatusOK).JSON(history)
}
