package load

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/helpers/perms"
	"github.com/drumkitai/drumkit/common/integrations/tms"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

type (
	PostNotePath struct {
		LoadID uint `params:"loadID" validate:"required"`
	}

	PostNoteBodyV1 struct {
		Note string `json:"note" validate:"required"`
	}

	PostNoteBody = models.Note
)

func PostNote(c *fiber.Ctx) error {
	var path PostNotePath
	var body PostNoteBody

	if err := api.Parse(c, &path, nil, &body); err != nil {
		// TODO For backwards-compatibility. Deprecate after v.19.1
		var v1Body PostNoteBodyV1
		if err := api.Parse(c, &path, nil, &v1Body); err != nil {
			return c.Status(http.StatusBadRequest).SendString(err.Error())
		}
		body.Note = v1Body.Note
	}
	ctx := log.With(c.UserContext(), zap.Uint("loadID", path.LoadID))

	load, err := loadDB.GetLoadByID(ctx, path.LoadID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("load %d not found", path.LoadID))
		}

		log.Error(ctx, "get loadDB failed", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	client, err := tms.New(ctx, load.TMS)
	if err != nil {
		log.Error(ctx, "error creating TMS client", zap.Error(err))

		return c.Status(http.StatusServiceUnavailable).SendString("error connecting to TMS")
	}

	if !perms.HasLoadReadWritePermissions(c, load, client) {
		err := fmt.Errorf("not allowed to read load %d", load.ID)
		log.Info(ctx, "forbidden", zap.Error(err))
		return c.Status(http.StatusForbidden).SendString(err.Error())
	}

	var notes []models.Note
	if notes, err = client.PostNote(ctx, &load, body); err != nil {
		log.Error(ctx, "error POSTing note", zap.Error(err))

		return c.Status(http.StatusServiceUnavailable).SendString("error submitting exception to TMS")
	}

	return c.Status(http.StatusCreated).JSON(notes)
}
