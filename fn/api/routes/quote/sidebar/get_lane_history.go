package quoteprivate

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gofiber/fiber/v2"
	"github.com/hardfinhq/go-date"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"golang.org/x/sync/errgroup"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/pricing/globaltranz"
	"github.com/drumkitai/drumkit/common/integrations/pricing/greenscreens"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/fn/api/env"
	quoteCommon "github.com/drumkitai/drumkit/fn/api/routes/quote/common"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

var createQuoteRecord = quoteCommon.CreateQuoteRecord

type (
	// LaneTier represents the geographical granularity of the lane history lookup (e.g. City-to-City, State-to-State)
	LaneTier string

	LaneHistoryRequestBody struct {
		QuoteRequestID     uint                 `json:"quoteRequestId"`
		OriginCity         string               `json:"originCity"`
		OriginState        string               `json:"originState"`
		OriginZip          string               `json:"originZip"`
		OriginCountry      QuoteCountryName     `json:"originCountry"`
		DestinationCity    string               `json:"destinationCity"`
		DestinationState   string               `json:"destinationState"`
		DestinationZip     string               `json:"destinationZip"`
		DestinationCountry QuoteCountryName     `json:"destinationCountry"`
		TransportType      models.TransportType `json:"transportType"`
	}

	LaneHistoryResponse struct {
		// Determines how many graphs to generate on FE
		CountUniqueSources int `json:"countUniqueSources"`
		//nolint:lll
		UniqueSources   []models.IntegrationName                   `json:"uniqueSources"` // e.g. [Greenscreens, tms.Name]
		ResultsBySource map[models.IntegrationName][]SourceHistory `json:"resultsBySource"`
	}

	SourceHistory struct {
		Source                models.IntegrationName     `json:"source"` // e.g. Greenscreens, tms.Name
		LaneTier              LaneTier                   `json:"laneTier"`
		Timeframe             string                     `json:"timeframe"` // e.g. 28-day
		CalculatedQuote       *apiutil.CalculatedQuote   `json:"calculatedQuote"`
		InputtedTransportType models.TransportType       `json:"inputtedTransportType"`
		ProxiedTransportType  models.TransportType       `json:"proxiedTransportType"`
		Weeks                 []apiutil.WeekResponseItem `json:"weeks"`
		IsPercentile          bool                       `json:"isPercentile"`
	}
)

const (
	NumLookbackDays      = 28
	lowDistanceThreshold = 100.0

	_3DigitZipLaneTier = "3-Digit-Zip"
	CityLaneTier       = "City-to-City"
	MarketLaneTier     = "Market-to-Market" // Not supported by Mcleod rn
	StateLaneTier      = "State-to-State"
)

func GetLaneHistory(c *fiber.Ctx) error {
	var body LaneHistoryRequestBody
	var err error
	userServiceID := middleware.ServiceIDFromContext(c)
	email := middleware.ClaimsFromContext(c).Email

	ctx := log.With(c.UserContext(), zap.Uint("serviceID", userServiceID))

	if err := api.Parse(c, nil, nil, &body); err != nil {
		log.Error(ctx, "failed to parse request body", zap.Error(err))
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}
	log.Debug(ctx, "received lane history request", zap.Any("request", body))

	service, err := rds.GetServiceByID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "failed to get service by ID", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	user, err := rds.GetUserByEmail(c.UserContext(), email)
	if err != nil {
		log.Error(ctx, "failed to get user by email", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	searchFrom := time.Now().Add(-NumLookbackDays * 24 * time.Hour)
	searchTo := time.Now()

	// Populate city/state/zip from user input to enabled tiered lane lookups;
	// if error, fail-open and perform only inputted history lookup
	if err := validateLocation(
		ctx,
		&body.OriginCity,
		&body.OriginState,
		&body.OriginZip,
		&body.OriginCountry,
	); err != nil {
		log.Warn(ctx, "error validating pickup", zap.Error(err))
	}

	if err := validateLocation(
		ctx,
		&body.DestinationCity,
		&body.DestinationState,
		&body.DestinationZip,
		&body.DestinationCountry,
	); err != nil {
		log.Warn(ctx, "error validating dropoff", zap.Error(err))
	}

	log.Info(
		ctx,
		"validated pickup/dropoff locations",
		zap.String("originCity", body.OriginCity),
		zap.String("originState", body.OriginState),
		zap.String("originZip", body.OriginZip),
		zap.String("originCountry", string(body.OriginCountry)),
		zap.String("destCity", body.DestinationCity),
		zap.String("destState", body.DestinationState),
		zap.String("destZip", body.DestinationZip),
		zap.String("destCountry", string(body.DestinationCountry)),
	)

	var e errgroup.Group
	var gsData *SourceHistory
	var globalTranzData *SourceHistory
	var tmsResults []SourceHistory

	if !service.IsQuoteLaneHistoryEnabled {
		log.Info(ctx, "Quote lane history not enabled for service", zap.Uint("serviceID", userServiceID))
	} else {
		pricingIntegrations, err := integrationDB.GetPricingByServiceID(ctx, userServiceID)
		if err != nil {
			log.Error(ctx, "error fetching pricing integration", zap.Error(err))
			return c.SendStatus(http.StatusInternalServerError)
		}

		for _, integration := range pricingIntegrations {
			switch integration.Name {
			case "greenscreens":
				e.Go(func() error {
					gsData, err = getHistoryFromGreenscreens(ctx, userServiceID, body, searchFrom, searchTo)
					if err != nil {
						log.Error(ctx, "error getting lane history from Greenscreens", zap.Error(err))
						return err
					}
					return nil
				})
			case "globaltranz":
				e.Go(func() error {
					globalTranzData, err = getHistoryFromGlobalTranz(ctx, user, service, body, searchFrom, searchTo)
					if err != nil {
						log.Error(ctx, "error getting lane history from GlobalTranz", zap.Error(err))
						return err
					}
					return nil
				})
			}
		}
	}

	if !service.IsTMSLaneHistoryEnabled {
		log.Info(ctx, "TMS lane history not enabled for service", zap.Uint("serviceID", userServiceID))
	} else {
		tmsIntegration, err := getFirstAvailableTMSIntegration(ctx, userServiceID)
		if err == nil && tmsIntegration.ID != 0 {
			e.Go(func() error {
				tmsResults, err = getHistoryFromDB(
					ctx,
					userServiceID,
					body,
					searchFrom,
					searchTo,
					tmsIntegration.Name,
				)
				if err != nil {
					log.Error(
						ctx,
						"error getting lane history from TMS",
						zap.Error(err),
						zap.Time("searchFrom", searchFrom),
						zap.Time("searchTo", searchTo),
					)
					return err
				}
				return nil
			})
		}
	}

	err = e.Wait()
	if err != nil {
		log.Error(ctx, "error waiting for lane history lookups to complete", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	var results = make(map[models.IntegrationName][]SourceHistory)
	var uniqueSources []models.IntegrationName

	if gsData != nil && gsData.Weeks != nil && hasValidWeekData(gsData.Weeks) {
		results[models.Greenscreens] = []SourceHistory{*gsData}
		uniqueSources = append(uniqueSources, models.Greenscreens)
	}

	if globalTranzData != nil && globalTranzData.Weeks != nil && hasValidWeekData(globalTranzData.Weeks) {
		results[models.GlobalTranz] = []SourceHistory{*globalTranzData}
		uniqueSources = append(uniqueSources, models.GlobalTranz)
	}

	if len(tmsResults) > 0 {
		validTMSResults := processTMSResultsAndCreateQuotes(ctx, service, user, body, tmsResults)

		if len(validTMSResults) > 0 {
			// Original logic to add TMS results to the response for display
			actualTmsName := validTMSResults[0].Source
			if actualTmsName != "" {
				results[actualTmsName] = validTMSResults
				// Add the source name to uniqueSources only if it's not already present
				isNewSource := true
				for _, existingSource := range uniqueSources {
					if existingSource == actualTmsName {
						isNewSource = false
						break
					}
				}
				if isNewSource {
					uniqueSources = append(uniqueSources, actualTmsName)
				}
			}
		}
	}

	log.Debug(
		ctx,
		"completed lane history lookup",
		zap.Int("uniqueSourcesCount", len(uniqueSources)),
		zap.Any("uniqueSources", uniqueSources),
	)

	return c.Status(http.StatusOK).JSON(LaneHistoryResponse{
		CountUniqueSources: len(uniqueSources),
		UniqueSources:      uniqueSources,
		ResultsBySource:    results,
	})
}

func processTMSResultsAndCreateQuotes(
	ctx context.Context,
	service models.Service,
	user models.User,
	body LaneHistoryRequestBody,
	tmsResults []SourceHistory,
) []SourceHistory {
	var validTMSResults []SourceHistory
	for _, result := range tmsResults {
		if result.Weeks != nil && hasValidWeekData(result.Weeks) {
			validTMSResults = append(validTMSResults, result)
		}
	}

	if len(validTMSResults) == 0 {
		return nil
	}

	actualTmsName := validTMSResults[0].Source
	var currentTypeInSource models.QuoteTypeInSource

	switch actualTmsName {
	case models.Mcleod, models.McleodEnterprise:
		currentTypeInSource = models.McleodEnterpriseQuoteTypeInSource
	case models.Turvo:
		currentTypeInSource = models.TurvoQuoteTypeInSource
	default:
		log.Warn(ctx, "Unknown TMS source for quote generation",
			zap.String("tmsName", string(actualTmsName)))
	}

	// Construct stops once as they are the same for all results from the same 'body'
	stops := []models.Stop{
		{
			StopNumber: 0,
			Address: models.Address{
				City:    body.OriginCity,
				State:   body.OriginState,
				Zip:     body.OriginZip,
				Country: string(body.OriginCountry),
			},
		},
		{
			StopNumber: 1,
			Address: models.Address{
				City:    body.DestinationCity,
				State:   body.DestinationState,
				Zip:     body.DestinationZip,
				Country: string(body.DestinationCountry),
			},
		},
	}

	for _, tmsResult := range validTMSResults {
		if tmsResult.CalculatedQuote == nil {
			log.Warn(ctx, "Skipping TMS quote generation due to nil CalculatedQuote",
				zap.String("tmsName", string(actualTmsName)),
				zap.String("laneTier", string(tmsResult.LaneTier)))
			continue
		}

		rateData := quoteCommon.RateData{
			// ExternalID:      "", // N/A
			TargetBuyRate: tmsResult.CalculatedQuote.AvgRatePerMile,
			LowBuyRate:    tmsResult.CalculatedQuote.MinRatePerMile,
			HighBuyRate:   tmsResult.CalculatedQuote.MaxRatePerMile,
			// StartBuyRate:    0, // N/A
			// FuelRate:        0, // N/A
			Distance: tmsResult.CalculatedQuote.AvgDistance,
			// ConfidenceLevel: 0, // N/A
		}

		// Track TMS name AND lane tier for each quote record (i.e. turvo-city-to-city)
		typeInSource := strings.ToLower(string(currentTypeInSource) + "-" + string(tmsResult.LaneTier))
		formattedTypeInSource := models.QuoteTypeInSource(typeInSource)

		// emailID, threadID are 0/"" as this is system-generated.
		// pickupDate, deliveryDate are N/A
		quoteRecord := createQuoteRecord(
			ctx,
			service,
			user.ID,
			0,  // emailID
			"", // threadID
			body.QuoteRequestID,
			stops,
			body.TransportType,
			time.Time{}, // pickupDate
			time.Time{}, // deliveryDate
			rateData,
			tmsResult.CalculatedQuote.AvgCost, // targetSellCost (total)
			tmsResult.CalculatedQuote.MinCost, // minTargetSellCost (total)
			tmsResult.CalculatedQuote.MaxCost, // maxTargetSellCost (total)
			models.TmsLaneHistorySource,
			formattedTypeInSource,
			nil,
		)
		if quoteRecord == nil {
			// TODO: The CreateQuoteRecord function should return an error if it fails to create a quote record
			// instead of having to handle nils without knowing the error
			span := trace.SpanFromContext(ctx)
			span.RecordError(
				fmt.Errorf("failed to create tms lane history quote record for source %s, tier %s",
					actualTmsName, tmsResult.LaneTier),
			)
		}
	}
	return validTMSResults
}

func getHistoryFromGreenscreens(
	ctx context.Context,
	userServiceID uint,
	body LaneHistoryRequestBody,
	searchFrom, searchTo time.Time,
) (_ *SourceHistory, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "getHistoryFromGreenscreens", nil)
	defer func() { metaSpan.End(err) }()

	gsTransportTypeInput := mapTransportType(body.TransportType)
	integration, err := integrationDB.GetPricingByServiceID(ctx, userServiceID)
	if err != nil {
		log.Error(ctx, "error fetching pricing integration", zap.Error(err))
		return nil, fmt.Errorf("error fetching Greenscreens integration: %w", err)
	}

	var greenscreensIntegration models.Integration
	for _, i := range integration {
		if i.Name == models.Greenscreens {
			greenscreensIntegration = i
			break
		}
	}

	if greenscreensIntegration.ID == 0 {
		return nil, errors.New("no Greenscreens integration found for service")
	}

	var client greenscreens.API
	client, err = greenscreens.CachedClient(ctx, greenscreensIntegration)
	if err != nil {
		return nil, fmt.Errorf("error creating Greenscreens client: %w", err)
	}

	laneHistoryBody := greenscreens.GetLaneHistoryRequest{
		Currency:           "USD",
		DateFrom:           searchFrom.Format("2006-01-02"), // 28 days ago
		DateTo:             searchTo.Format("2006-01-02"),   // today
		OriginZip:          body.OriginZip,
		OriginCity:         body.OriginCity, // zip code is always required regardless of region
		OriginState:        body.OriginState,
		OriginCountry:      string(body.OriginCountry),
		DestinationCity:    body.DestinationCity,
		DestinationState:   body.DestinationState,
		DestinationZip:     body.DestinationZip, // zip code is always required regardless of region
		DestinationCountry: string(body.DestinationCountry),
		// region could be KMA, CITY, or 3DZIP. We use CITY because it best reflects the shipping lane
		// Future consideration: also support multiple region/tier lookups?
		Region:        "CITY",
		ExtraStops:    []greenscreens.ExtraStop{},
		TransportType: gsTransportTypeInput,
	}

	laneHistory, err := client.GetLaneHistory(ctx, &laneHistoryBody)
	if err != nil {
		log.Error(ctx, "GetLaneHistory failed", zap.Error(err))
		return nil, fmt.Errorf("error getting lane history: %w", err)
	}

	if len(laneHistory.Items) == 0 {
		log.WarnNoSentry(ctx, "No lane history items found for Greenscreens")

		// if no items, return an empty response
		return nil, nil
	}

	weekData := apiutil.CalculateFourWeekData(ctx, laneHistory.Items, nil)
	orderedData := apiutil.OrderWeekData(weekData, nil)

	return &SourceHistory{
		Source:                models.Greenscreens,
		LaneTier:              CityLaneTier,
		Timeframe:             fmt.Sprintf("%d-day", NumLookbackDays),
		ProxiedTransportType:  models.TransportType(gsTransportTypeInput),
		InputtedTransportType: body.TransportType,
		Weeks:                 orderedData,
		IsPercentile:          false,
	}, nil
}

func getHistoryFromGlobalTranz(
	ctx context.Context,
	user models.User,
	service models.Service,
	body LaneHistoryRequestBody,
	searchFrom, searchTo time.Time,
) (_ *SourceHistory, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "getHistoryFromGlobalTranz", nil)
	defer func() { metaSpan.End(err) }()

	globalTranzTransportTypeInput := mapTransportType(body.TransportType)
	integration, err := integrationDB.GetPricingByServiceID(ctx, user.ServiceID)
	if err != nil {
		log.Error(ctx, "error fetching pricing integration", zap.Error(err))
		return nil, fmt.Errorf("error fetching GlobalTranz integration: %w", err)
	}

	var globalTranzIntegration models.Integration
	for _, i := range integration {
		if i.Name == models.GlobalTranz {
			globalTranzIntegration = i
			break
		}
	}

	if globalTranzIntegration.ID == 0 {
		return nil, errors.New("no GlobalTranz integration found for service")
	}

	laneHistoryBody := globaltranz.GetLaneHistoryRequest{
		DateFrom:         searchFrom.Format("2006-01-02"), // 28 days ago
		DateTo:           searchTo.Format("2006-01-02"),   // today
		DestinationCity:  body.DestinationCity,
		DestinationState: body.DestinationState,
		DestinationZip:   body.DestinationZip,
		OriginZip:        body.OriginZip,
		OriginCity:       body.OriginCity,
		OriginState:      body.OriginState,
		TransportType:    globalTranzTransportTypeInput,
	}

	var laneHistory *globaltranz.GetLaneHistoryResponse

	cachedLaneHistory, err := globaltranz.GetRedisLaneHistory(ctx, &laneHistoryBody)
	if err == nil {
		log.Info(
			ctx,
			"found cached GlobalTranz lane history in Redis",
			zap.String("originState", body.OriginState),
			zap.String("destinationState", body.DestinationState),
			zap.String("transportType", globalTranzTransportTypeInput),
		)

		laneHistory = cachedLaneHistory
	} else {

		client, _, err := globaltranz.New(ctx, globalTranzIntegration)
		if err != nil {
			return nil, fmt.Errorf("error creating GlobalTranz client: %w", err)
		}

		laneHistory, err = client.GetLaneHistory(ctx, &laneHistoryBody)
		if err != nil {
			log.Error(ctx, "GetLaneHistory failed", zap.Error(err))
			return nil, fmt.Errorf("error getting lane history: %w", err)
		}
	}

	if len(laneHistory.Items) == 0 {
		log.WarnNoSentry(ctx, "No lane history items found for GlobalTranz")

		// if no items, return an empty response
		return nil, nil
	}

	quoteMileDistance, err := quoteCommon.GetRedisMileDistanceBetweenLocationsWithFallback(
		ctx,
		body.OriginCity,
		body.OriginState,
		body.DestinationCity,
		body.DestinationState,
	)
	if err != nil {
		strErr := "failed to get mile distance between locations or fallback to Greenscreens"
		log.Error(ctx, strErr, zap.Error(err))
		return nil, fmt.Errorf("%s: %w", strErr, err)
	}

	weekData := apiutil.CalculateFourWeekDataPercentile(ctx, laneHistory.Items, quoteMileDistance)
	orderedData := apiutil.OrderWeekDataPercentile(weekData)
	calculatedQuote := apiutil.CalculateQuotePercentile(weekData, quoteMileDistance)

	// GlobalTranz is an exception where we create a quote through lane history
	// instead of Quick Quote - hence why we create a quote record here
	rateData := quoteCommon.RateData{
		TargetBuyRate: calculatedQuote.AvgCost,
		LowBuyRate:    calculatedQuote.MinCost,
		HighBuyRate:   calculatedQuote.MaxCost,
		Distance:      calculatedQuote.AvgDistance,
	}

	globaltranz.CreateQuoteRecord(
		ctx,
		service,
		user,
		body.QuoteRequestID,
		body.TransportType,
		rateData,
		[]models.Stop{
			{
				Order: 0,
				Address: models.Address{
					Country: "US",
					State:   body.OriginState,
					City:    body.OriginCity,
					Zip:     body.OriginZip,
				},
			},
			{
				Order: 1,
				Address: models.Address{
					Country: "US",
					State:   body.DestinationState,
					City:    body.DestinationCity,
					Zip:     body.DestinationZip,
				},
			},
		},
	)

	return &SourceHistory{
		Source:                models.GlobalTranz,
		LaneTier:              CityLaneTier,
		Timeframe:             fmt.Sprintf("%d-day", NumLookbackDays),
		ProxiedTransportType:  models.TransportType(globalTranzTransportTypeInput),
		CalculatedQuote:       calculatedQuote,
		InputtedTransportType: body.TransportType,
		Weeks:                 orderedData,
		IsPercentile:          true,
	}, nil
}

func populateQueryForTier(query *models.SearchLoadsQuery, tier LaneTier, body LaneHistoryRequestBody) error {
	switch tier {

	case _3DigitZipLaneTier:
		if len(body.OriginZip) == 5 && len(body.DestinationZip) == 5 {
			query.Pickup.Zip = body.OriginZip[:3]
			query.Dropoff.Zip = body.DestinationZip[:3]
			return nil
		}
		return errors.New("insufficient zips provided for 3-digit zip tier")

	case CityLaneTier:
		if !helpers.IsBlank(body.OriginCity) && !helpers.IsBlank(body.OriginState) &&
			!helpers.IsBlank(body.DestinationCity) && !helpers.IsBlank(body.DestinationState) {
			query.Pickup.City = body.OriginCity
			query.Pickup.State = body.OriginState
			query.Dropoff.City = body.DestinationCity
			query.Dropoff.State = body.DestinationState
			return nil
		}
		return errors.New("insufficient city/state provided for city/state tier")

	case StateLaneTier:
		if !helpers.IsBlank(body.OriginState) && !helpers.IsBlank(body.DestinationState) {
			query.Pickup.State = body.OriginState
			query.Dropoff.State = body.DestinationState
			return nil
		}
		return errors.New("insufficient state provided for state tier")
	}

	return fmt.Errorf("unknown tier type: %s", tier)
}

var (
	lookupZipFunc         = helpers.LookupZipCodeByCityState
	lookupCityStateFunc   = helpers.LookupCityStateByZipcode
	lookupAWSLocationFunc = helpers.AwsLocationLookup
)

// User can input either zipcode or city, state. validateLocation maps city/state to zip or zip to city/state
// to ensure all inputs are populated.
func validateLocation(ctx context.Context, city, state, zip *string, country *QuoteCountryName) error {
	if city == nil || state == nil || zip == nil {
		return errors.New("city, state, and zip must not be nil")
	}

	locationCountry := country
	// If country is not provided, default to US
	if locationCountry == nil {
		defaultCountry := QuoteCountryUS
		locationCountry = &defaultCountry
	}

	if helpers.IsBlank(*city) || helpers.IsBlank(*state) {
		if *locationCountry == QuoteCountryCanada {

			newCity, newProvince, err := getCanadaLocationByPostalCode(ctx, zip)
			if err != nil {
				return fmt.Errorf("error mapping canada postal code to city/state: %w", err)
			}

			*city = newCity
			*state = newProvince

			return nil
		}

		// Lookup city/state by zip
		cityState, err := lookupCityStateFunc(ctx, *zip, env.Vars.USPSUserID)
		if err != nil {
			return fmt.Errorf("error mapping zip to city/state: %w", err)
		}

		*city = cityState.ZipCode.City
		*state = cityState.ZipCode.State

		return nil
	}

	if helpers.IsBlank(*zip) {

		// Lookup zip by city/state
		zipResult, err := lookupZipFunc(ctx, *city, *state)
		if err != nil {
			log.Warn(ctx, "error mapping city/state to zip", zap.Error(err))
			return fmt.Errorf("error mapping city/state to zip: %w", err)
		}

		if len(zipResult) < 3 {
			return fmt.Errorf("no valid zips found for city/state: %s, %s", *city, *state)
		}

		*zip = zipResult
	}

	return nil
}

// Get the first available TMS integration (McleodEnterprise or Turvo)
func getFirstAvailableTMSIntegration(ctx context.Context, userServiceID uint) (models.Integration, error) {
	globalTranzIntegration, errGlobalTranz := integrationDB.GetByName(ctx, userServiceID, models.GlobalTranzTMS)
	if errGlobalTranz == nil && globalTranzIntegration.ID != 0 {
		return globalTranzIntegration, nil
	}

	mcleodIntegration, errMcleod := integrationDB.GetByName(ctx, userServiceID, models.McleodEnterprise)
	if errMcleod == nil && mcleodIntegration.ID != 0 {
		return mcleodIntegration, nil
	}
	turvoIntegration, errTurvo := integrationDB.GetByName(ctx, userServiceID, models.Turvo)
	if errTurvo == nil && turvoIntegration.ID != 0 {
		return turvoIntegration, nil
	}
	// If neither found, return zero value and error
	return models.Integration{}, fmt.Errorf("no TMS integration found for service %d", userServiceID)
}

// Get lane history data from the loads table
func getHistoryFromDB(
	ctx context.Context,
	userServiceID uint,
	body LaneHistoryRequestBody,
	searchFrom, searchTo time.Time,
	tmsIntegrationName models.IntegrationName,
) (_ []SourceHistory, err error) {
	log.Info(
		ctx,
		"Getting history from loads\n",
		zap.String("tmsIntegrationName", string(tmsIntegrationName)),
		zap.String("transportType", string(body.TransportType)),
	)
	ctx, metaSpan := otel.StartSpan(ctx, "getHistoryFromLoads", nil)
	defer func() { metaSpan.End(err) }()

	// Fetch quote mile distance for percentile calculations
	quoteMileDistance, err := quoteCommon.GetRedisMileDistanceBetweenLocationsWithFallback(
		ctx,
		body.OriginCity,
		body.OriginState,
		body.DestinationCity,
		body.DestinationState,
	)
	if err != nil {
		strErr := "failed to get mile distance between locations or fallback for TMS history"
		log.Error(ctx, strErr, zap.Error(err))
		// Return nil, nil to allow other sources to potentially succeed
		return nil, nil
	}

	tiers := []LaneTier{_3DigitZipLaneTier, CityLaneTier, StateLaneTier}
	resultsChannel := make(chan *SourceHistory, len(tiers))
	errChan := make(chan error, len(tiers))
	var wg sync.WaitGroup

	// Lookup each tier concurrently
	for _, t := range tiers {
		tier := t // Create new variable to avoid closure problems
		wg.Add(1)
		go func() {
			defer wg.Done()
			result, err := searchLoadsByTierFromRDS(
				ctx,
				tier,
				body,
				userServiceID,
				searchFrom,
				searchTo,
				quoteMileDistance,
				tmsIntegrationName,
			)
			if err != nil {
				errChan <- fmt.Errorf("tier %s search failed: %w", tier, err)
				return
			}
			resultsChannel <- result
		}()
	}

	// Close channels after all goroutines complete
	go func() {
		wg.Wait()
		close(resultsChannel)
		close(errChan)
	}()

	var results []SourceHistory
	var errors []error

	for err := range errChan {
		errors = append(errors, err)
	}

	for result := range resultsChannel {
		if result != nil {
			results = append(results, *result)
		}
	}

	if len(errors) == len(tiers) {
		return nil, fmt.Errorf("all tier lookups failed: %v", errors)
	}

	if len(errors) > 0 {
		log.WarnNoSentry(ctx, "some tier lookups failed", zap.Errors("errors", errors))
	}

	return results, nil
}

func searchLoadsByTierFromRDS(
	ctx context.Context,
	tier LaneTier,
	body LaneHistoryRequestBody,
	userServiceID uint,
	searchFrom, searchTo time.Time,
	quoteMileDistance float64,
	tmsIntegrationName models.IntegrationName,
) (*SourceHistory, error) {
	log.Info(
		ctx,
		"starting tier search",
		zap.String("tier", string(tier)),
		zap.Time("searchFrom", searchFrom),
		zap.Time("searchTo", searchTo),
	)

	var loads []models.Load
	db := rds.WithContextReader(ctx)

	// Convert time.Time to UTC first, then to date.Date for proper date-only comparison
	fromDate := date.Date{
		Year:  searchFrom.UTC().Year(),
		Month: searchFrom.UTC().Month(),
		Day:   searchFrom.UTC().Day(),
	}

	toDate := date.Date{
		Year:  searchTo.UTC().Year(),
		Month: searchTo.UTC().Month(),
		Day:   searchTo.UTC().Day(),
	}

	query := db.Model(&models.Load{}).Select(
		"pickup_date, carrier_name, " +
			"ratedata_carrier_line_haul_charge, " +
			"ratedata_carrier_cost, ratedata_carrier_cost_currency, " +
			"specifications_total_distance, specifications_transport_type, " +
			"pickup_city, pickup_state, pickup_zip_prefix, " +
			"consignee_city, consignee_state, consignee_zip_prefix")

	// Base query conditions that apply to all tiers
	query = query.Where(
		"service_id = ? "+
			"AND pickup_date IS NOT NULL "+
			"AND pickup_date BETWEEN ? AND ? "+
			"AND ratedata_carrier_cost IS NOT NULL",
		userServiceID, fromDate, toDate).Debug()

	// Handle transport type matching using GORM
	query = query.Scopes(fuzzyMatchTransportType(string(body.TransportType)))

	// Fetch has mode values that are not "TL", we are temporarily supporting "Spot Load" and "Primary Commitment"
	// TODO: After clarifying with Fetch and looking at Mcleod load integration `OrderType` values adjust this
	// TODO: Support query by mode in the future "TL" OR "LTL" lookups, we have tms loads that are "LTL"
	query = query.Where("mode IN (?)", []string{"TL", "Spot Load", "Primary Commitment"})

	// Add tier-specific conditions
	switch tier {
	case _3DigitZipLaneTier:
		if len(body.OriginZip) < 3 || len(body.DestinationZip) < 3 {
			log.Info(
				ctx,
				"insufficient zips for 3-digit zip tier",
				zap.String("originZip", body.OriginZip),
				zap.String("destZip", body.DestinationZip),
			)
			return nil, errors.New("insufficient zips provided for 3-digit zip tier")
		}
		originPrefix := body.OriginZip[:3]
		destPrefix := body.DestinationZip[:3]
		query = query.Where("pickup_zip_prefix = ? AND consignee_zip_prefix = ?", originPrefix, destPrefix)

	case CityLaneTier:
		if helpers.IsBlank(body.OriginCity) || helpers.IsBlank(body.OriginState) ||
			helpers.IsBlank(body.DestinationCity) || helpers.IsBlank(body.DestinationState) {
			log.Info(
				ctx,
				"insufficient city/state data for city tier",
				zap.String("originCity", body.OriginCity),
				zap.String("originState", body.OriginState),
				zap.String("destCity", body.DestinationCity),
				zap.String("destState", body.DestinationState),
			)
			return nil, errors.New("insufficient city/state provided for city/state tier")
		}
		query = query.Where(
			"pickup_city ILIKE ? AND pickup_state ILIKE ? AND "+
				"consignee_city ILIKE ? AND consignee_state ILIKE ?",
			body.OriginCity, body.OriginState,
			body.DestinationCity, body.DestinationState,
		)

	case StateLaneTier:
		if helpers.IsBlank(body.OriginState) || helpers.IsBlank(body.DestinationState) {
			log.Info(
				ctx,
				"insufficient state data for state tier",
				zap.String("originState", body.OriginState),
				zap.String("destState", body.DestinationState),
			)
			return nil, errors.New("insufficient state provided for state tier")
		}
		query = query.Where(
			"pickup_state ILIKE ? AND consignee_state ILIKE ?",
			body.OriginState, body.DestinationState,
		)
	}

	if err := query.Find(&loads).Error; err != nil {
		log.Error(
			ctx,
			"error querying loads for tier",
			zap.String("tier", string(tier)),
			zap.Error(err),
		)
		return nil, fmt.Errorf("error querying loads for tier %s: %w", tier, err)
	}

	if len(loads) == 0 {
		log.Info(
			ctx,
			"no matching loads found for tier",
			zap.String("tier", string(tier)),
			zap.String("transportType", string(body.TransportType)),
		)
		return nil, nil
	}

	log.Info(
		ctx,
		"found matching loads for tier",
		zap.String("tier", string(tier)),
		zap.Int("loadCount", len(loads)),
	)

	filteredLoads, filteredOutLoads := filterLoadsByDistance(loads, quoteMileDistance)

	log.Info(
		ctx, "filtered loads by distance",
		zap.String("tier", string(tier)),
		zap.Int("loadCountBefore", len(loads)),
		zap.Int("loadCountAfter", len(filteredLoads)),
		zap.Float64("quoteMileDistance", quoteMileDistance),
	)

	logFilteredOutLoads(ctx, filteredOutLoads, quoteMileDistance)

	if len(filteredLoads) == 0 {
		log.Info(
			ctx,
			"no loads matching distance criteria",
			zap.String("tier", string(tier)),
			zap.Float64("quoteMileDistance", quoteMileDistance),
		)
		return nil, nil
	}

	// Process the loads into the expected format using percentile calculations
	weekDataMap := apiutil.CalculateFourWeekDataPercentile(ctx, filteredLoads, quoteMileDistance)
	orderedData := apiutil.OrderWeekDataPercentile(weekDataMap)
	calculatedQuote := apiutil.CalculateQuotePercentile(weekDataMap, quoteMileDistance)

	return &SourceHistory{
		Source:                tmsIntegrationName,
		Timeframe:             fmt.Sprintf("%d-day", NumLookbackDays),
		LaneTier:              tier,
		InputtedTransportType: body.TransportType,
		ProxiedTransportType:  body.TransportType,
		CalculatedQuote:       calculatedQuote,
		Weeks:                 orderedData,
		IsPercentile:          true,
	}, nil
}

func fuzzyMatchTransportType(transportType string) func(db *gorm.DB) *gorm.DB {
	return func(db *gorm.DB) *gorm.DB {
		if transportType == "" {
			return db
		}

		// For VAN transport type, exclude sprinter vans
		if strings.ToUpper(transportType) == "VAN" {
			return db.Where(
				"specifications_transport_type ILIKE ? AND specifications_transport_type NOT ILIKE ?",
				fmt.Sprintf("%%%s%%", transportType),
				"%sprinter%",
			)
		}

		return db.Where(
			"specifications_transport_type ILIKE ?",
			fmt.Sprintf("%%%s%%", transportType),
		)
	}
}

func getCanadaLocationByPostalCode(ctx context.Context, postalCode *string) (city, province string, err error) {

	// Using AWS Locations for Canada postal codes, since they're not supported by USPS API
	pickupLocation, err := lookupAWSLocationFunc(ctx, "", "", *postalCode)
	if err != nil || pickupLocation == nil {
		log.Error(ctx, "error looking up canada zipcode with AWS Location", zap.Error(err))

		if err == nil && pickupLocation == nil {
			err = errors.New("failed to lookup location: no response from AWS Location service")
		}

		return "", "", err
	}

	if len(pickupLocation.Results) == 0 || pickupLocation.Results[0].Place == nil {
		return "", "", errors.New("no results found for canada zipcode")
	}

	pickupPlace := *pickupLocation.Results[0].Place

	province = *pickupPlace.Region
	if len(province) > 2 {
		provinceAbbr, err := helpers.GetCanadaProvinceAbbreviation(province)
		if err != nil {
			return "", "", err
		}

		province = provinceAbbr
	}

	return *pickupPlace.Municipality, province, nil
}

// hasValidWeekData checks if any week in the data has actual quotes
func hasValidWeekData(weeks []apiutil.WeekResponseItem) bool {
	for _, week := range weeks {
		if week.Quotes > 0 {
			return true
		}
	}
	return false
}

// filterLoadsByDistance filters loads based on distance criteria.
// For short hauls (<100 miles), tolerance (±30%).
// For long hauls (≥100 miles), tolerance (±50%).
func filterLoadsByDistance(
	loads []models.Load,
	quoteMileDistance float64,
) (filteredLoads, filteredOutLoads []models.Load) {

	for _, load := range loads {
		loadDistance := float64(load.Specifications.TotalDistance.Val)

		if quoteMileDistance < lowDistanceThreshold {
			// For short hauls, use tolerance (±30%)
			minDistance := quoteMileDistance * 0.7
			maxDistance := quoteMileDistance * 1.3
			if loadDistance >= minDistance && loadDistance <= maxDistance {
				filteredLoads = append(filteredLoads, load)
			} else {
				filteredOutLoads = append(filteredOutLoads, load)
			}
		} else {
			// For long hauls, use tolerance (±50%)
			minDistance := quoteMileDistance * 0.5
			maxDistance := quoteMileDistance * 1.5
			if loadDistance >= minDistance && loadDistance <= maxDistance {
				filteredLoads = append(filteredLoads, load)
			} else {
				filteredOutLoads = append(filteredOutLoads, load)
			}
		}
	}

	return filteredLoads, filteredOutLoads
}

func logFilteredOutLoads(ctx context.Context, filteredOutLoads []models.Load, quoteMileDistance float64) {
	for _, load := range filteredOutLoads {
		pickupTime := time.Date(load.PickupDate.Year, load.PickupDate.Month, load.PickupDate.Day, 0, 0, 0, 0, time.UTC)
		log.Debug(
			ctx,
			"load filtered out by distance",
			zap.String("pickupZip", load.Pickup.ZipPrefix),
			zap.String("consigneeZip", load.Consignee.ZipPrefix),
			zap.String("pickupCity", load.Pickup.City),
			zap.String("pickupState", load.Pickup.State),
			zap.String("consigneeCity", load.Consignee.City),
			zap.String("consigneeState", load.Consignee.State),
			zap.Float64("loadDistance", float64(load.Specifications.TotalDistance.Val)),
			zap.Float64("quoteMileDistance", quoteMileDistance),
			zap.Time("pickupDate", pickupTime),
		)
	}
}
