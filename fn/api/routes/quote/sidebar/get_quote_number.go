package quoteprivate

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"regexp"

	"github.com/PuerkitoBio/goquery"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/log"
	quoteRequestDB "github.com/drumkitai/drumkit/common/rds/quote_request"
)

type (
	GetQuoteNumberParams struct {
		EmailID uint `json:"emailID" validate:"required"`
	}
)

type GetQuoteNumberResponse struct {
	QuoteNumber            string `json:"quoteNumber"`
	HasThirdPartyQuoteURLs bool   `json:"hasThirdPartyQuoteURLs"`
}

func GetPrivateQuoteNumber(c *fiber.Ctx) error {
	var params GetQuoteNumberParams
	var err error

	if err := api.Parse(c, &params, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}
	ctx := c.UserContext()

	resp, err := getPrivateQuoteNumber(ctx, &params)
	if err != nil {
		log.Warn(ctx, "getQuoteNumber failed", zap.Error(err))
		// return the response because the UI relies on HasThirdPartyURLs regardless of the error
		return c.Status(http.StatusOK).JSON(resp)
	}

	return c.Status(http.StatusOK).JSON(resp)
}

func getPrivateQuoteNumber(
	ctx context.Context,
	params *GetQuoteNumberParams,
) (*GetQuoteNumberResponse, error) {

	quoteURLs, err := quoteRequestDB.GetThirdPartyURLsByEmailID(ctx, params.EmailID)
	if err != nil || quoteURLs.FormURL == "" || quoteURLs.SubmissionURL == "" {
		return &GetQuoteNumberResponse{HasThirdPartyQuoteURLs: false}, err
	}

	client := &http.Client{}
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, quoteURLs.FormURL, nil)
	if err != nil {
		return &GetQuoteNumberResponse{HasThirdPartyQuoteURLs: true}, err
	}

	res, err := client.Do(req)
	if err != nil {
		return &GetQuoteNumberResponse{HasThirdPartyQuoteURLs: true}, err
	}
	defer res.Body.Close()
	if res.StatusCode != http.StatusOK {
		return &GetQuoteNumberResponse{HasThirdPartyQuoteURLs: true},
			fmt.Errorf("error fetching quote number: %s", res.Status)
	}

	doc, err := goquery.NewDocumentFromReader(res.Body)
	if err != nil {
		return &GetQuoteNumberResponse{HasThirdPartyQuoteURLs: true}, err
	}

	td := doc.Find("td:contains('Reference:')").First()
	text := td.Text()

	var quoteNumber string
	re := regexp.MustCompile(`Reference:\s*(\S+)\s*\(Load ID\)`)
	match := re.FindStringSubmatch(text)
	if len(match) > 1 {
		quoteNumber = match[1]
	} else {
		return &GetQuoteNumberResponse{HasThirdPartyQuoteURLs: true}, errors.New("could not find quote number")
	}

	return &GetQuoteNumberResponse{QuoteNumber: quoteNumber, HasThirdPartyQuoteURLs: true}, nil

}
