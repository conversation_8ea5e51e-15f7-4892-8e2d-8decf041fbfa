package email

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"google.golang.org/api/gmail/v1"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/fn/api/env"
	apiUtil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	DevIngestionMessage struct {
		MessageID string `json:"messageID"`
		FromName  string `json:"fromName"`
		FromEmail string `json:"fromEmail"`
		ToName    string `json:"toName"`
		ToEmail   string `json:"toEmail"`
		Subject   string `json:"subject"`
		Body      string `json:"body"`
		Date      string `json:"date"`
	}

	DevIngestEmailRequest struct {
		ThreadID string `json:"threadId"`
		// to update
		IngestionMessage DevIngestionMessage `json:"ingestionMessage"`
	}

	DevIngestEmailResponse struct {
		Triggered bool `json:"triggered"`
	}
	localWebhookBody struct {
		EmailAddress string `json:"emailAddress"`

		// Alternatively, explicitly define the entire message yourself.
		// Each payload part must be base64-URL encoded, unless AddPayloadEncoding is enabled.
		Msg *gmail.Message `json:"msg"`

		// By default, we assume the provided Msg payload data is already base64-URL encoded.
		// If not, set "addPayloadEncoding": true
		AddPayloadEncoding bool `json:"addPayloadEncoding"`
	}
)

// POST to beacon-processor running locally on a different port
func DevIngestEmail(c *fiber.Ctx) error {
	var body DevIngestEmailRequest
	if err := api.Parse(c, nil, nil, &body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(
		c.UserContext(),
		zap.Any("messageId", body.IngestionMessage.MessageID), zap.Any("threadId", body.ThreadID),
	)

	claims := middleware.ClaimsFromContext(c)
	cleanedDateString := strings.ReplaceAll(body.IngestionMessage.Date, "\u202F", " ")

	t, err := time.Parse("Jan 2, 2006, 3:04 PM", cleanedDateString)
	if err != nil {
		return fmt.Errorf("error parsing date: %w", err)
	}

	// reproducing outlook's webhook behavior for environment compatibility.
	// (Gmail IDs are hexadecimal so they're unchanged by this)
	decodedThreadID, err := apiUtil.DecodeOutlookID(body.ThreadID)
	if err != nil {
		log.Error(ctx, "error decoding threadID", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	webhookBody := localWebhookBody{
		EmailAddress: claims.Email,
		Msg: &gmail.Message{
			Id:           body.IngestionMessage.MessageID,
			ThreadId:     decodedThreadID,
			InternalDate: t.Unix(),
			Payload: &gmail.MessagePart{
				Headers: []*gmail.MessagePartHeader{
					{
						Name:  "Message-ID",
						Value: "something idk",
					},
					{
						Name:  "Subject",
						Value: body.IngestionMessage.Subject,
					},
					{
						Name: "From",
						Value: fmt.Sprintf(
							"%s %s", body.IngestionMessage.FromName, body.IngestionMessage.FromEmail),
					},
					{
						Name:  "To",
						Value: fmt.Sprintf("%s %s", body.IngestionMessage.ToName, body.IngestionMessage.ToEmail),
					},
				},
				MimeType: "text/plain",
				Body: &gmail.MessagePartBody{
					Data: body.IngestionMessage.Body,
				},
			},
		},
		AddPayloadEncoding: true,
	}

	marshalledBody, err := json.Marshal(webhookBody)
	if err != nil {
		return fmt.Errorf("unable to marshal local POST request: %w", err)
	}
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		fmt.Sprintf("http://%s:5001/inboxWebhook", env.Vars.DBHost),
		strings.NewReader(string(marshalledBody)),
	)
	if err != nil {
		return fmt.Errorf("unable to create local POST request: %w", err)
	}
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		return fmt.Errorf("failed to send POST request to %s: %w", req.URL.String(), err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("POST %s returned %d", req.URL.String(), resp.StatusCode)
	}

	return c.Status(http.StatusOK).JSON(DevIngestEmailResponse{Triggered: true})
}
