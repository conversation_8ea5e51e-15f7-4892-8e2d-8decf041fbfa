package email

import (
	"errors"
	"fmt"
	"net/http"

	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/api"
	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	apiutil "github.com/drumkitai/drumkit/fn/api/util"
)

type (
	GetEmailAttachmentsPath struct {
		// QN: Or Drumkit ID? TBD
		ExternalID string `params:"external_id" validate:"required"`
	}

	GetEmailAttachmentsResponse struct {
		EmailID         uint                `json:"emailID"`
		EmailExternalID string              `json:"emailExternalID"`
		EmailThreadID   string              `json:"emailThreadID"`
		Attachments     []models.Attachment `json:"attachments"`
	}
)

func GetEmailAttachments(c *fiber.Ctx) error {

	var path GetEmailAttachmentsPath
	if err := api.Parse(c, &path, nil, nil); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	ctx := log.With(c.UserContext(), zap.Any("pathParams", path))

	// NOTE: When ingested, Outlook webhooks replace / with -, and + with _
	// (Gmail IDs are hexadecimal so they're unaffected by this)
	replacedID, err := apiutil.DecodeOutlookID(path.ExternalID)
	if err != nil {
		log.Error(ctx, "error decoding threadID", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	email, err := emailDB.GetEmailByExternalID(ctx, replacedID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.Status(http.StatusNotFound).SendString(
				fmt.Sprintf("message %s not found", replacedID),
			)
		}

		log.Error(ctx, "get email DB error", zap.Error(err))
		return c.SendStatus(http.StatusInternalServerError)
	}

	ctx = log.With(ctx, zap.Uint("emailDrumkitID", email.ID))

	claims := middleware.ClaimsFromContext(c)
	user, err := userDB.GetByEmail(ctx, claims.Email)
	if err != nil {
		log.Error(ctx, "could not get user", zap.Error(err))
		return c.SendStatus(http.StatusUnauthorized)
	}

	if user.Service.IsDelegatedInboxEnabled {
		// For delegated inboxes we're a bit more permissive, so instead of matching email addresses from the
		// JWT to the Email DB entry we simply match their service IDs instead.
		if *claims.ServiceID != email.ServiceID {
			log.WarnNoSentry(
				ctx,
				"unauthorized: email from token does not match serviceID in DB",
				zap.Uint("dbEmailServiceID", email.ServiceID),
			)

			return c.SendStatus(http.StatusUnauthorized)
		}
	} else {
		if user.ID != email.UserID {
			log.WarnNoSentry(
				ctx,
				"unauthorized: userID from token does not match email's userID",
				zap.Uint("dbEmailUserID", email.UserID),
				zap.String("dbEmailAccount", email.Account),
			)

			return c.SendStatus(http.StatusUnauthorized)
		}
	}

	return c.Status(http.StatusOK).JSON(GetEmailAttachmentsResponse{
		EmailID:         email.ID,
		EmailExternalID: email.ExternalID,
		EmailThreadID:   email.ThreadID,
		Attachments:     email.Attachments,
	})
}
