package env

import (
	"context"
	"fmt"
	"os"

	"github.com/joho/godotenv"
	"github.com/kelseyhightower/envconfig"

	"github.com/drumkitai/drumkit/common/helpers/awsutil"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/rds"
)

var Vars envVars

type envVars struct {
	rds.EnvConfig

	// Deployment stage: "dev" or "prod"
	AppEnv         string `envconfig:"APP_ENV" required:"true"`
	TraceOn        bool   `envconfig:"TRACE_ON"`
	SkipDuplicates bool   `envconfig:"SKIP_DUPLICATES" default:"true"`

	AxiomLogDataset   string `envconfig:"AXIOM_LOG_DATASET"`
	AxiomTraceDataset string `envconfig:"AXIOM_TRACE_DATASET"`
	AxiomOrgID        string `envconfig:"AXIOM_ORG_ID"`
	AxiomToken        string `envconfig:"AXIOM_TOKEN"`

	// Associated with the Drumkit Outlook app
	MicrosoftClientID string `envconfig:"MICROSOFT_CLIENT_ID" required:"true"`

	S3BucketName string `envconfig:"S3_BUCKET_NAME" required:"true"`
	SQSQueueURL  string `envconfig:"SQS_QUEUE_URL" required:"true"`

	// For prod only
	SecretARN string `envconfig:"SECRET_ARN"`

	// In dev, these are set directly.
	// In prod, this is loaded from SecretARN at startup.
	MicrosoftClientSecret string `envconfig:"MICROSOFT_CLIENT_SECRET"`
}

// Format in AWS secrets manager
type envSecrets struct {
	AESKey                string `json:"AES_KEY"`
	MicrosoftClientSecret string `json:"MICROSOFT_CLIENT_SECRET"`
}

// Load environment variables into Vars global
func Load(ctx context.Context) error {
	if stage := os.Getenv("APP_ENV"); stage == "" || stage == "dev" {
		if err := godotenv.Load(); err != nil {
			log.WarnNoSentry(ctx, "no .env file found")
		}
		ctx = log.NewFromEnv(ctx)
	}

	if err := envconfig.Process("", &Vars); err != nil {
		return err
	}

	switch Vars.AppEnv {
	case "dev":
		if Vars.MicrosoftClientSecret == "" {
			log.Warn(ctx, "missing MICROSOFT_CLIENT_SECRET env var")
		}

	case "prod", "staging":
		var secret envSecrets

		if err := awsutil.ReadSecretJSON(ctx, Vars.SecretARN, &secret); err != nil {
			return err
		}

		if secret.AESKey == "" || secret.MicrosoftClientSecret == "" {
			return fmt.Errorf("%s is missing some fields", Vars.SecretARN)
		}

		crypto.AESKey = []byte(secret.AESKey)
		Vars.MicrosoftClientSecret = secret.MicrosoftClientSecret

	}

	return nil
}
