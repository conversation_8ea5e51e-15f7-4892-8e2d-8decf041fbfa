package main

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"github.com/gofiber/fiber/v2"
	"go.uber.org/zap"
	"google.golang.org/api/gmail/v1"

	"github.com/drumkitai/drumkit/common/api/middleware"
	"github.com/drumkitai/drumkit/common/helpers/oauth"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	mem "github.com/drumkitai/drumkit/common/integrations/email/gmailclient/mock"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/fn/ingestion"
	"github.com/drumkitai/drumkit/fn/ingestion/gmail/env"
)

const devPort = ":5001"

// POST format when invoking beacon-ingestion locally via Fiber
//
// Email message data can be replayed from the S3 archive or defined explicitly.
type localWebhookBody struct {
	EmailAddress string `json:"emailAddress"`
	HistoryID    uint64 `json:"historyId"`

	// Specify an email from the axle-beacon-ingestion S3 bucket to replay.
	// E.g. "gmail/<EMAIL>/18b05dea69236b99.json"
	S3Key string `json:"s3Key"`

	// Alternatively, explicitly define the entire message yourself.
	// Each payload part must be base64-URL encoded, unless AddPayloadEncoding is enabled.
	Msg *gmail.Message `json:"msg"`

	// By default, we assume the provided Msg payload data is already base64-URL encoded.
	// If not, set "addPayloadEncoding": true
	AddPayloadEncoding bool `json:"addPayloadEncoding"`
}

// Send messages to beacon-processor instead of SQS when running locally
type localSQSClient struct{}

func (c *localSQSClient) SendMessage(
	ctx context.Context,
	input *sqs.SendMessageInput,
	_ ...func(*sqs.Options),
) (*sqs.SendMessageOutput, error) {

	// POST to beacon-processor running locally on a different port
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		fmt.Sprintf("http://%s:5005/invoke", env.Vars.DBHost),
		strings.NewReader(aws.ToString(input.MessageBody)),
	)
	if err != nil {
		return nil, fmt.Errorf("unable to create local POST request: %w", err)
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send POST request to %s: %w", req.URL.String(), err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("POST %s returned %d", req.URL.String(), resp.StatusCode)
	}

	return &sqs.SendMessageOutput{}, nil
}

// Launch a local fiber server
func runLocalServer() error {

	var localSQS localSQSClient
	sqsClient = &localSQS

	app := fiber.New()

	app.Use(middleware.Tracer())
	app.Use(middleware.Zap(middleware.WithAppEnv("dev")))

	app.Post("/inboxWebhook", fiberHandler)

	return app.Listen(devPort)
}

// TODO: handle actual Gmail notifs in dev as well
func fiberHandler(c *fiber.Ctx) error {
	ctx := c.UserContext()

	var body localWebhookBody
	if err := c.BodyParser(&body); err != nil {
		return c.Status(http.StatusBadRequest).SendString(err.Error())
	}

	if body.S3Key != "" {
		var err error
		if err = ingestion.DownloadFromS3(ctx, body.S3Key, &body.Msg); err != nil {
			return c.Status(http.StatusInternalServerError).SendString(err.Error())
		}
	} else if body.Msg == nil || body.Msg.Payload == nil {
		return c.Status(http.StatusBadRequest).SendString("s3Key or msg.payload must be defined")
	}

	if body.AddPayloadEncoding {
		encodePayload(body.Msg.Payload)
	}

	// If true, makes requests to the Gmail API instead of in-memory implementation.
	var liveRun = os.Getenv("LIVE_RUN") == "true" || os.Getenv("LIVE_RUN") == "1"

	// Replace Gmail client with in-memory mock implementation
	if !liveRun {
		memService := &mem.Service{
			Messages: map[string]*gmail.Message{body.Msg.Id: body.Msg},
		}

		gmailConstructor = func(
			_ context.Context,
			_,
			_ string,
			_ models.UserAccessor,
			_ ...oauth.Option,
		) (gmailclient.Client, error) {
			return memService, nil
		}
	}

	// Convert Fiber POST request into the Gmail payload expected by the app
	dataJSON, err := json.Marshal(MessageData{EmailAddress: body.EmailAddress, HistoryID: body.HistoryID})
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("invalid json body: " + err.Error())
	}

	whJSON, err := json.Marshal(WebhookBody{
		Message: Message{
			Data:        base64.URLEncoding.EncodeToString(dataJSON),
			MessageID:   body.Msg.Id,
			PublishTime: time.Now(),
		},
		Subscription: env.Vars.GmailSubscriptionID,
	})
	if err != nil {
		return c.Status(http.StatusBadRequest).SendString("json webhook marshal failed: " + err.Error())
	}

	event := events.LambdaFunctionURLRequest{
		Headers: map[string]string{"from": "<EMAIL>"},
		Body:    string(whJSON),
		RawPath: c.Path(),
	}

	log.Info(ctx, "sending event to beacon-ingestion handler", zap.Any("event", event))

	result, err := handler(ctx, event)
	if err != nil {
		return c.Status(http.StatusInternalServerError).SendString(err.Error())
	}

	return c.Status(result.StatusCode).SendString(result.Body)
}

// Base64-URL encode the data of each payload part
func encodePayload(part *gmail.MessagePart) {
	if part.Body != nil && part.Body.Data != "" {
		part.Body.Data = base64.URLEncoding.EncodeToString([]byte(part.Body.Data))
	}

	for _, child := range part.Parts {
		encodePayload(child)
	}
}
