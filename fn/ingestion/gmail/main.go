package main

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/aws/aws-lambda-go/events"
	"github.com/aws/aws-lambda-go/lambda"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"go.opentelemetry.io/contrib/instrumentation/github.com/aws/aws-lambda-go/otellambda"
	"go.opentelemetry.io/otel/sdk/resource"
	semconv "go.opentelemetry.io/otel/semconv/v1.24.0"
	"go.uber.org/zap"
	"golang.org/x/oauth2"
	"google.golang.org/api/gmail/v1"
	"google.golang.org/api/googleapi"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/emails"
	gmail_helpers "github.com/drumkitai/drumkit/common/gmail"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
	emailDB "github.com/drumkitai/drumkit/common/rds/email"
	userDB "github.com/drumkitai/drumkit/common/rds/user"
	"github.com/drumkitai/drumkit/common/redis"
	"github.com/drumkitai/drumkit/common/sentry"
	helpers "github.com/drumkitai/drumkit/fn/ingestion"
	"github.com/drumkitai/drumkit/fn/ingestion/gmail/env"
)

type (
	WebhookBody struct {
		Message      Message `json:"message"`
		Subscription string  `json:"subscription"`
	}

	Message struct {
		// Base64-URL encoded JSON string of MessageData
		Data string `json:"data"`

		// ID of the message that triggered the webhook. NOT necessarily a new message.
		MessageID string `json:"messageId"`

		PublishTime time.Time `json:"publishTime"`

		// ***** Following section is for dev purposes only and not part of Pub/Sub ****** //

		// If true, use the historyID of the specified message ID. If false, get just that message.
		UseMessageHistoryID bool

		// Max number of most recent messages to get. `MessageID` takes precedence
		NumMessages int `json:"numMessages"`
	}

	MessageData struct {
		EmailAddress string `json:"emailAddress"`
		HistoryID    uint64 `json:"historyId"`
	}

	WatchInboxBody struct {
		EmailAddress string `json:"emailAddress"`
		StopWatching bool   `json:"stopWatching"`
	}
)

var (
	// Unit tests can replace these functions
	getUserByEmail   = userDB.GetByEmail
	dbUpdateUserFunc = userDB.Update
	gmailConstructor = gmailclient.New[models.UserAccessor]
	getEmailFunc     = emailDB.GetEmailByExternalID
	dbGetServiceFunc = rds.GetServiceByID

	s3Uploader s3backup.Archiver
	sqsClient  helpers.SQSAPI
)

const (
	serviceName    = "ingestion-gmail-axiom-otel" // Name of the service for tracing.
	serviceVersion = "0.0.1"                      // Version of the service.
)

func Resource() *resource.Resource {
	return resource.NewWithAttributes(
		semconv.SchemaURL,
		semconv.ServiceNameKey.String(serviceName),
		semconv.ServiceVersionKey.String(serviceVersion),
	)
}

func main() {
	if err := sentry.Initialize(); err != nil {
		panic(err)
	}

	ctx := context.Background()

	// If there is a panic in the rest of the initialization, report to Sentry
	defer func() {
		if r := recover(); r != nil {
			hub := sentry.GetHubFromContext(ctx)
			hub.RecoverWithContext(ctx, r)
			hub.Flush(sentry.FlushTimeout)

			panic(r)
		}
	}()

	if err := env.Load(ctx); err != nil {
		panic(err)
	}

	stopFunc := otel.CreateTracerProvider(ctx, env.Vars.AxiomTraceDataset)
	if stopFunc != nil {
		defer func() {
			err := stopFunc()
			if err != nil {
				log.Warn(ctx, "error cleaning up tracer provider", zap.Error(err))
			}
		}()
	}

	if err := rds.Open(
		ctx,
		env.Vars.EnvConfig,
		rds.WithAppEnv(env.Vars.AppEnv),
		rds.WithAWSSecretsManager(env.Vars.AppEnv == "staging" || env.Vars.AppEnv == "prod"),
		rds.WithApplicationName("drumkit-ingestion-gmail"),
	); err != nil {
		panic(err)
	}

	if env.Vars.RedisURL != "" {
		if err := redis.Init(ctx, env.Vars.RedisURL); err != nil {
			panic(err)
		}
	}

	if env.Vars.S3BucketName != "" {
		var err error
		if s3Uploader, err = s3backup.New(ctx, env.Vars.S3BucketName); err != nil {
			panic(err)
		}
	}

	if env.Vars.AppEnv == "dev" {
		if err := rds.AutoMigrate(ctx); err != nil {
			log.Warn(ctx, "Error running migrations", zap.Error(err))
		}

		panic(runLocalServer())
	}

	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
	if err != nil {
		panic(err)
	}
	sqsClient = sqs.NewFromConfig(cfg)

	lambda.Start(otellambda.InstrumentHandler(handlerWithLogging))
}

func handlerWithLogging(
	ctx context.Context,
	event events.LambdaFunctionURLRequest,
) (result *events.LambdaFunctionURLResponse, err error) {

	sentry.WithHub(ctx, func(ctx context.Context) {
		ctx = log.NewFromEnv(ctx, zap.String("path", event.RawPath))
		hub := sentry.GetHubFromContext(ctx)

		// If there is an error, flush it to Sentry before the Lambda invocation is finished.
		defer hub.Flush(sentry.FlushTimeout)
		// NOTE: Have all logs flushed before the application exits.
		//
		// THIS IS IMPORTANT: Without it, the logs will not be sent to Axiom as
		// the buffer will not be flushed when the application exits.
		defer log.Flush(ctx)

		// Log Lambda result and report errors to Sentry
		defer func() {
			if err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) &&
					!strings.Contains(err.Error(), `oauth2: "internal_failure"`) {

					log.Error(ctx, "returning error", zap.Error(err))

				} else {
					log.ErrorNoSentry(ctx, "returning error", zap.Error(err))
				}

			} else {
				log.Info(ctx, "returning result", zap.Any("result", result))
			}
		}()

		log.Info(ctx, "received event", zap.Any("event", event))
		result, err = handler(ctx, event)

		var oauthError *oauth2.RetrieveError
		if err != nil && errors.As(err, &oauthError) {
			log.Info(ctx, "oauth error", zap.Any("error", err))
			result = &events.LambdaFunctionURLResponse{StatusCode: http.StatusNoContent}
		}
	})

	return
}

func handler(
	ctx context.Context,
	event events.LambdaFunctionURLRequest,
) (*events.LambdaFunctionURLResponse, error) {

	switch event.RawPath {
	case "/watchInbox":
		return handleWatchInbox(ctx, event)

	case "/inboxWebhook":
		return handleInboxWebhook(ctx, event)

	default:
		log.WarnNoSentry(ctx, "invalid request path")
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusOK}, nil
	}
}

// Placeholder code for subscribing to Inbox webhook
// Need to be re-subscribe at least once every 7 days
func handleWatchInbox(
	ctx context.Context,
	event events.LambdaFunctionURLRequest,
) (*events.LambdaFunctionURLResponse, error) {

	var body WatchInboxBody
	if err := json.Unmarshal([]byte(event.Body), &body); err != nil {
		return nil, err
	}

	ctx = log.With(ctx, zap.String("emailAccount", body.EmailAddress))

	user, err := getUserByEmail(ctx, body.EmailAddress)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnNoSentry(ctx, "handleWatchInbox: user does not exist")
			return &events.LambdaFunctionURLResponse{StatusCode: http.StatusOK, Body: "user not found"}, nil
		}

		return nil, fmt.Errorf("user '%s' lookup failed: %w", body.EmailAddress, err)
	}

	client, err := gmailConstructor(
		ctx,
		env.Vars.GoogleClientID,
		env.Vars.GoogleClientSecret,
		&user,
	)
	if err != nil {
		return nil, fmt.Errorf("error creating Gmail service: %w", err)
	}

	var respStr string

	if body.StopWatching {
		if err := client.StopWatchingInbox(ctx); err != nil {
			return nil, fmt.Errorf("error stopping watching %s inbox: %w", body.EmailAddress, err)
		}

		respStr = fmt.Sprint("Stopping watching user: ", body.EmailAddress)
	} else {
		req := &gmail.WatchRequest{LabelIds: []string{"INBOX"}, TopicName: env.Vars.GmailWebhookTopic}

		r, err := client.WatchInbox(ctx, req)
		if err != nil {
			return nil, fmt.Errorf("error watching %s inbox: %w", body.EmailAddress, err)
		}

		respStr = fmt.Sprintf("%#v", *r)
	}
	return &events.LambdaFunctionURLResponse{
		StatusCode: http.StatusOK,
		Body:       respStr,
	}, nil
}

func handleInboxWebhook(
	ctx context.Context,
	event events.LambdaFunctionURLRequest,
) (*events.LambdaFunctionURLResponse, error) {

	webhookBody, msgData, err := parseWebhook(ctx, event)
	if err != nil {
		log.Error(ctx, "parseWebhook failed", zap.Error(err))
		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusBadRequest}, err
	}

	ctx = log.With(
		ctx,
		zap.String("emailAddress", msgData.EmailAddress),
		zap.Uint64("historyId", msgData.HistoryID),
	)

	user, err := getUserByEmail(ctx, msgData.EmailAddress)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnNoSentry(ctx, "handleInboxWebhook: user does not exist")
			return &events.LambdaFunctionURLResponse{StatusCode: http.StatusOK, Body: "user not found"}, nil
		}

		return nil, fmt.Errorf("user '%s' lookup failed: %w", msgData.EmailAddress, err)
	}

	service, err := dbGetServiceFunc(ctx, user.ServiceID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.WarnNoSentry(ctx, "handleInboxWebhook: service does not exist")
			return &events.LambdaFunctionURLResponse{StatusCode: http.StatusOK, Body: "service not found"}, nil
		}

		return nil, fmt.Errorf("service lookup failed: %w", err)
	}

	client, err := gmailConstructor(
		ctx,
		env.Vars.GoogleClientID,
		env.Vars.GoogleClientSecret,
		&user,
	)
	if err != nil {
		return nil, fmt.Errorf("error creating Gmail service: %w", err)
	}

	var newWebhookExpiry time.Time
	// Re-watch inbox before subscription expires
	diff := time.Until(user.WebhookExpiration).Hours()
	if diff < 72 {
		req := &gmail.WatchRequest{LabelIds: []string{"INBOX"}, TopicName: env.Vars.GmailWebhookTopic}

		if r, err := client.WatchInbox(ctx, req); err == nil {
			log.Info(ctx, "successfully resubscribed to inbox", zap.Any("response", r))
			newWebhookExpiry = time.UnixMilli(r.Expiration)
		} else {
			log.ErrorNoSentry(ctx, "error resubscribing to inbox", zap.Error(err))
			if diff < 24 && shouldSendToSentry() {
				sentry.GetHubFromContext(ctx).CaptureException(err)
			}
		}
	}

	var msgIDs []string

	// Complete dev request
	if msgData.HistoryID == 0 {
		msg, err := client.GetMessage(ctx, webhookBody.Message.MessageID)
		if err != nil {
			return nil, fmt.Errorf("[Dev Request] error getting message %s: %w", webhookBody.Message.MessageID, err)
		}

		if webhookBody.Message.UseMessageHistoryID {
			user.GmailLastHistoryID = msg.HistoryId
		} else {
			msgIDs = append(msgIDs, msg.Id)
		}
	}

	// Complete Pub/Sub request
	if len(msgIDs) == 0 {
		msgIDs, err = getMsgIDs(ctx, client, &user)
		var httpErr *googleapi.Error
		if err != nil {
			// A 404 on a historyId is an expected error for old webhooks.
			// Log it and move on. The user will be backfilled by the dedicated service if needed.
			// For other errors, we should fail and allow the webhook to be retried.
			//nolint:staticcheck
			if !(errors.As(err, &httpErr) && httpErr.Code == http.StatusNotFound) {
				// unexpected error: return
				return nil, fmt.Errorf("error getting history list for %s: %w", msgData.EmailAddress, err)
			}

			log.Info(
				ctx,
				"gmailclient.ListHistory returned 404 (likely an expired historyID), skipping",
				zap.Uint64("lastHistoryId", user.GmailLastHistoryID),
			)
			// Return OK to prevent the webhook from being redelivered.
			return &events.LambdaFunctionURLResponse{StatusCode: http.StatusOK, Body: "expired historyId"}, nil
		}
	}

	log.Info(ctx, "processing messages", zap.Int("count", len(msgIDs)), zap.Strings("msgIds", msgIDs))

	if len(msgIDs) == 0 {
		if err := updateUserHistory(ctx, user, msgData.HistoryID, newWebhookExpiry); err != nil {
			// Fail-open. If there's a transient error updating the DB, next webhook will try same history ID
			log.Error(ctx, "updateUserHistory failed", zap.Error(err))
		}

		return &events.LambdaFunctionURLResponse{StatusCode: http.StatusOK, Body: "OK"}, nil
	}

	const batchSize = 100
	var groups [][]string

	for i := 0; i < len(msgIDs); i += batchSize {
		end := i + batchSize
		if end > len(msgIDs) {
			end = len(msgIDs)
		}
		groups = append(groups, msgIDs[i:end])
	}

	var msgs []*gmail.Message
	for _, group := range groups {
		groupMsgs, err := client.BatchGetMessages(ctx, group)
		if err != nil {
			return nil, fmt.Errorf("failed to get msgs: %w", err)
		}
		msgs = append(msgs, groupMsgs...)
	}

	for _, msg := range msgs {
		if msg.Id == "" {
			log.WarnNoSentry(ctx, "skipping gmail msg with no id", zap.Any("fullMsg", msg))
			continue
		}

		v := *msg
		gmailMsg := emails.GmailMessage{Message: &v}

		ctx = log.With(ctx,
			zap.String("externalId", msg.Id),
			zap.String("threadId", msg.ThreadId),
			//nolint:contextcheck
			zap.String("rfcId", gmailMsg.GetRFCMessageID()),
			//nolint:contextcheck
			zap.String("subject", gmailMsg.GetSubject()),
			//nolint:contextcheck
			zap.String("sender", gmailMsg.GetSender()),
		)

		if env.Vars.SkipDuplicates {
			if shouldSkip, location := shouldSkipDuplicate(ctx, msg.Id); shouldSkip {
				log.Info(ctx, "skipping duplicate message", zap.String("location", location))
				continue
			}
		}

		if shouldSkip, reason := shouldSkipMessage(msg); shouldSkip {
			log.Info(ctx, "skipping gmail msg", zap.String("reason", reason), zap.Any("fullMsg", msg))
			continue
		}

		if err := gmail_helpers.ProcessAndEnqueueGmailMessage(
			ctx,
			client,
			sqsClient,
			s3Uploader,
			&user,
			&service,
			msg,
			env.Vars.SQSQueueURL,
		); err != nil {
			log.Error(ctx, "failed to process and enqueue gmail message", zap.Error(err), zap.String("msgId", msg.Id))
			// Continue processing other messages
		}

		// Save email external id to redis for future duplicate check in gmail ingestion
		if redis.RDB != nil {
			redis.RDB.Set(
				ctx,
				fmt.Sprintf("email-external-id-%s", msg.Id),
				msg.Id,
				72*time.Hour,
			)
		}
	}

	if err := updateUserHistory(ctx, user, msgData.HistoryID, newWebhookExpiry); err != nil {
		log.Error(ctx, "updateUserHistory failed", zap.Error(err))
	}

	return &events.LambdaFunctionURLResponse{StatusCode: http.StatusOK, Body: "OK"}, nil
}

// Validate and parse a Gmail webhook
func parseWebhook(ctx context.Context, event events.LambdaFunctionURLRequest) (*WebhookBody, *MessageData, error) {
	log.Info(ctx, "parsing webhook", zap.Any("event", event))
	if from := event.Headers["from"]; from != "<EMAIL>" {
		return nil, nil, fmt.Errorf("ignoring /inboxWebhook request from unknown sender %s", from)
	}

	var webhookBody WebhookBody
	if err := json.Unmarshal([]byte(event.Body), &webhookBody); err != nil {
		return nil, nil, fmt.Errorf("error unmarshaling request body: %w", err)
	}

	if webhookBody.Subscription != env.Vars.GmailSubscriptionID {
		return nil, nil, fmt.Errorf("ignoring /inboxWebhook request from unknown subscription '%s' (expected '%s')",
			webhookBody.Subscription, env.Vars.GmailSubscriptionID)
	}

	decoded, err := base64.URLEncoding.DecodeString(webhookBody.Message.Data)
	if err != nil {
		return nil, nil, fmt.Errorf("error base64-decoding webhook data: %w", err)
	}

	var msgData MessageData
	if err = json.Unmarshal(decoded, &msgData); err != nil {
		return nil, nil, fmt.Errorf("error unmarshaling message data: %w", err)
	}

	return &webhookBody, &msgData, nil
}

func updateUserHistory(ctx context.Context, user models.User, msgHistoryID uint64, newWebhookExpiry time.Time) error {
	if msgHistoryID != 0 && user.GmailLastHistoryID != msgHistoryID ||
		newWebhookExpiry.Sub(user.WebhookExpiration) > 0 {
		user.GmailLastHistoryID = msgHistoryID
		// If time = 0, Gorm will not update by default
		user.WebhookExpiration = newWebhookExpiry

		log.Info(
			ctx,
			"updating user in DB",
			zap.String("emailAddress", user.EmailAddress),
			zap.Uint64("historyId", user.GmailLastHistoryID),
		)

		if err := dbUpdateUserFunc(ctx, user); err != nil {
			return fmt.Errorf("rds.UpdateUser failed: %w", err)
		}
	}

	return nil
}

// Simple time-based function to filter out Sentry reports due to auth issues and prevent spikes
// Specific to oauth issues for now until more cases are known
func shouldSendToSentry() bool {
	now := time.Now()
	return now.Minute()%10 == 0 && now.Second()%25 == 0
}

func getMsgIDs(ctx context.Context, client gmailclient.Client, user *models.User) (msgIDs []string, err error) {
	resp, err := client.ListHistory(ctx, user.GmailLastHistoryID)
	if err != nil {
		return msgIDs, err
	}

	for _, history := range resp.History {
		for _, msgAdded := range history.MessagesAdded {
			msgIDs = append(msgIDs, msgAdded.Message.Id)
		}
	}

	return msgIDs, nil
}

func shouldSkipMessage(msg *gmail.Message) (shouldSkip bool, reason string) {
	for _, labelID := range msg.LabelIds {
		if labelID == "DRAFT" {
			return true, "draft"
		}
		if labelID == "SENT" {
			return true, "outgoing"
		}
	}
	return false, ""
}

func shouldSkipDuplicate(ctx context.Context, msgID string) (shouldSkip bool, location string) {
	if redis.RDB != nil {
		_, redisErr := redis.RDB.Get(ctx, fmt.Sprintf("email-external-id-%s", msgID)).Int()
		if redisErr == nil {
			log.Info(ctx, "skipping duplicate message found in redis", zap.String("duplicateMsg", msgID))
			return true, "redis"
		}

		log.ErrorNoSentry(
			ctx,
			"error checking if msgID is in redis - falling back to RDS",
			zap.Error(redisErr),
			zap.String("msgID", msgID),
		)
	} else {
		log.Warn(ctx, "redis is not connected, falling back to RDS to check for duplicates")
	}

	// Fallback to RDS check
	_, rdsErr := getEmailFunc(ctx, msgID)
	if rdsErr == nil {
		log.Info(ctx, "skipping duplicate message found in RDS", zap.String("duplicateMsg", msgID))
		return true, "rds"
	} else if !errors.Is(rdsErr, gorm.ErrRecordNotFound) {
		log.ErrorNoSentry(ctx, "error querying email in RDS - processing message anyway", zap.Error(rdsErr))
	}

	return false, ""
}
