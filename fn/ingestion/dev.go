package ingestion

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/sentry"
	"github.com/drumkitai/drumkit/fn/ingestion/gmail/env"
)

// For dev; replay emails from S3
// `out` should be either *gmail.Message or *msclient.Message
func DownloadFromS3(ctx context.Context, key string, out any) error {
	if out == nil {
		return errors.New("you want me to get a message from S3, but you didn't provide an `out` param :(")
	}

	cfg, err := config.LoadDefaultConfig(ctx, config.WithRegion("us-east-1"))
	if err != nil {
		return fmt.Errorf("failed to create S3 client: %w", err)
	}
	s3Client := s3.NewFromConfig(cfg)

	const bucket = "axle-beacon-ingestion"
	log.Info(ctx, "downloading from s3", zap.String("bucket", bucket), zap.String("key", key))
	resp, err := s3Client.GetObject(ctx, &s3.GetObjectInput{
		Bucket: aws.String(bucket),
		Key:    aws.String(key),
	})
	if err != nil {
		return fmt.Errorf("failed to GetObject from s3: %w", err)
	}

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read S3 response body: %w", err)
	}

	if err := json.Unmarshal(data, out); err != nil {
		return fmt.Errorf("json.Unmarshal into out failed: %w", err)
	}

	return nil
}

// Send messages to beacon-processor instead of SQS when running locally
type LocalSQSClient struct{}

func (c *LocalSQSClient) SendMessage(
	ctx context.Context,
	input *sqs.SendMessageInput,
	_ ...func(*sqs.Options),
) (*sqs.SendMessageOutput, error) {

	// POST to beacon-processor running locally on a different port
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		fmt.Sprintf("http://%s:5005/invoke", env.Vars.DBHost),
		strings.NewReader(aws.ToString(input.MessageBody)),
	)
	if err != nil {
		return nil, fmt.Errorf("unable to create local POST request: %w", err)
	}

	// Microsoft Graph will retry the webhook request if it doesn't receive a 2XX response within 10 seconds.
	// To avoid spam, particularly for emails that take longer to process like load building & truck lists,
	// we asynchronously send the request to processor. https://tinyurl.com/mrxapsma
	go sentry.WithHub(ctx, func(ctx context.Context) {
		asyncCtx := log.InheritContext(ctx, context.Background())
		asyncCtx, cancel := context.WithTimeout(asyncCtx, 5*time.Minute)
		defer cancel()

		resp, err := otel.TracingHTTPClient().Do(req)
		if err != nil {
			log.Error(asyncCtx, "failed to send POST request to "+req.URL.String(), zap.Error(err))
			return
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			log.Error(asyncCtx, fmt.Sprintf("POST %s returned %d", req.URL.String(), resp.StatusCode))
		}
	})

	return &sqs.SendMessageOutput{}, nil
}
