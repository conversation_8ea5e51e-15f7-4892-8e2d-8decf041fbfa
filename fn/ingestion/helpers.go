package ingestion

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type EnqueueBackfillOptions struct {
	StartTime      *time.Time
	EndTime        *time.Time
	Hours          int
	IsContinuation bool
}

type EnqueueBackfillOption func(*EnqueueBackfillOptions)

func WithTimeWindow(start, end time.Time) EnqueueBackfillOption {
	return func(o *EnqueueBackfillOptions) {
		o.StartTime = &start
		o.EndTime = &end
	}
}

func WithBackfillHours(hours int) EnqueueBackfillOption {
	return func(o *EnqueueBackfillOptions) {
		o.Hours = hours
	}
}

func WithContinuation() EnqueueBackfillOption {
	return func(o *EnqueueBackfillOptions) {
		o.IsContinuation = true
	}
}

func EnqueueBackfillJob(
	ctx context.Context,
	sqsClient SQSAPI,
	backfillSQSQueueURL string,
	provider string,
	user *models.User,
	opts ...EnqueueBackfillOption,
) (err error) {

	ctx, span := otel.StartSpan(ctx, "EnqueueBackfillJob", nil)
	defer func() { span.End(err) }()

	options := &EnqueueBackfillOptions{}
	for _, opt := range opts {
		opt(options)
	}

	var startTime, endTime time.Time

	// Determine the time window for the backfill
	switch {
	case options.StartTime != nil && options.EndTime != nil:
		// A specific time window is provided for a remainder backfill
		startTime = *options.StartTime
		endTime = *options.EndTime
	case options.Hours > 0:
		// A duration in hours is provided for an initial backfill
		endTime = time.Now()
		startTime = endTime.Add(-time.Duration(options.Hours) * time.Hour)

		// If the time window is less than a day, Gmail's date-only query format
		// might not return all expected results. We adjust to ensure at least a one-day window.
		if endTime.Sub(startTime) < 24*time.Hour {
			// Set the window to be a full 24 hours from the start time.
			endTime = startTime.Add(24 * time.Hour)
		}
	default:
		err = errors.New("must provide either a time window or backfill hours")
		return
	}

	payload := models.BackfillJobPayload{
		UserID:         user.ID,
		Provider:       provider,
		StartTime:      &startTime,
		EndTime:        &endTime,
		IsContinuation: options.IsContinuation,
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		err = fmt.Errorf("error marshaling backfill job payload: %w", err)
		return
	}

	_, err = sqsClient.SendMessage(ctx, &sqs.SendMessageInput{
		QueueUrl:    aws.String(backfillSQSQueueURL),
		MessageBody: aws.String(string(payloadBytes)),
	})

	if err != nil {
		err = fmt.Errorf("error queueing backfill (%s) job for new user %s: %w", provider, user.EmailAddress, err)
		return
	}

	return nil
}

func ShouldBackfill(ctx context.Context, user *models.User) bool {
	duration := time.Since(user.BackfillStartTime.Time)
	// A backfill started but due to an error (i.e. timeout), the lock was never removed so we should try again.
	// Duration is based on Lambda timeout configuration
	isLongTime := !user.BackfillStartTime.Time.IsZero() && duration > 10*time.Minute

	if isLongTime {
		log.WarnNoSentry(ctx, "backfill lock set too long", zap.Duration("duration", duration))
	}

	return user.BackfillStartTime.Time.IsZero() || isLongTime
}
