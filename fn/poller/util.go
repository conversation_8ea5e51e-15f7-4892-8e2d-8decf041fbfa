package main

import (
	"context"
	"fmt"

	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

var (
	// For unit testing
	dbFuncListFreightTrackingIDs                         = loadDB.ListFreightTrackingIDs
	dbFuncListExternalTMSIDs                             = loadDB.ListExternalTMSIDs
	dbFuncFindLoadIDsEmptyCarrierCost                    = loadDB.FindLoadIDsWithEmptyCarrierCost
	dbFuncFindRecentlyUpdatedLoadIDs                     = loadDB.FindRecentlyUpdatedLoadIDs
	dbFuncFindLoadIDsWithEmptyCarrierCostAndRecentPickup = loadDB.FindLoadIDsWithEmptyCarrierCostAndRecentPickup
)

type GetSetFunc func(ctx context.Context, integrationID uint) (map[string]struct{}, error)

// GetFreightTrackingIDSet fetches all freight tracking IDs for a given TMS integration ID from the database.
func GetFreightTrackingIDSet(ctx context.Context, integrationID uint) (map[string]struct{}, error) {
	freightTrackingIDs, err := dbFuncListFreightTrackingIDs(ctx, integrationID)
	if err != nil {
		return nil, fmt.Errorf("error getting list of IDs: %w", err)
	}

	set := make(map[string]struct{}, len(freightTrackingIDs))
	for _, freightTrackingID := range freightTrackingIDs {
		set[freightTrackingID] = struct{}{}
	}

	return set, nil
}

func GetExternalTMSIDSet(ctx context.Context, integrationID uint) (map[string]struct{}, error) {
	freightTrackingIDs, err := dbFuncListExternalTMSIDs(ctx, integrationID)
	if err != nil {
		return nil, fmt.Errorf("error getting list of IDs: %w", err)
	}

	set := make(map[string]struct{}, len(freightTrackingIDs))
	for _, freightTrackingID := range freightTrackingIDs {
		set[freightTrackingID] = struct{}{}
	}

	return set, nil
}

// GetLoadsWithEmptyCarrierCost returns a set of freight_tracking_ids from the database for loads
// that don't have ratedata_carrier_cost populated
func GetLoadsWithEmptyCarrierCost(
	ctx context.Context,
	tmsID uint,
	tmsName models.IntegrationName,
) (map[string]struct{}, error) {
	result := make(map[string]struct{})

	dbLoadsWithEmptyCarrierCostFunc := dbFuncFindLoadIDsEmptyCarrierCost

	// Note: GlobalTranzTMS doesn't allow for recently-updated load filters, so instead we keep pinging loads
	// that still need a carrier cost update until they get stale (outside the Lane History 4-week window)
	if tmsName == models.GlobalTranzTMS {
		dbLoadsWithEmptyCarrierCostFunc = dbFuncFindLoadIDsWithEmptyCarrierCostAndRecentPickup
	}

	loadIDs, err := dbLoadsWithEmptyCarrierCostFunc(ctx, tmsID)
	if err != nil {
		return nil, fmt.Errorf("error finding loads with empty carrier cost: %w", err)
	}

	for _, id := range loadIDs {
		if id != "" {
			result[id] = struct{}{}
		}
	}

	return result, nil
}

// GetRecentlyUpdatedLoadIDSet returns a set of freight_tracking_ids for loads
// that were updated within the given timeframe (in minutes)
func GetRecentlyUpdatedLoadIDSet(ctx context.Context, tmsID uint, minutes int) (map[string]struct{}, error) {
	result := make(map[string]struct{})

	loadIDs, err := dbFuncFindRecentlyUpdatedLoadIDs(ctx, tmsID, minutes)
	if err != nil {
		return nil, fmt.Errorf("error finding recently updated loads: %w", err)
	}

	for _, id := range loadIDs {
		if id != "" {
			result[id] = struct{}{}
		}
	}

	return result, nil
}
