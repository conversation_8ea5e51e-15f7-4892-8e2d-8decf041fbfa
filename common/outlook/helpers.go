package outlookhelpers

import (
	"context"
	"encoding/base64"
	"fmt"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/sqs"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/emails"
	"github.com/drumkitai/drumkit/common/helpers/jsoncfg"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	helpers "github.com/drumkitai/drumkit/fn/ingestion"
)

func ProcessAttachments(
	ctx context.Context,
	client msclient.Client,
	account,
	msgID string,
	s3Uploader s3backup.Archiver,
) (res []models.Attachment, hasPDFs bool, err error) {

	attachments, err := client.GetMessageAttachmentsByID(ctx, msgID)
	if err != nil {
		return nil, hasPDFs, fmt.Errorf("failed to get attachments: %w", err)
	}

	if len(attachments) == 0 {
		log.Info(ctx, "No attachments found for message", zap.String("msgID", msgID))
		return nil, hasPDFs, nil
	}

	for _, attachment := range attachments {
		if attachment.ODataType != "#microsoft.graph.fileAttachment" {
			continue
		}

		ctx = log.With(
			ctx,
			zap.String("attachmentName", attachment.Name),
			zap.String("attachmentType", attachment.ContentType),
		)

		// LLM Load Building and Quote Request pipelines currently only support PDFs for now
		if strings.Contains(strings.ToLower(attachment.ContentType), "pdf") {
			hasPDFs = true
		}

		data, err := base64.StdEncoding.DecodeString(attachment.ContentBytes)
		if err != nil {
			log.Error(ctx, "Failed to decode attachment content", zap.Error(err))
			continue
		}

		// S3 Cost Optimization: LLM Load Building and Quote Request pipelines currently only support PDFs for now
		// Store other attachments' metadata for displaying on FE
		var s3URL string
		if strings.Contains(strings.ToLower(attachment.ContentType), "pdf") {
			s3URL, err = s3Uploader.Attachment(ctx, models.Outlook, account, msgID, attachment.Name, data)
			if err != nil {
				log.Error(ctx, "Failed to upload attachment to S3", zap.Error(err))
			} else {
				log.Info(ctx, "Uploaded attachment to S3")
			}
		}

		res = append(res, models.Attachment{
			MessageExternalID:   msgID,
			ExternalID:          attachment.ID,
			MimeType:            attachment.ContentType,
			IsInline:            attachment.IsInline,
			OriginalFileName:    attachment.Name,
			TransformedFileName: s3backup.SanitizeFileName(attachment.Name),
			S3URL:               s3URL,
		})

	}

	return res, hasPDFs, nil
}

// ProcessAndEnqueueOutlookMessage encapsulates the common logic for processing a single Outlook message
// and enqueueing it for downstream processing. It fetches full message details, processes attachments,
// archives the message to S3, and sends a payload to an SQS queue.
func ProcessAndEnqueueOutlookMessage(
	ctx context.Context,
	client msclient.Client,
	sqsClient helpers.SQSAPI,
	s3Uploader s3backup.Archiver,
	user *models.User,
	service *models.Service,
	msgSummary msclient.Message,
	processorQueueURL string,
) error {
	ctx = log.With(
		ctx,
		zap.String("msgExternalId", msgSummary.ID),
		zap.String("msgThreadId", msgSummary.ConversationID),
		zap.String("msgSubject", msgSummary.Subject),
	)

	// Get the full message details.
	msg, err := client.GetMessageByID(
		ctx,
		msgSummary.ID,
		msclient.WithContentType(msclient.HTMLContentType),
	)
	if err != nil {
		if strings.Contains(err.Error(), "resource not found") {
			log.Info(
				ctx,
				"message not found, skipping",
				zap.String("messageId", msgSummary.ID),
			)
			return nil // Not an error, just skip.
		}
		// Return a fatal error to be handled by the caller.
		return fmt.Errorf("failed to get full message details: %w", err)
	}

	// Process attachments.
	var s3Attachments []models.Attachment
	var hasPDFs bool
	if emails.ShouldProcessAttachments(service) {
		s3Attachments, hasPDFs, err = ProcessAttachments(ctx, client, user.EmailAddress, msg.ID, s3Uploader)
		if err != nil {
			// Log the error but continue processing the email itself.
			if !strings.Contains(err.Error(), "resource not found") {
				log.Error(ctx, "failed to process attachments", zap.Error(err))
			}
		}
	}

	outlookMsg := emails.OutlookMessage{Message: &msg}

	// Prepare the payload for SQS.
	payload, err := emails.PrepareEmailPayload(
		ctx,
		&outlookMsg,
		emails.WithEmailAddress(user.EmailAddress),
		emails.WithUser(user),
		emails.WithS3URL("temp-s3-url"), // This will be replaced after S3 upload.
		emails.WithAttachments(s3Attachments),
		emails.WithHasPDFs(hasPDFs),
	)
	if err != nil {
		log.Error(ctx, "error creating email payload", zap.Error(err))
		return nil // Non-fatal, log and continue.
	}

	// Archive the full message payload to S3.
	var s3URL string
	if s3Uploader != nil {
		if s3URL, err = s3Uploader.Outlook(ctx, payload, user.EmailAddress); err != nil {
			// Non-fatal, S3 archiving failure should not block ingestion.
			log.Error(ctx, "s3 archive failed", zap.String("msgId", msg.ID), zap.Error(err))
		}
	}
	payload.S3URL = s3URL

	// Marshal and enqueue the payload.
	payloadBytes, err := jsoncfg.SpaceEfficientConfig.Marshal(payload)
	if err != nil {
		log.Error(ctx, "error marshalling email payload", zap.Error(err))
		return nil // Non-fatal, log and continue.
	}

	_, err = sqsClient.SendMessage(ctx, &sqs.SendMessageInput{
		QueueUrl:    aws.String(processorQueueURL),
		MessageBody: aws.String(string(payloadBytes)),
	})
	if err != nil {
		// This is potentially a more significant error (e.g., SQS permissions).
		// We log it and continue, but it might indicate a larger problem.
		log.Error(ctx, "error enqueueing email", zap.Error(err))
		return nil
	}

	log.Info(ctx, "successfully enqueued email for processing")
	return nil
}
