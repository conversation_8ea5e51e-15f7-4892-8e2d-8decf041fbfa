package errtypes

import (
	"errors"
	"fmt"
	"net/http"
	"strings"

	"github.com/drumkitai/drumkit/common/helpers/scrub"
	"github.com/drumkitai/drumkit/common/models"
)

// HTTPResponseError is returned for a non-2xx status response from an HTTP call.
// Harmonizes bespoke, non-REST API conformant integrations.
type HTTPResponseError struct {
	IntegrationName models.IntegrationName
	IntegrationType models.IntegrationType
	AxleTSPID       uint
	ServiceID       uint
	// If true, shows ResponseBody to user; helpful for dynamic validation errors that TMS throws upon submission.
	// Caller is responsible for ensuring ResponseBody is modified to be user-friendly.
	// (see ../integrations/tms/mcleodenterprise/load.go:504-510 for example)
	IsUserFacing bool

	HTTPMethod string
	URL        string

	StatusCode      int
	ResponseHeaders http.Header
	ResponseBody    []byte
}

func (e HTTPResponseError) Error() string {
	return fmt.Sprintf("%s %s %s returned %d: %s",
		e.<PERSON><PERSON><PERSON>, e.HTTPMethod, e.<PERSON><PERSON>, e.<PERSON>Code, e.ResponseBody)
}

// UserFacingErrorString returns HTTPResponseError string representation.
// NOTE: This function returns the integration's response body, so when building integrations it's important
// to verify and revise the body to be user-friendly before returning it to the user
// For example, if a TMS always returns an error in a JSON object, then build a package utility function to extract the
// plaintext error and re-assign it to httpErr.ResponseBody, then wrap it with NewUserFacingError(httpErr).
// Example:
func (e HTTPResponseError) UserFacingErrorString() string {
	name := models.FormatIntegrationName(e.IntegrationName)
	body := string(e.ResponseBody)

	if strings.TrimSpace(body) != "" {
		return fmt.Sprintf("%s returned an error: %s", name, body)
	}

	statusText := http.StatusText(e.StatusCode)
	// If statusText is empty, then the status code is invalid or custom like 599 for context timeouts
	if statusText == "" {
		return name + " returned an error"
	}

	return name + " returned an error: " + statusText

}

func (e HTTPResponseError) Wrap(msg string) error {
	// We want to preserve the original error message and add the outer context to it
	if e.ResponseBody != nil {
		e.ResponseBody = []byte(msg + ": " + string(e.ResponseBody))
	} else {
		e.ResponseBody = []byte(msg)
	}

	return e
}

// Verify that HTTPResponseError implements UserFacingErrorStringer
var _ UserFacingErrorStringer = HTTPResponseError{}

// NewHTTPResponseError constructs an error based on a single HTTP request + response.
func NewHTTPResponseError(integ models.Integration, req *http.Request, resp *http.Response, responseBody []byte) error {
	// Note: the HTTP response body can only be read once, so we force the caller to pass the body in

	return HTTPResponseError{
		IntegrationName: integ.Name,
		AxleTSPID:       integ.ID,
		ServiceID:       integ.ServiceID,
		HTTPMethod:      req.Method,
		URL:             scrub.URL(req.URL).String(),
		StatusCode:      resp.StatusCode,
		ResponseHeaders: resp.Header,
		ResponseBody:    responseBody,
	}
}

// EntityNotFoundError returns a HTTPResponseError for 404 not found.
// idName is optional but can be helpful to provide additional context.
func EntityNotFoundError(integ models.Integration, externalID string, idName string) error {
	url := externalID
	if idName != "" {
		url = fmt.Sprintf("%s=%s", idName, externalID)
	}

	return HTTPResponseError{
		IntegrationName: integ.Name,
		AxleTSPID:       integ.ID,
		ServiceID:       integ.ServiceID,
		HTTPMethod:      http.MethodGet,
		URL:             url,
		StatusCode:      http.StatusNotFound,
		ResponseBody:    []byte(fmt.Sprintf("%s %s not found", idName, externalID)),
	}
}

// DisabledIntegrationError returns a HTTPResponseError for 406 Not Acceptable.
// This likely represents an integration that had it's credentials updated, so turned the integration off
// in order prevent locking the account.
func DisabledIntegrationError(integ models.Integration) error {
	return HTTPResponseError{
		IntegrationName: integ.Name,
		IntegrationType: integ.Type,
		ServiceID:       integ.ServiceID,
		StatusCode:      http.StatusNotAcceptable,
	}
}

// NotFoundError represents a custom "Not Found" error type.
// For example, when a load does not exist, Aljex returns 200 instead of 404 but with "invalid PRO" msg in the body.
// This custom type is for such cases.
func IsEntityNotFoundError(err error) bool {
	var httpErr HTTPResponseError
	return errors.As(err, &httpErr) && httpErr.StatusCode == http.StatusNotFound
}

// To use when integrations don't implement certain methods of an interface.
// Helpful for filtering out from Sentry
type NotImplementedError struct {
	IntegrationName models.IntegrationName
	Function        string
}

func (e NotImplementedError) Error() string {
	return e.Function + " is not available for " + string(e.IntegrationName)
}

func NotImplemented(intName models.IntegrationName, function string) error {
	return NotImplementedError{IntegrationName: intName, Function: function}
}

func IsNotImplementedError(err error) bool {
	var notImplErr NotImplementedError
	return errors.As(err, &notImplErr)
}

// UserFacingError is an error that is returned to the user,
// e.g. TMS input validation errors.
// Drumkit API returns this in 'Message' field of the response body JSON.
type UserFacingError struct {
	err error
}

// If an error implements UserFacingErrorStringer, then the error's UserFacingErrorString() is returned.
// Otherwise, the error's Error() is returned.
// Helpful for separating internal error strings from user-facing error strings,
// e.g. HTTPResponseError.Error() returns:
//
//	fmt.Sprintf("%s %s %s returned %d: %s",
//	e.IntegrationName, e.HTTPMethod, e.URL, e.StatusCode, e.ResponseBody)
//
// whereas its UserFacingErrorString() returns the more simple:
//
//	fmt.Sprintf("%s returned an error: %s", e.IntegrationName, e.ResponseBody)
type UserFacingErrorStringer interface {
	UserFacingErrorString() string
	Wrap(msg string) error
}

// NewUserFacingError wraps an error to be user facing.
// NOTE: When building integrations, it's important to verify and revise TMS error responses to be user-friendly
// before returning them to the user.
// For example, if a TMS always returns an error in a JSON struct, then build a utility function to extract the
// error plaintext and return that to the user, not the whole error JSON object.
// Example:
func NewUserFacingError(err error) error {
	return UserFacingError{err: err}
}

// UserFacingErrorString returns the error's string representation.
// If the error implements UserFacingErrorStringer, then UserFacingErrorString() is returned.
// Otherwise, Error() is returned.
func (e UserFacingError) Error() string {
	var baseErr UserFacingErrorStringer
	if errors.As(e.err, &baseErr) {
		return baseErr.UserFacingErrorString()
	}

	str := strings.TrimSpace(e.err.Error())
	if len(str) < 2 {
		return str
	}

	return strings.ToUpper(string(str[0])) + strings.ToLower(str[1:])
}

func (e UserFacingError) Unwrap() error {
	return e.err
}

// WrapNewUserFacingError wraps an error with an outer message, similar to fmt.Errorf().
// This is useful when an error is returned from a nested operation and should be surfaced to the user with context
// on the sub-operation that failed.
// Example: Mcleod CreateLoad() has multiple, nested steps that can fail.
// Correct usage:
//
//	 func (m *McleodEnterprise) CreateLoad(ctx context.Context, reqLoad models.Load) (resp Load, err error) {
//
//		err := createPickupLocation(ctx, reqLoad)
//		if err != nil {
//			return errtypes.WrapNewUserFacingError(fmt.Errorf("error creating pickup location: %w", err))
//		}
//
// User sees: "Mcleod returned an error: error creating pickup location: duplicate location name"
// Incorrect:
//
//	 func (m *McleodEnterprise) CreateLoad(ctx context.Context, reqLoad models.Load) (resp Load, err error) {
//
//		err := createPickupLocation()
//		if err != nil {
//			return errtypes.NewUserFacingError(fmt.Errorf("error creating pickup location: %w", err))
//		}
//
// User sees: "`Error creating pickup location: mcleodenterprise post <long_url> returned 400: duplicate location name`"
// Issues:
// 1) The outer "Mcleod returned an error" is dropped, 2) user isn't given info on which sub-operation failed,
// and 3) the error message is not user friendly as it contains the full URL and status code.
func WrapNewUserFacingError(msg string, err error) error {
	var ufErr UserFacingErrorStringer
	if errors.As(err, &ufErr) {
		// Preserves the UserFacingErrorStringer() in nested, wrapped error
		return NewUserFacingError(ufErr.Wrap(msg))
	}

	// Add special handling for other error types that implement UserFacingErrorStringer as needed

	return UserFacingError{err: fmt.Errorf("%s: %w", msg, err)}
}
