package api

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"

	"github.com/aws/aws-lambda-go/events"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/sentry"
)

// Utility functions for handling AWS API Gateway requests.
//
// NOTE: AWS API Gateway handlers may take only 0 - 2 arguments. Because of the variadic argument here,
// this utility function should be wrapped within a top-level function handler that conforms to AWS specs.
//
// OK:
//
//		func main() {
//		  lambda.Start(otellambda.InstrumentHandler(handlerWithLogging))
//		}
//
//		func handlerWithLogging(
//	    	ctx context.Context,
//	    	request *events.APIGatewayProxyRequest,
//		 	) (*events.APIGatewayProxyResponse, error) {
//
//	  	return api.GatewayHandler(ctx, request) // OR return api.GatewayHandler(ctx, request, api.WithPort("5005"))
//	}
//
// THIS WILL FAIL:
//
//	func main() {
//	  lambda.Start(otellambda.InstrumentHandler(api.GatewayHandler))
//	}
func GatewayHandler(
	ctx context.Context,
	request *events.APIGatewayProxyRequest,
	opts ...Option,
) (result *events.APIGatewayProxyResponse, err error) {

	sentry.WithHub(ctx, func(ctx context.Context) {
		ctx = log.NewFromEnv(ctx, zap.String("requestPath", request.Path))

		defer sentry.GetHubFromContext(ctx).Flush(sentry.FlushTimeout)
		// NOTE: Have all logs flushed before the application exits.
		//
		// THIS IS IMPORTANT: Without it, the logs will not be sent to Axiom as
		// the buffer will not be flushed when the application exits.
		defer log.Flush(ctx)

		log.Info(ctx, "received APIGatewayProxyRequest", zap.Any("request", RedactObj(request)))

		if result, err = handler(ctx, request, opts...); err != nil {
			return
		}

		log.Info(ctx, "returning APIGatewayProxyResponse", zap.Any("response", RedactObj(result)))

	})

	return
}

func handler(
	ctx context.Context,
	request *events.APIGatewayProxyRequest,
	opts ...Option,
) (*events.APIGatewayProxyResponse, error) {

	req, err := GatewayToHTTPRequest(ctx, request, opts...)
	if err != nil {
		log.Error(ctx, "api gateway to http request failed", zap.Error(err))
		return nil, err
	}

	log.Info(
		ctx,
		"invoking local fiber server",
		zap.String("method", req.Method),
		zap.String("url", req.URL.String()),
	)

	client := otel.TracingHTTPClient()
	resp, err := client.Do(req)
	if err != nil {
		log.Error(ctx, fmt.Sprintf("failed to send %s %s request", req.Method, req.URL.String()), zap.Error(err))
		return nil, fmt.Errorf("failed to send %s %s request: %w", req.Method, req.URL.String(), err)
	}
	defer resp.Body.Close()

	return HTTPResponseToGateway(resp)
}

// Convert an APIGatewayProxyRequest into a localhost HTTP request
func GatewayToHTTPRequest(
	ctx context.Context,
	request *events.APIGatewayProxyRequest,
	opts ...Option,
) (req *http.Request, err error) {

	options := &Options{
		Port: "5000",
	}

	for _, opt := range opts {
		opt(options)
	}

	localURL := url.URL{
		Scheme:   "http",
		Host:     "localhost:" + options.Port,
		RawPath:  request.Path,
		RawQuery: url.Values(request.MultiValueQueryStringParameters).Encode(),
	}

	localURL.Path, err = url.PathUnescape(request.Path)
	if err != nil {
		return nil, fmt.Errorf("error unescaping APIGatewayProxyRequest path: %w", err)
	}

	var reqBody io.Reader
	if request.Body != "" {
		reqBody = strings.NewReader(request.Body)
	}

	req, err = http.NewRequestWithContext(ctx, request.HTTPMethod, localURL.String(), reqBody)
	if err != nil {
		return nil, fmt.Errorf("failed to build HTTP %s request: %w", request.HTTPMethod, err)
	}

	req.Header = request.MultiValueHeaders

	return req, nil
}

// Convert a localhost HTTP response to an APIGatewayProxyResponse
func HTTPResponseToGateway(resp *http.Response) (*events.APIGatewayProxyResponse, error) {
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	return &events.APIGatewayProxyResponse{
		Body:              string(body),
		MultiValueHeaders: resp.Header,
		StatusCode:        resp.StatusCode,
	}, nil
}

func IsLambda() bool {
	return os.Getenv("LAMBDA_TASK_ROOT") != ""
}
