package helpers

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/drumkitai/drumkit/common/models"
)

func TestOr(t *testing.T) {
	t.Run("Or with strings", func(t *testing.T) {
		result := Or("", "default")
		assert.Equal(t, "default", result)

		result = Or("non-empty", "default")
		assert.Equal(t, "non-empty", result)
	})

	t.Run("Or with integers", func(t *testing.T) {
		result := Or(0, 10)
		assert.Equal(t, 10, result)

		result = Or(5, 10)
		assert.Equal(t, 5, result)
	})

	t.Run("Or with booleans", func(t *testing.T) {
		result := Or(false, true)
		assert.Equal(t, true, result)

		result = Or(true, false)
		assert.Equal(t, true, result)
	})

	t.Run("Or with floats", func(t *testing.T) {
		result := Or(0.0, 3.14)
		assert.Equal(t, 3.14, result)

		result = Or(2.71, 3.14)
		assert.Equal(t, 2.71, result)
	})
}

func TestOrNullTime(t *testing.T) {
	someTime1 := time.Date(2025, time.February, 14, 8, 0, 0, 0, time.UTC)
	someTime2 := time.Date(2025, time.January, 8, 13, 0, 0, 0, time.UTC)
	zeroTime := time.Time{}

	t.Run("First value is valid", func(t *testing.T) {
		a := models.NullTime{Valid: true, Time: someTime1}
		b := models.NullTime{Valid: true, Time: someTime2}

		result := OrNullTime(a, b)

		require.True(t, result.Valid)
		assert.Equal(t, a.Time, result.Time)
	})

	t.Run("Second value is valid", func(t *testing.T) {
		a := models.NullTime{Valid: false, Time: zeroTime}
		b := models.NullTime{Valid: true, Time: someTime2}

		result := OrNullTime(a, b)

		require.True(t, result.Valid)
		assert.Equal(t, b.Time, result.Time)
	})

	t.Run("Both values are not valid and zero", func(t *testing.T) {
		a := models.NullTime{Valid: false, Time: zeroTime}
		b := models.NullTime{Valid: false, Time: zeroTime}

		result := OrNullTime(a, b)

		require.False(t, result.Valid)
		assert.Equal(t, zeroTime, result.Time)
	})

	t.Run("Both values are not valid but non-zero", func(t *testing.T) {
		a := models.NullTime{Valid: false, Time: someTime1}
		b := models.NullTime{Valid: false, Time: someTime2}

		result := OrNullTime(a, b)

		require.False(t, result.Valid)
		assert.Equal(t, someTime2, result.Time)
	})

	t.Run("First value is not valid, second has zero-time but valid", func(t *testing.T) {
		a := models.NullTime{Valid: false, Time: zeroTime}
		b := models.NullTime{Valid: true, Time: zeroTime}

		result := OrNullTime(a, b)

		require.True(t, result.Valid)
		assert.Equal(t, zeroTime, result.Time)
	})
}
