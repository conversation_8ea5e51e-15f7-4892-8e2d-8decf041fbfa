package helpers

import (
	"strings"

	"golang.org/x/exp/constraints"

	"github.com/drumkitai/drumkit/common/models"
)

func Min[T constraints.Ordered](a, b T) T {
	if a < b {
		return a
	}
	return b
}

// Max returns the maximum of two values of an ordered type.
func Max[T constraints.Ordered](a, b T) T {
	if a > b {
		return a
	}
	return b
}

// Or returns the first non-zero value out of two provided values, similar to JS's ?? operator
func Or[T comparable](a, b T) T {
	var zero T
	if a != zero {
		return a
	}
	return b
}

// OrNullTime returns the first non-zero value out of two provided values, similar to JS's ?? operator.
// Defined separately from Or() because cases like `models.NullTime{Time: time.Time{}.In(someLoc), Valid: false}`
// where the struct was technically not entirely empty, `Or` would select that non-valid NullTime obj instead of
// the second, actually valid datetime object.
func OrNullTime(a, b models.NullTime) models.NullTime {
	if a.Valid {
		return a
	}
	return b
}

func Ternary[T any](cond bool, a, b T) T {
	if cond {
		return a
	}
	return b
}

func IsBlank(s string) bool {
	return strings.TrimSpace(s) == ""
}
