package braintrustutil

import (
	"context"
	"errors"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/integrations/llm"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// This file contains helper functions for normalizing quote request suggestion changes into llm QR models
// that are used for submitting braintrust logs. This is allows us to compare suggested and applied changes,
// score the initial LLM response, and submit that feedback to Braintrust.

// Quote Begin Conversation
func normalizeQuoteBeginConversation(obj models.QuoteLoadInfo) llm.QuoteRequestOutput {
	return llm.QuoteRequestOutput{
		QuoteRequests: []llm.QuoteRequest{
			{
				PickupLocation: llm.Address{
					City:  obj.PickupLocation.City,
					State: obj.PickupLocation.State,
					Zip:   obj.PickupLocation.Zip,
				},
				PickupDate: obj.PickupDate.Time.String(),
				DropoffLocation: llm.Address{
					City:  obj.DeliveryLocation.City,
					State: obj.DeliveryLocation.State,
					Zip:   obj.DeliveryLocation.Zip,
				},
				DropoffDate: obj.DeliveryDate.Time.String(),
				TruckType:   string(obj.TransportType),
			},
		},
	}
}

func NormalizeQuoteBeginConversation(
	suggestion models.QuoteRequest,
) (suggested llm.QuoteRequestOutput, expected llm.QuoteRequestOutput) {
	return normalizeQuoteBeginConversation(suggestion.SuggestedRequest),
		normalizeQuoteBeginConversation(suggestion.AppliedRequest)
}

// Quote Customer
func normalizeQuoteCustomer(obj models.QuoteLoadInfo) llm.CustomerInfo {
	return llm.CustomerInfo{
		ShipperName:         obj.Customer.Name,
		OriginalSenderEmail: obj.Customer.Email,
	}
}

func NormalizeQuoteCustomer(
	suggestion models.QuoteRequest,
) (suggested llm.CustomerInfo, expected llm.CustomerInfo) {
	return normalizeQuoteCustomer(suggestion.SuggestedRequest),
		normalizeQuoteCustomer(suggestion.AppliedRequest)
}

func SubmitBraintrustQuoteRequestFeedback(ctx context.Context, suggestion models.QuoteRequest) error {
	if len(suggestion.BraintrustLogIDs) == 0 {
		return errors.New("no braintrust log records found for quote request suggestion")
	}

	var err error
	var expected, suggested any

	for _, logID := range suggestion.BraintrustLogIDs {

		switch logID.ProjectStepName {
		case string(braintrustsdk.QRBeginConversation):
			expected, suggested = NormalizeQuoteBeginConversation(suggestion)

		case string(braintrustsdk.QRGetCustomer):
			expected, suggested = NormalizeQuoteCustomer(suggestion)
		}

		err = braintrustsdk.SubmitFeedback(
			ctx,
			braintrustsdk.QuoteRequestProjectID,
			braintrustsdk.BuildFeedbackRequestBody(ctx, logID.ID, expected, suggested),
		)
		if err != nil {
			log.ErrorNoSentry(ctx, "error submitting feedback to Braintrust", zap.Error(err))
		}
	}

	return nil
}
