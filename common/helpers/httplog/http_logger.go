package httplog

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strconv"
	"strings"

	"go.uber.org/zap"
	"golang.org/x/oauth2"

	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	"github.com/drumkitai/drumkit/common/redis"
)

// CloudWatch metric filters generate custom metrics from this data.
type IntegrationResponseCode struct {
	AxleIntegrationID uint                   `json:"axleIntegrationID"`
	IntegrationName   models.IntegrationName `json:"integrationName"`
	IntegrationType   models.IntegrationType `json:"integrationType"`
	StatusCode        int                    `json:"statusCode"`
}

func redisUnauthorizedIntegrationAttemptsKey(integrationID uint) string {
	return fmt.Sprintf("integration-%d-unauthorized-attempts", integrationID)
}

func LogHTTPResponseCode(ctx context.Context, integration models.Integration, statusCode int) {

	// Ingestion mocks Gmail and Outlook clients as Integrations for logging, but they won't ever be disabled.
	if integration.Name != models.Gmail && integration.Name != models.Outlook {
		checkUnauthorizedAttempts(ctx, integration, statusCode)
	}

	body := GetHTTPLogJSON(ctx, integration, statusCode)
	if len(body) > 0 {
		//nolint:forbidigo // Needed so Cloudwatch can parse as a metric. It can't when it's a nested JSON object
		// in zap log
		fmt.Println(string(body))
	}
}

// For use in LogHTTPResponseCode and retryablehttp.Client.Logger
func GetHTTPLogJSON(ctx context.Context, integration models.Integration, statusCode int) (res []byte) {
	if !isLoggingEnabled() {
		return
	}

	if lower := strings.ToLower(string(integration.Name)); lower == "" || lower == "null" || lower == "none" {
		log.Error(ctx, fmt.Sprintf("invalid integration name '%s'", integration.Name))
		return
	}

	res, err := json.Marshal(IntegrationResponseCode{
		AxleIntegrationID: integration.ID,
		IntegrationType:   integration.Type,
		IntegrationName:   integration.Name,
		StatusCode:        statusCode},
	)
	if err != nil {
		log.Error(ctx, "jsonMarshal failed", zap.Error(err))
		return
	}

	return res
}

// Report an HTTP request which never reached the server (e.g. "i/o timeout" or "connection reset by peer").
// For clients that use oauth2.NewClient(), it checks for Oauth errors and logs that oauth endpoint's original
// status code instead of 599.
func LogHTTPRequestFailed(ctx context.Context, integration models.Integration, err error) {
	// Oauth client bubbles up oauth endpoint non-2xx in error object instead of in response.
	// Explicitly log those as 401s/403 because we want to know if oauth is continuously failing.
	var oauthError *oauth2.RetrieveError
	if errors.As(err, &oauthError) {
		LogHTTPResponseCode(ctx, integration, 401)
		return
	}

	// Use the non-standard status code 599 Network Connect Timeout Error,
	// which is unlikely to be used by any real integration and lets us distinguish this case
	log.Info(ctx, "integration error 599:", zap.Error(err))
	LogHTTPResponseCode(ctx, integration, 599)
}

// Disable integration logging during local development by default
func isLoggingEnabled() bool {
	return os.Getenv("APP_ENV") != "dev" ||
		strings.ToLower(os.Getenv("DEBUG")) == "true" ||
		os.Getenv("DEBUG") == "1"
}

// checkUnauthorizedAttempts caches 401 responses from integrations in Redis. If the number of consecutive 401s
// is 5 or more, the integration is disabled and a warning is sent to Sentry.
func checkUnauthorizedAttempts(ctx context.Context, integration models.Integration, statusCode int) {
	ctx = log.With(ctx,
		zap.String("integrationName", string(integration.Name)),
		zap.Uint("integrationID", integration.ID),
		zap.Uint("serviceID", integration.ServiceID),
		zap.Int("statusCode", statusCode))

	if redis.RDB == nil {
		msg := "redis RDB has not been initialized"

		if os.Getenv("APP_ENV") == "dev" {
			log.Debug(ctx, msg)
		} else {
			log.Warn(ctx, msg)
		}
		return
	}

	redisKey := redisUnauthorizedIntegrationAttemptsKey(integration.ID)

	unauthorizedAttemptsStr, redisErr := redis.RDB.Get(ctx, redisKey).Result()

	switch {
	case redisErr == nil:
		unauthorizedAttempts, err := strconv.Atoi(unauthorizedAttemptsStr)
		if err != nil {
			log.WarnNoSentry(ctx, "error parsing unauthorized attempts count", zap.Error(err))
		}

		if statusCode != 401 && unauthorizedAttempts > 0 {
			log.Info(ctx, "resetting unauthorized attempts for integration due to non-401 response")

			if err = redis.RDB.Set(ctx, redisKey, 0, 0).Err(); err != nil {
				logUnauthorizedCountWarnsNoSentry(
					ctx, "error resetting unauthorized integration attempt entry in redis", integration, err,
				)
			}
		} else if statusCode == 401 {
			// incrementing redis unauthorized attempts
			if err = redis.RDB.Incr(ctx, redisKey).Err(); err != nil {
				logUnauthorizedCountWarnsNoSentry(
					ctx, "error incrementing unauthorized integration attempt entry in redis", integration, err,
				)
			}

			if unauthorizedAttempts >= 4 { // pre-increment count, limit is 5 attempts

				// GlobalTranz has a higher threshold due to poller's parallel requests
				if integration.Name == models.GlobalTranz && unauthorizedAttempts < 50 {
					return
				}

				logUnauthorizedCountErrors(
					ctx, "disabling integration due to unauthorized status code response", integration, err,
				)

				if err := integrationDB.Disable(ctx, &integration); err != nil {
					logUnauthorizedCountErrors(
						ctx, "failed disabling integration due to 401 response", integration, err,
					)
				}

				log.Error(ctx, "disabled integration due to multiple unauthorized status code responses")
			}
		}
	case errors.Is(redisErr, redis.NilEntry):
		if statusCode == 401 {
			// creating redis entry with count at 1
			if err := redis.RDB.Set(ctx, redisKey, 1, 0).Err(); err != nil {
				logUnauthorizedCountWarnsNoSentry(
					ctx, "error creating unauthorized integration attempt entry in redis", integration, err,
				)
			}
		}

	default:
		logUnauthorizedCountWarnsNoSentry(
			ctx, "non nil-entry error while querying unauthorized attempts in redis", integration, redisErr,
		)
	}
}

func logUnauthorizedCountErrors(
	ctx context.Context,
	errMessage string,
	integration models.Integration,
	err error,
) {
	log.Error(
		ctx,
		errMessage,
		zap.String("integration name", string(integration.Name)),
		zap.String("integration username", integration.Username),
		zap.Uint("integration service id", integration.ServiceID),
		zap.Error(err),
	)
}

func logUnauthorizedCountWarnsNoSentry(
	ctx context.Context,
	errMessage string,
	integration models.Integration,
	err error,
) {
	log.WarnNoSentry(
		ctx,
		errMessage,
		zap.String("integration name", string(integration.Name)),
		zap.String("integration username", integration.Username),
		zap.Uint("integration service id", integration.ServiceID),
		zap.Error(err),
	)
}
