package emails

import (
	"context"
	"errors"
	"fmt"
	"slices"
	"strings"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/extractor"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

// Classify categorizes an email using the LLM and falls back to regex if the LLM fails at any point.
func Classify(
	ctx context.Context,
	email models.IngestedEmail,
	opts ...Option,
) ([]string, models.ClassificationApproach, string, string) {

	options := &Options{
		Service:    nil,
		EmailLoads: nil,
	}

	for _, opt := range opts {
		opt(options)
	}

	ctx = log.With(
		ctx,
		zap.String("externalId", email.ExternalID),
		zap.String("threadId", email.ThreadID),
		zap.String("account", email.Account),
		zap.String("subject", email.Subject),
	)

	emailContent := prepareEmailContent(ctx, email)

	openaiService, err := openai.NewService(ctx)
	if err != nil {
		log.Info(ctx, "failed to get OpenAI service, falling back to regex", zap.Error(err))

		return ClassifyWithRegex(ctx, email, options)
	}

	categories, categoryReasoning, previousResponseID, err := categorizeEmail(ctx, openaiService, email, emailContent)
	if err != nil {
		log.Info(ctx, "error categorizing email by LLM, falling back to regex", zap.Error(err))

		return ClassifyWithRegex(ctx, email, options)
	}

	log.Info(
		ctx,
		"LLM email categorization result",
		zap.Any("categories", categories),
		zap.String("category_reasoning", categoryReasoning),
	)

	var verifiedLabels []string
	var labelReasoning string
	var errCount int
	for category, isApplicable := range categories {
		if !isApplicable {
			continue
		}

		//nolint:lll
		categoryLabels, llmReasoning, err := getLabelsForCategory(ctx, openaiService, email, category, previousResponseID)
		if err != nil {
			log.Error(ctx, "error getting labels for category",
				zap.String("category", string(category)),
				zap.Error(err))
			errCount++

			continue
		}

		// Hardcoded fallback for notorious mislabelled quote request emails
		if category == QuotingCategory &&
			strings.Contains(strings.ToLower(email.Subject), "load bidding is available") &&
			!slices.Contains(categoryLabels, string(QuoteRequestLabel)) {
			categoryLabels = append(categoryLabels, string(QuoteRequestLabel))
		}

		verifiedLabels = append(verifiedLabels, categoryLabels...)
		labelReasoning = fmt.Sprintf("%s\n%s", labelReasoning, llmReasoning)
	}

	if len(verifiedLabels) == 0 {
		if errCount > 0 {
			log.WarnNoSentry(
				ctx,
				"error getting labels from LLM, falling back to regex",
			)

			return ClassifyWithRegex(ctx, email, options)
		}

		log.WarnNoSentry(
			ctx,
			"no labels from LLM, falling back to regex",
			zap.String("labelReasoning", labelReasoning),
			zap.Any("categories", categories),
		)

		return ClassifyWithRegex(ctx, email, options)
	}

	return verifiedLabels, models.LLMApproach, categoryReasoning, labelReasoning
}

// prepareEmailContent formats the email content for LLM analysis
func prepareEmailContent(ctx context.Context, email models.IngestedEmail) string {
	aggregatedAttachments := "Email has no attachments."
	if email.HasPDFs {
		aggregatedAttachments = aggregateAttachments(ctx, email.Attachments)
	}

	// Truncate the email content to 1,500 tokens
	emailContent := helpers.TokenTruncater(fmt.Sprintf(`
		*Email subject:*
		%s

		*Email body:*
		%s
	`, email.Subject, email.BodyWithoutSignature))

	return fmt.Sprintf(`
		%s

		*Attachments:*
		%s
	`, emailContent, aggregatedAttachments)
}

// categorizeEmail prompts the LLM to categorize the email
func categorizeEmail(
	ctx context.Context,
	openaiService openai.Service,
	ingestedEmail models.IngestedEmail,
	ingestedEmailContent string,
) (categories map[EmailCategory]bool, llmReasoning string, responseID string, err error) {

	response, err := openaiService.GetResponse(
		ctx,
		getEmailFromIngestedEmail(ingestedEmail),
		braintrustsdk.CreateProjectDetails(braintrustsdk.EmailClassification, false),
		openai.ResponseOptions{
			UserPrompt:      ingestedEmailContent,
			DeveloperPrompt: categoryClassificationPrompt,
			Schema:          extractor.GenerateSchema[CategorySchema](),
		},
	)
	if err != nil {
		return nil, "", "", fmt.Errorf("failed to categorize email: %w", err)
	}

	categoryResult, err := extractor.StructExtractor[CategorySchema](response.Content)
	if err != nil {
		return nil, "", response.ResponseID, fmt.Errorf("failed to extract category result: %w", err)
	}

	return map[EmailCategory]bool{
			TrackAndTraceCategory:      categoryResult.TrackAndTrace,
			SchedulingCategory:         categoryResult.Scheduling,
			QuotingCategory:            categoryResult.Quoting,
			CapacityManagementCategory: categoryResult.CapacityManagement,
			LoadBuildingCategory:       categoryResult.LoadBuilding,
		},
		categoryResult.Reasoning,
		response.ResponseID,
		nil
}

// getLabelsForCategory prompts the LLM to identify specific labels within a category
func getLabelsForCategory(
	ctx context.Context,
	openaiService openai.Service,
	email models.IngestedEmail,
	category EmailCategory,
	previousResponseID string,
) ([]string, string, error) {

	if category == QuotingCategory {
		return getLabelsForQuoting(ctx, email)
	}

	promptTemplate, exists := categorySpecificLabelPrompts[category]
	if !exists {
		return nil, "", fmt.Errorf("no prompt template for category: %s", category)
	}

	// Get the handler for this category
	handler, exists := categoryHandlers[category]
	if !exists {
		return nil, "", fmt.Errorf("unknown category: %s", category)
	}

	// Execute the appropriate handler
	return handler.extract(ctx, openaiService, email, category, previousResponseID, promptTemplate)
}

// getLabelsForSpecificCategory is a generic helper function that handles the common flow for all category
// types in the handler map.
func getLabelsForSpecificCategory[T ReasoningProvider](
	ctx context.Context,
	openaiService openai.Service,
	email models.IngestedEmail,
	category EmailCategory,
	previousResponseID string,
	promptTemplate string,
	mapFn func(T) map[EmailLabel]bool,
) ([]string, string, error) {

	response, err := openaiService.GetResponse(
		ctx,
		getEmailFromIngestedEmail(email),
		braintrustsdk.CreateProjectDetails(braintrustsdk.EmailCategoryLabelling, false),
		openai.ResponseOptions{
			PreviousResponseID: previousResponseID,
			DeveloperPrompt:    promptTemplate,
			Schema:             extractor.GenerateSchema[T](),
		},
	)
	if err != nil {
		return nil, "", fmt.Errorf("failed to get labels for category %s: %w", category, err)
	}

	labelResult, err := extractor.StructExtractor[T](response.Content)
	if err != nil {
		return nil, "", fmt.Errorf("failed to extract label result for category %s: %w", category, err)
	}

	categoryLabels := mapFn(labelResult)

	// Extract the relevant labels for the category
	var result []string
	for label, isTrue := range categoryLabels {
		if isTrue {
			result = append(result, string(label))
		}
	}

	return result, labelResult.GetReasoning(), nil
}

// getLabelsForQuoting is a helper function that handles the labelling flow for the quoting category using the service's
// feature flags, as those emails present a unique LLM challenge since all labels are pretty much indistinguishable.
func getLabelsForQuoting(ctx context.Context, email models.IngestedEmail) ([]string, string, error) {
	service, err := rds.GetServiceByID(ctx, email.ServiceID)
	if err != nil {
		errMsg := "failed to get service"
		if errors.Is(err, gorm.ErrRecordNotFound) {
			errMsg = "service not found for ingested email"
		}

		log.Error(ctx, errMsg, zap.Error(err))
		return nil, "", fmt.Errorf("failed to get service: %w", err)
	}

	var labels []string
	if service.IsQuickQuoteEnabled {
		labels = append(labels, string(QuoteRequestLabel))
	}

	carrierEmailInThread, carrierEmailInThreadErr := dbFuncGetEmailAndQuoteRequestByThreadID(ctx, email.ThreadID)
	if carrierEmailInThreadErr != nil {
		logMsg := "error getting associated quote request thread from DB"
		logFunc := log.Error

		if errors.Is(carrierEmailInThreadErr, gorm.ErrRecordNotFound) {
			logMsg = "email not associated with carrier network flow, skip carrier_quote label"
			logFunc = log.Info
		}

		logFunc(ctx, logMsg, zap.Error(carrierEmailInThreadErr))
		return labels, "", nil
	}

	if service.IsCarrierNetworkQuotingEnabled &&
		carrierEmailInThread != nil &&
		len(carrierEmailInThread.QuoteRequests) > 0 {
		labels = append(labels, string(CarrierQuoteResponseLabel))
	}

	return labels, "", nil
}

// aggregateAttachments processes and aggregates attachments into a single string for LLM classification.
// A maximum number of 1 page per attachment and a maximum of 3 attachments are processed.
// NOTE: This function should NOT be used in LLM pipelines because of the above constraints. For example,
// Trident load building emails often include 3+ shipments that are 2 pages long each.
func aggregateAttachments(ctx context.Context, attachments []models.Attachment) string {
	var builder strings.Builder

	// Only aggregate a maximum of 3 attachments to avoid overwhelming the LLM.
	counter := 0
	for _, attachment := range attachments {
		ctx = log.With(
			ctx,
			zap.String("attachmentName", attachment.OriginalFileName),
			zap.String("attachmentURL", attachment.S3URL),
		)

		if attachment.MimeType != "application/pdf" || attachment.S3URL == "" {
			continue
		}

		if counter >= 3 {
			break
		}

		// Fallback to using GoFitz conversion when Zerox fails
		markdown, err := extractor.S3PDFToMarkdown(ctx, attachment.S3URL, extractor.MarkdownOptions{NumPages: 1})
		if err != nil {
			log.Warn(ctx, "error converting PDF to markdown for classification", zap.Error(err))
			continue
		}

		// Truncate the markdown to 1,500 tokens
		markdown = helpers.TokenTruncater(markdown)

		counter++
		builder.WriteString(fmt.Sprintf("*%s:*\n%s\n\n", attachment.OriginalFileName, markdown))
	}

	return builder.String()
}

func getEmailFromIngestedEmail(ingestedEmail models.IngestedEmail) models.Email {
	return models.Email{
		Account:    ingestedEmail.Account,
		Subject:    ingestedEmail.Subject,
		ExternalID: ingestedEmail.ExternalID,
		ThreadID:   ingestedEmail.ThreadID,
		UserID:     ingestedEmail.UserID,
		ServiceID:  ingestedEmail.ServiceID,
	}
}

// GetAllLabelsForCategory returns all possible labels for a given category
func GetAllLabelsForCategory(category EmailCategory) []EmailLabel {
	switch category {
	case TrackAndTraceCategory:
		return []EmailLabel{
			CarrierInfoLabel,
			DeliveryConfirmationLabel,
			DriverInfoLabel,
			TrackingETALabel,
			PickupConfirmationLabel,
			CheckCallLabel,
		}
	case SchedulingCategory:
		return []EmailLabel{
			AppointmentSchedulingLabel,
			AppointmentConfirmedLabel,
		}
	case QuotingCategory:
		return []EmailLabel{
			CarrierQuoteResponseLabel,
			QuoteRequestLabel,
		}
	case CapacityManagementCategory:
		return []EmailLabel{
			TruckListLabel,
		}
	case LoadBuildingCategory:
		return []EmailLabel{
			LoadBuildingLabel,
		}
	default:
		return nil
	}
}

// GetAllLabels returns all possible labels across all categories
func GetAllLabels() []EmailLabel {
	var allLabels []EmailLabel
	for _, category := range []EmailCategory{
		TrackAndTraceCategory,
		SchedulingCategory,
		QuotingCategory,
		CapacityManagementCategory,
		LoadBuildingCategory,
	} {
		allLabels = append(allLabels, GetAllLabelsForCategory(category)...)
	}
	return allLabels
}
