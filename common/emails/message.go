package emails

import (
	"context"
	"strings"
	"time"

	"go.uber.org/zap"
	"google.golang.org/api/gmail/v1"

	"github.com/drumkitai/drumkit/common/integrations/email/frontclient"
	"github.com/drumkitai/drumkit/common/integrations/email/msclient"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	MessageInterface interface {
		GetID() string
		GetThreadID() string
		GetRFCMessageID() string
		GetInternalDate() time.Time
		GetThreadReferences() string
		GetInReplyTo() string
		GetSender() string
		GetRecipients() string
		GetCC() string
		GetSubject() string
		GetBody(internalPayload bool) (body string, bodyType models.EmailBodyType, isTruncated bool)
	}

	GmailMessage struct {
		*gmail.Message
	}

	OutlookMessage struct {
		*msclient.Message
	}

	FrontMessage struct {
		*frontclient.Message
	}
)

func (m *GmailMessage) GetID() string {
	return m.Id
}

func (m *GmailMessage) GetThreadID() string {
	return m.ThreadId
}

func (m *GmailMessage) GetRFCMessageID() string {
	return getSingleHeader(context.Background(), headerMap(m.Message), "message-id")
}

func (m *GmailMessage) GetInternalDate() time.Time {
	return time.UnixMilli(m.InternalDate)
}

func (m *GmailMessage) GetThreadReferences() string {
	return getSingleHeader(context.Background(), headerMap(m.Message), "references")
}

func (m *GmailMessage) GetInReplyTo() string {
	return getSingleHeader(context.Background(), headerMap(m.Message), "in-reply-to")
}

func (m *GmailMessage) GetSender() string {
	return addresses(context.Background(), headerMap(m.Message), "from")
}

func (m *GmailMessage) GetRecipients() string {
	return addresses(context.Background(), headerMap(m.Message), "to")
}

func (m *GmailMessage) GetCC() string {
	return addresses(context.Background(), headerMap(m.Message), "cc")
}

func (m *GmailMessage) GetSubject() string {
	return getSingleHeader(context.Background(), headerMap(m.Message), "subject")
}

// GetBody returns the body of the email message. If it's an internal payload, then we send the email over AWS SQS so
// we have to truncate the body to fit the size limit. If it's an external payload, then the customer self-hosts
// and forwards us the email via API.
func (m *GmailMessage) GetBody(internalPayload bool) (body string, bodyType models.EmailBodyType, truncated bool) {

	body, bodyType, err := plaintextOrMarkdown(context.Background(), m.Payload)
	if err != nil {
		return "", bodyType, truncated
	}

	if internalPayload {
		// The max SQS message size is 262144 bytes: truncate the body to 200KB or less
		const maxBodyLength = 200000
		truncated = len(body) > maxBodyLength

		if truncated {
			log.WarnNoSentry(
				context.Background(),
				"truncating email body",
				zap.String("msgId", m.GetID()),
				zap.Int("originalLength", len(body)),
				zap.Int("truncatedLength", maxBodyLength),
			)

			body = body[:maxBodyLength]
		}
	}

	return body, bodyType, truncated
}

func (m *OutlookMessage) GetID() string {
	return m.ID
}

func (m *OutlookMessage) GetThreadID() string {
	return m.ConversationID
}

func (m *OutlookMessage) GetRFCMessageID() string {
	return m.InternetMessageID
}

func (m *OutlookMessage) GetInternalDate() time.Time {
	return m.SentDateTime
}

func (m *OutlookMessage) GetThreadReferences() string {
	return ""
}

func (m *OutlookMessage) GetInReplyTo() string {
	return ""
}

func (m *OutlookMessage) GetSender() string {
	return m.Sender.EmailAddress.Address
}

func (m *OutlookMessage) GetRecipients() string {
	var recipients []string
	for _, r := range m.ToRecipients {
		recipients = append(recipients, r.EmailAddress.Address)
	}

	return strings.Join(recipients, ",")
}

func (m *OutlookMessage) GetCC() string {
	var cc []string
	for _, r := range m.CcRecipients {
		cc = append(cc, r.EmailAddress.Address)
	}

	return strings.Join(cc, ",")
}

func (m *OutlookMessage) GetSubject() string {
	return m.Subject
}

// GetBody returns the body of the email message. If it's an internal payload, then we send the email over AWS SQS so
// we have to truncate the body to fit the size limit. If it's an external payload, then the customer self-hosts
// and forwards us the email via API.
func (m *OutlookMessage) GetBody(internalPayload bool) (body string, bodyType models.EmailBodyType, truncated bool) {

	body, bodyType, err := plaintextOrMarkdown(context.Background(), m.Body)
	if err != nil {
		return "", bodyType, truncated
	}

	if internalPayload {
		// The max SQS message size is 262144 bytes: truncate the body to 200KB or less
		const maxBodyLength = 200000

		truncated = len(body) > maxBodyLength
		if truncated {
			log.WarnNoSentry(
				context.Background(),
				"truncating email body",
				zap.String("msgId", m.GetID()),
				zap.Int("originalLength", len(body)),
				zap.Int("truncatedLength", maxBodyLength),
			)

			body = body[:maxBodyLength]
		}
	}

	return body, bodyType, truncated
}

func (f *FrontMessage) GetID() string {
	return f.ID
}

func (f *FrontMessage) GetThreadID() string {
	if f.MetaLinks.Related["conversation"] == "" {
		return ""
	}

	urlParts := strings.Split(f.MetaLinks.Related["conversation"], "/")

	// The conversation ID is the last part of the URL
	return urlParts[len(urlParts)-1]
}

func (f *FrontMessage) GetRFCMessageID() string {
	if f.MetaLinks.Related["conversation"] == "" {
		return ""
	}

	urlParts := strings.Split(f.MetaLinks.Related["conversation"], "/")

	// The conversation ID is the last part of the URL
	return urlParts[len(urlParts)-1]
}

func (f *FrontMessage) GetInternalDate() time.Time {
	return time.Unix(int64(f.CreatedAt), 0)
}

func (f *FrontMessage) GetThreadReferences() string {
	return ""
}

func (f *FrontMessage) GetInReplyTo() string {
	return ""
}

func (f *FrontMessage) GetSender() string {
	for _, r := range f.Recipients {
		if r.Role == "from" {
			return r.Handle
		}
	}
	return ""
}

func (f *FrontMessage) GetRecipients() string {
	var recipients []string
	for _, r := range f.Recipients {
		if r.Role == "to" {
			recipients = append(recipients, r.Handle)
		}
	}

	return strings.Join(recipients, ",")
}

func (f *FrontMessage) GetCC() string {
	var cc []string
	for _, r := range f.Recipients {
		if r.Role == "cc" {
			cc = append(cc, r.Handle)
		}
	}

	return strings.Join(cc, ",")
}

func (f *FrontMessage) GetSubject() string {
	return f.Subject
}

func (f *FrontMessage) GetBody(internalPayload bool) (body string, bodyType models.EmailBodyType, truncated bool) {

	body, bodyType = f.Text, models.PlaintextEmailBodyType

	if internalPayload {
		// The max SQS message size is 262144 bytes: truncate the body to 200KB or less
		const maxBodyLength = 200000

		truncated = len(body) > maxBodyLength
		if truncated {
			log.WarnNoSentry(
				context.Background(),
				"truncating email body",
				zap.String("msgId", f.GetID()),
				zap.Int("originalLength", len(body)),
				zap.Int("truncatedLength", maxBodyLength),
			)

			body = body[:maxBodyLength]
		}
	}

	return body, bodyType, truncated
}
