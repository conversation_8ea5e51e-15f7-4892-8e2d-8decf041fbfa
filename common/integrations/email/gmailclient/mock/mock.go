// Package mem provides an in-memory implementation of Gmail client interface
package mem

import (
	"context"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"google.golang.org/api/gmail/v1"
	"google.golang.org/api/googleapi"

	"github.com/drumkitai/drumkit/common/integrations/email/gmailclient"
	"github.com/drumkitai/drumkit/common/models"
)

// Service is an in-memory implementation of the gmailclient.Client interface
type Service struct {
	Messages map[string]*gmail.Message

	// List of service calls that tests can verify
	Calls []string
}

func (s *Service) addCall(call string) {
	s.Calls = append(s.Calls, call)
}

func (s *Service) BatchGetMessages(_ context.Context, ids []string) ([]*gmail.Message, error) {
	sort.Strings(ids)
	s.addCall(fmt.Sprintf("mem.BatchGetMessages([%s])", strings.Join(ids, ", ")))

	results := make([]*gmail.Message, 0, len(ids))
	for _, id := range ids {
		results = append(results, s.Messages[id])
	}

	return results, nil
}

func (s *Service) GetMessage(_ context.Context, id string) (*gmail.Message, error) {
	s.addCall(fmt.Sprintf("mem.GetMessage(%s)", id))
	return s.Messages[id], nil
}

func (s *Service) ListHistory(_ context.Context, lastHistoryID uint64) (*gmail.ListHistoryResponse, error) {
	s.addCall(fmt.Sprintf("mem.ListHistory(%d)", lastHistoryID))

	if lastHistoryID == 9999999 {
		return nil, &googleapi.Error{Code: http.StatusNotFound}
	}

	history := make([]*gmail.History, 0, len(s.Messages))
	for _, msg := range s.Messages {
		history = append(history, &gmail.History{
			MessagesAdded: []*gmail.HistoryMessageAdded{
				{Message: msg},
			},
		})
	}

	return &gmail.ListHistoryResponse{HistoryId: lastHistoryID, History: history}, nil
}

func (s *Service) WatchInbox(context.Context, *gmail.WatchRequest) (*gmail.WatchResponse, error) {
	s.addCall("mem.WatchInbox()")
	return &gmail.WatchResponse{}, nil
}

func (s *Service) StopWatchingInbox(context.Context) error {
	s.addCall("mem.StopWatchingInbox()")
	return nil
}

func (s *Service) SendMessage(
	_ context.Context,
	_ *models.GeneratedEmail,
	_ string,
	_ []gmailclient.NewAttachment,
) (*gmail.Message, error) {
	s.addCall("mem.SendMessage()")
	return &gmail.Message{}, nil
}

func (s *Service) ListMessagesAfterDate(_ context.Context, t time.Time) (msgIDs []string, err error) {
	s.addCall(fmt.Sprintf("mem.ListMessagesAfterDate(%s)", t.Format(time.DateOnly)))
	for _, msg := range s.Messages {
		msgIDs = append(msgIDs, msg.Id)
	}

	return msgIDs, nil
}

func (s *Service) ListMessagesBetweenDates(
	_ context.Context,
	startDate time.Time,
	endDate time.Time,
) (msgIDs []string, err error) {
	s.addCall(
		fmt.Sprintf(
			"mem.ListMessagesBetweenDates(%s, %s)",
			startDate.Format(time.DateOnly),
			endDate.Format(time.DateOnly),
		),
	)
	for _, msg := range s.Messages {
		msgIDs = append(msgIDs, msg.Id)
	}

	return msgIDs, nil
}

func (s *Service) GetAttachment(context.Context, string, string) (*gmail.MessagePartBody, error) {
	s.addCall("mem.GetAttachment()")
	return &gmail.MessagePartBody{}, nil
}

func (s *Service) GetAuthenticatedUser() models.UserAccessor {
	s.addCall("mem.GetAuthenticatedUser()")
	return &models.User{EmailAddress: "<EMAIL>"}
}

func (s *Service) GetSignature(context.Context, string) (string, error) {
	s.addCall("mem.GetSignature()")
	//nolint:lll
	return `
	<div dir="ltr">
		<font color="#2196f3"><b>Drumkit TestUser</b></font><br>
		<div style="color:rgb(80,0,80)"><span style="color:rgb(76,175,80)"><b>Logistics Coordinator<br>A to Z Industries </b></span><span style="color:rgb(76,175,80)"><b><br></b></span><span style="color:rgb(33,150,243)"><a href="mailto:<EMAIL>" style="color:rgb(17,85,204)" target="_blank"><EMAIL></a></span><span style="color:rgb(158,158,158)"> </span></div>
		<div style="color:rgb(80,0,80)"><span style="color:rgb(17,17,17);font-family:Arial,&quot;Helvetica Neue&quot;,Helvetica,sans-serif;font-size:14px">(586)-250-2310</span></div>
		<div><b style="color:rgb(80,0,80)">24hr Shipment Support: 000-499-0000</b></div>
		<div><img data-aii="CiExLV9pT0RybmwtS0tiaWV2N1RtNkFheno0dmNnUmdPUmY" width="95" height="96" src="https://ci3.googleusercontent.com/mail-sig/AIorK4yxygfpYmLvhEOH2BDh6Yqd2YQKuXeR8Z1kWK2_fTPA3PHkXCLy0WsbKerVKK-o5MNBlE0ZGD4Xz09h" data-os="https://lh3.googleusercontent.com/d/1-_iODrnl-KKbiev7Tm6Aazz4vcgRgORf"><span style="color:rgb(158,158,158)"><br></span></div>
	</div>`,
		nil
}

func (s *Service) GetAliases(context.Context) ([]string, error) {
	return []string{s.GetAuthenticatedUser().GetEmailAddress()}, nil
}

func (s *Service) ForwardMessage(
	context.Context,
	string,
	*models.GeneratedEmail,
	bool,
	bool,
) (*gmail.Message, error) {
	s.addCall("mem.ForwardMessage()")
	return &gmail.Message{Id: "forwarded-message-id"}, nil
}
