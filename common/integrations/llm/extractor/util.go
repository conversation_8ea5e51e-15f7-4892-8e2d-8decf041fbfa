package extractor

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"regexp"
	"slices"
	"strings"

	md "github.com/<PERSON>mann/html-to-markdown"
	plugins "github.com/<PERSON><PERSON>/html-to-markdown/plugin"
	"github.com/gen2brain/go-fitz"
	"github.com/invopop/jsonschema"
	"github.com/microcosm-cc/bluemonday"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/s3fetcher"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	tmsLocationDB "github.com/drumkitai/drumkit/common/rds/tms_location"
)

var (
	TridentServiceID      = uint(562)
	TridentCustomers      = []string{"MJB", "ARGO"}
	FetchFreightCustomers = []string{
		// These are the same customer
		"Steel Equipment Specialists",
		"SES, LLC",
		"seseng",
	}
)

// extractDomain extracts the domain from an email address.
// e.g. "<EMAIL>" -> "example.com"
func extractDomain(email string) string {
	parts := strings.Split(email, "@")
	if len(parts) > 1 {
		return parts[1]
	}

	return ""
}

// ExtractSecondLevelDomain extracts the second-level domain from an email address or domain string.
// FYI, where "LD" stands for "level domain", format for domains is {3rdLD}.{2ndLD}.{1stLD(s)},
// e.g. app.drumkit.ai or app.company.co.uk. For these examples, function returns "drumkit" and "company".
// See tests for cases supported. If the domain is not recognized, returns "".
func ExtractSecondLevelDomain(input string) string {
	// Remove leading '@' if present, e.g. "@foo.com"
	input = strings.TrimPrefix(input, "@")

	// If input contains '@', extract the domain part
	parts := strings.Split(input, "@")
	domain := input
	if len(parts) == 2 {
		domain = parts[1]
	}

	domainParts := strings.Split(domain, ".")
	n := len(domainParts)

	if n < 2 {
		return ""
	}

	// Simple case: domain.com - return the first part
	if n == 2 {
		return domainParts[0]
	}

	// At this point, n > 2 and could be sub.domain.com, domain.co.uk, or sub.domain.co.uk, etc.
	// We'll use a heuristic based on common patterns:
	// 1. Most multi-part TLDs have exactly 2 parts (co.uk, com.au)
	// 2. Most multi-part TLDs have a short first part (2-3 chars like "co", "com", "org")

	secondToLastPart := domainParts[n-2]

	// If the second-to-last part is very short (2-3 chars), it's likely part of a multi-part TLD
	// Examples: co.uk, com.au, ac.uk, etc.
	if len(secondToLastPart) <= 3 && n >= 3 {
		// This is likely domain.co.uk pattern, return the part before the multi-part TLD
		return domainParts[n-3]
	}

	// Otherwise, it's likely a sub.domain.com pattern
	return domainParts[n-2]
}

// isInsufficientAddressData checks if the address data is insufficient to create a new load suggestion.
// NOTE: We must adhere to the common denominator between LB and QR suggestions,
// which is a minimum of zipcode OR city & state.
func IsInsufficientAddressData(addr models.CompanyCoreInfo) bool {
	if addr.Zipcode != "" {
		return false // Zipcode present = sufficient
	}
	return addr.City == "" || addr.State == "" // Need both city AND state if no zipcode
}

func ValidateStop(
	ctx context.Context,
	stopType string,
	stop models.CompanyCoreInfo,
	tmsID uint,
) models.CompanyCoreInfo {
	// Handle when LLM is silly
	if stop.AddressLine1 == fmt.Sprintf("%s, %s", stop.City, stop.State) {
		stop.AddressLine1 = ""
	}

	// NOTE: For Trident Mcleod Enterprise, pickup *is* required to have a TMS object ID
	if tmsID > 0 {
		mappedLocation, err := tmsLocationDB.GetLocationByAddress(ctx, tmsID, stop)
		if err == nil && mappedLocation.ID > 0 {
			return mappedLocation.CompanyCoreInfo
		}
		log.WarnNoSentry(
			ctx,
			fmt.Sprintf("unable to match %s location to TMS location by address", stopType),
			zap.Error(err),
		)
	}

	// If LLM parsed address line and city, assume it's correct
	if stop.AddressLine1 != "" {
		return stop
	}

	// New Load pipeline is responsible for both load building and quote requests
	// If LLM parsed only city/state and/or zip, we validate zip to avoid hallucinations
	validateZip(ctx, &stop)

	return stop
}

func validateZip(ctx context.Context, loc *models.CompanyCoreInfo) {
	if helpers.IsBlank(loc.Zipcode) {
		return // Zip is optional; assume that LLM parsed city, state correctly
	}

	location, err := helpers.AwsLocationLookup(ctx, "", "", loc.Zipcode)
	if err != nil {
		log.WarnNoSentry(ctx, "validateZip: error looking up zip", zap.Error(err))
		return
	}

	if len(location.Results) == 0 || location.Results[0].Place == nil {
		log.WarnNoSentry(ctx, "validateZip:no location found for zip", zap.String("zip", loc.Zipcode))
		// If no location found, clear zipcode to avoid hallucinations and fallback to city, state
		// If both pickup & dropoff are missing zipcode, caller will skip QR suggestion
		loc.Zipcode = ""

		return
	}

	placeRes := location.Results[0].Place
	if placeRes.Municipality != nil && *placeRes.Municipality != loc.City {
		log.WarnNoSentry(ctx, "LLM hallucinated city, using AWS result",
			zap.String("llmCity", loc.City),
			zap.String("awsCity", *placeRes.Municipality),
		)

		loc.City = *placeRes.Municipality
	}

	if placeRes.Region != nil {
		stateAbbrv := helpers.GetStateAbbreviation(ctx, *placeRes.Region)
		if stateAbbrv == "Unknown State" {
			stateAbbrv = loc.State
		}

		if loc.State != stateAbbrv {
			log.WarnNoSentry(ctx, "LLM hallucinated state, using AWS result",
				zap.String("llmState", loc.State),
				zap.String("awsState", *placeRes.Region),
			)
			loc.State = stateAbbrv
		}
	}
}

// MapCustomer maps the extracted customer name to a TMSCustomer from the database
// handling both email-based and attachment-based extraction scenarios
func MapCustomer(
	ctx context.Context,
	tmsID uint,
	parsedName string,
	userPrompt string,
) (mappedCustomer *models.TMSCustomer, err error) {

	if tmsID == 0 {
		return nil, errors.New("unable to map customer, TMS ID is 0")
	}

	var name string

	// Check email & attachment data for hard coded edge case customers
	edgeCaseCustomers := append(FetchFreightCustomers, TridentCustomers...) //nolint:gocritic

	for _, customer := range edgeCaseCustomers {
		check := checkForStringInUserPrompt(
			customer, userPrompt,
		)

		if check {
			switch customer {
			case "MJB":
				name = "Liberty Woods International"
			case "ARGO":
				name = "ARGO Fine Imports"
			case "Steel Equipment Specialists", "SES, LLC", "seseng":
				name = "SES"
			}
			break
		}
	}

	if name == "" {
		name = parsedName
	}

	// If we still don't have a name, we can't map to a customer
	if name == "" {
		return nil, errors.New("no customer name found for mapping")
	}

	dbCustomer, err := tmsCustomerDB.GetCustomerByName(ctx, tmsID, name)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			log.ErrorNoSentry(ctx, "customer not found in database", zap.String("name", name))
			return nil, nil
		}
		return nil, fmt.Errorf("db error getting customer: %w", err)
	}

	return &dbCustomer, nil
}

// validateTransportType normalizes and validates the given transport type.
// It converts the input to uppercase and matches it against known transport types.
// If the input doesn't match any known type, it defaults to VAN and logs a warning.
func validateTransportType(
	ctx context.Context,
	input models.TransportType,
	userPrompt string,
	config models.QuickQuoteConfig,
) models.TransportType {

	// Check for special equipment types in email body/attachment content if any
	for _, equipment := range config.SpecialEquipment {
		if check := checkForStringInUserPrompt(
			equipment, userPrompt,
		); check {
			return models.SpecialTransportType
		}
	}

	if helpers.IsBlank(string(input)) {
		return models.VanTransportType
	}

	validTypes := models.ListTransportTypes()
	for _, otherType := range config.OtherTransportTypes {
		// Add other transport types to validTypes if not already in validTypes
		if !slices.Contains(validTypes, models.TransportType(otherType)) {
			validTypes = append(validTypes, models.TransportType(otherType))
		}
	}

	normalizedInput := strings.ToUpper(string(input))
	normalizedInput = strings.Join(strings.Fields(normalizedInput), " ") // Remove extra spaces
	// Handle case where LLM input is "HOT SHOT" and validType is "HOTSHOT"
	normalizedInput = strings.ReplaceAll(normalizedInput, "HOT SHOT", "HOTSHOT")

	if slices.Contains(validTypes, models.TransportType(normalizedInput)) {
		return models.TransportType(normalizedInput)
	}

	// If no exact match, try partial matching
	for _, validType := range validTypes {
		if strings.Contains(normalizedInput, strings.ToUpper(string(validType))) ||
			// Handle case where LLM input is "BOX" and validType is "BOX TRUCK"
			strings.Contains(string(validType), normalizedInput) {
			return validType
		}
	}

	log.WarnNoSentry(ctx,
		"unknown transport type, defaulting to Van",
		zap.String("input", string(input)),
		zap.String("normalized", normalizedInput),
	)
	return models.VanTransportType
}

// checkForStringInUserPrompt checks if a string is present in the user prompt
func checkForStringInUserPrompt(
	str string,
	userPrompt string,
) bool {
	if userPrompt == "" || str == "" {
		return false
	}

	// do string matching with regex
	regex := regexp.MustCompile(fmt.Sprintf("(?i)\\b%s\\b", regexp.QuoteMeta(str)))
	return regex.MatchString(userPrompt)
}

// StructExtractor attempts to unmarshal a JSON response into a struct.
// If the response is not valid JSON, it will attempt to extract JSON from the response.
func StructExtractor[T any](response string) (T, error) {
	var result T

	// Try to unmarshal directly first
	err := json.Unmarshal([]byte(response), &result)
	if err != nil {
		// If direct unmarshal fails, try extracting JSON from the response
		jsonStr, err := ExtractJSONFromText(response)
		if err != nil {
			return result, fmt.Errorf("failed to extract JSON: %w", err)
		}

		err = json.Unmarshal([]byte(jsonStr), &result)
		if err != nil {
			return result, fmt.Errorf("failed to unmarshal JSON: %w", err)
		}
	}

	return result, nil
}

// ExtractJSONFromText attempts to extract JSON content from a potentially larger text block
func ExtractJSONFromText(text string) (string, error) {
	// First, clean up the text by trimming whitespace
	trimmedText := strings.TrimSpace(text)

	// Try to extract from code blocks first
	if jsonFromBlock, found := extractFromCodeBlock(trimmedText); found {
		return jsonFromBlock, nil
	}

	// Find the JSON start position and type
	startPos, startChar, endChar, err := findJSONStart(trimmedText)
	if err != nil {
		return "", err
	}

	// Extract the JSON content by finding the matching end character
	jsonContent, err := extractJSONContent(trimmedText, startPos, startChar, endChar)
	if err != nil {
		return "", err
	}

	// Validate the extracted JSON
	if err := validateJSON(jsonContent); err != nil {
		return "", err
	}

	return jsonContent, nil
}

// extractFromCodeBlock extracts JSON from markdown code blocks if present
func extractFromCodeBlock(text string) (string, bool) {
	if strings.HasPrefix(text, "```json") && strings.HasSuffix(text, "```") {
		// Extract the JSON content from the code block
		jsonContent := text[7:]                        // Skip the ```json prefix
		jsonContent = jsonContent[:len(jsonContent)-3] // Remove the closing ```
		return strings.TrimSpace(jsonContent), true
	}

	if strings.HasPrefix(text, "```") && strings.HasSuffix(text, "```") {
		// Generic code block
		jsonContent := text[3:]                        // Skip the ``` prefix
		jsonContent = jsonContent[:len(jsonContent)-3] // Remove the closing ```
		return strings.TrimSpace(jsonContent), true
	}

	return "", false
}

// findJSONStart locates the beginning of a JSON object or array in the text
func findJSONStart(text string) (startPos int, startChar byte, endChar byte, err error) {
	startIdxBrace := strings.Index(text, "{")
	startIdxBracket := strings.Index(text, "[")

	if startIdxBrace != -1 && (startIdxBracket == -1 || startIdxBrace < startIdxBracket) {
		// JSON object found
		return startIdxBrace, '{', '}', nil
	}

	if startIdxBracket != -1 {
		// JSON array found
		return startIdxBracket, '[', ']', nil
	}

	return -1, 0, 0, fmt.Errorf("no JSON object or array found in text: %s", text)
}

// extractJSONContent parses the text to find the matching end character for the JSON
func extractJSONContent(text string, startPos int, startChar byte, endChar byte) (string, error) {
	bracketCount := 1
	inString := false
	escaped := false

	for i := startPos + 1; i < len(text); i++ {
		// Handle escape sequences
		if escaped {
			escaped = false
			continue
		}

		c := text[i]

		// Handle string literals
		if c == '\\' && !escaped {
			escaped = true
			continue
		}

		if c == '"' && !escaped {
			inString = !inString
			continue
		}

		// Only count brackets outside of string literals
		if !inString {
			switch c {
			case startChar:
				bracketCount++
			case endChar:
				bracketCount--
				if bracketCount == 0 {
					// Found matching bracket - extract the JSON string
					return text[startPos : i+1], nil
				}
			}
		}
	}

	return "", errors.New("no valid JSON object found: unclosed brackets")
}

// validateJSON ensures the extracted JSON content is valid
func validateJSON(jsonStr string) error {
	var js any
	if err := json.Unmarshal([]byte(jsonStr), &js); err != nil {
		return fmt.Errorf("extracted invalid JSON: %w", err)
	}
	return nil
}

// GenerateSchema generates a JSON schema for a given struct type
func GenerateSchema[T any]() map[string]any {
	// Structured Outputs uses a subset of JSON schema
	// These flags are necessary to comply with the subset
	reflector := jsonschema.Reflector{
		AllowAdditionalProperties: false,
		DoNotReference:            true,
	}
	var v T
	schema := reflector.Reflect(v)

	// Convert schema to map[string]any format
	schemaBytes, err := json.Marshal(schema)
	if err != nil {
		return map[string]any{}
	}
	var schemaMap map[string]any
	err = json.Unmarshal(schemaBytes, &schemaMap)
	if err != nil {
		return map[string]any{}
	}

	return schemaMap
}

func S3PDFToMarkdown(
	ctx context.Context,
	attachmentURL string,
	options ...MarkdownOptions,
) (markdown string, err error) {

	parsedURL, err := url.Parse(attachmentURL)
	if err != nil {
		return markdown, fmt.Errorf("failed to parse S3 URL: %w", err)
	}

	queryParams := parsedURL.Query()

	objectKey := queryParams.Get("prefix")
	bucketName := strings.TrimPrefix(parsedURL.Path, "/s3/object/")

	if bucketName == "" || objectKey == "" {
		return markdown, fmt.Errorf("empty bucket or object key in S3 URL %s", attachmentURL)
	}

	s3Client, err := s3fetcher.New(ctx, bucketName)
	if err != nil {
		return markdown, fmt.Errorf("s3backup.New error: %w", err)
	}

	resp, err := s3Client.GetAttachment(ctx, objectKey)
	if err != nil {
		return markdown, fmt.Errorf("s3.GetObject error: %w", err)
	}
	if len(resp) == 0 {
		return "", errors.New("received nil response from S3 GetAttachment")
	}

	doc, err := fitz.NewFromMemory(resp)
	if err != nil {
		return markdown, fmt.Errorf("fitz.NewFromMemory error: %w", err)
	}
	defer doc.Close()

	// initialize cleaner and converter before loop
	p := bluemonday.UGCPolicy()
	p.AllowElements("body")

	converter := md.NewConverter("", true, nil)
	converter.Use(plugins.GitHubFlavored())

	numPages := doc.NumPage()
	if len(options) > 0 && options[0].NumPages > 0 {
		numPages = options[0].NumPages
	}

	var builder strings.Builder
	// Convert each page of the PDF to HTML, sanitize it, and convert to Markdown
	for i := 0; i < numPages; i++ {
		html, err := doc.HTML(i, true)
		if err != nil {
			return markdown, fmt.Errorf("failed to get HTML from PDF: %w", err)
		}

		sanitizedHTML := p.Sanitize(html)

		markdownText, err := converter.ConvertString(sanitizedHTML)
		if err != nil {
			return markdown, fmt.Errorf("failed to convert HTML to Markdown: %w", err)
		}

		builder.WriteString(markdownText)
		builder.WriteString("\n\n")
	}

	return builder.String(), nil
}
