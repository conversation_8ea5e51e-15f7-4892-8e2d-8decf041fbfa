package extractor

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3fetcher"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	MarkdownOptions struct {
		// Since GetZeroxMarkdown is used for verifying email labels,
		// we need to allow a limit on the number of pages we convert to markdown.
		// Otherwise, the LLM will struggle to handle the request.
		NumPages int
	}

	ZeroxRequest struct {
		File         []byte `json:"file"`
		Model        string `json:"model"`
		CustomPrompt string `json:"custom_prompt"`
	}

	ZeroxResponse struct {
		Success bool        `json:"success"`
		Message string      `json:"message"`
		Result  ZeroxResult `json:"result"`
	}

	ZeroxResult struct {
		CompletionTime float64     `json:"completion_time"`
		FileName       string      `json:"file_name"`
		InputTokens    int         `json:"input_tokens"`
		OutputTokens   int         `json:"output_tokens"`
		Pages          []ZeroxPage `json:"pages"`
	}

	ZeroxPage struct {
		Content       string `json:"content"`
		ContentLength int    `json:"content_length"`
		Page          int    `json:"page"`
	}
)

// GetZeroxMarkdown converts a file attachment to markdown using the Zerox service.
// The following document types are accepted by the extract endpoints as either a file buffer or URL:
// .pdf, .doc, .docx, .png, .jpg, .jpeg, .tiff, .odt, .ott, .rtf, .txt, .html, .htm, .xml, .wps, .wpd,
// .ods, .ots, .ppt, .pptx, .odp, .otp
func GetZeroxMarkdown(
	ctx context.Context,
	attachment models.Attachment,
	options ...MarkdownOptions,
) (string, error) {

	cyclopsAddress := os.Getenv("CYCLOPS_URL")
	if cyclopsAddress == "" {
		return "", errors.New("missing cyclops ec2 address")
	}

	if attachment.S3URL == "" {
		return "", errors.New("no S3 URL provided")
	}

	bucketName, objectKey, err := parseS3URL(attachment.S3URL)
	if err != nil {
		return "", fmt.Errorf("invalid S3 URL: %w", err)
	}

	s3Client, err := s3fetcher.New(ctx, bucketName)
	if err != nil {
		return "", fmt.Errorf("failed to create S3 client: %w", err)
	}

	fileData, err := fetchFileFromS3(ctx, s3Client, objectKey)
	if err != nil {
		return "", err
	}

	return processFileWithZerox(ctx, fileData, attachment.OriginalFileName, cyclopsAddress, options...)
}

// parseS3URL extracts bucket name and object key from an S3 URL
func parseS3URL(s3URL string) (bucketName, objectKey string, err error) {
	parsedURL, err := url.Parse(s3URL)
	if err != nil {
		return "", "", fmt.Errorf("failed to parse S3 URL: %w", err)
	}

	queryParams := parsedURL.Query()
	objectKey = queryParams.Get("prefix")
	bucketName = strings.TrimPrefix(parsedURL.Path, "/s3/object/")

	if bucketName == "" || objectKey == "" {
		return "", "", fmt.Errorf("empty bucket or object key in S3 URL %s", s3URL)
	}

	return bucketName, objectKey, nil
}

// fetchFileFromS3 retrieves a file from S3 storage
func fetchFileFromS3(ctx context.Context, s3Client s3fetcher.Fetcher, objectKey string) ([]byte, error) {
	fileData, err := s3Client.GetAttachment(ctx, objectKey)
	if err != nil {
		return nil, fmt.Errorf("s3.GetObject error: %w", err)
	}
	if fileData == nil {
		return nil, errors.New("received nil response from S3 GetAttachment")
	}

	fileDataSizeInBytes := len(fileData)

	log.Debug(
		ctx,
		"fetched file from S3",
		zap.Float64("fileSizeKb", float64(fileDataSizeInBytes)/1024),
		zap.String("objectKey", objectKey),
	)

	return fileData, nil
}

// processFileWithZerox sends the file to the Zerox API and returns the markdown content
func processFileWithZerox(
	ctx context.Context,
	fileData []byte,
	fileName string,
	cyclopsAddress string,
	options ...MarkdownOptions,
) (string, error) {

	requestBody, contentType, err := createMultipartForm(fileData, fileName, options...)
	if err != nil {
		return "", fmt.Errorf("failed to create multipart form: %w", err)
	}

	log.Debug(ctx, "sending zerox request")

	response, err := sendZeroxRequest(ctx, requestBody, contentType, cyclopsAddress)
	if err != nil {
		return "", err
	}

	if len(response.Result.Pages) == 0 {
		return "", errors.New("no pages returned from Zerox API")
	}

	log.Info(
		ctx,
		fmt.Sprintf("zerox conversion succeeded with %d pages", len(response.Result.Pages)),
		zap.Any("response", response),
	)

	return response.Result.Pages[0].Content, nil
}

// createMultipartForm creates a multipart form for the Zerox API request
func createMultipartForm(fileData []byte, fileName string, options ...MarkdownOptions) (*bytes.Buffer, string, error) {
	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	fileField, err := writer.CreateFormFile("file", fileName)
	if err != nil {
		return nil, "", fmt.Errorf("failed to create form file: %w", err)
	}
	if _, err = io.Copy(fileField, bytes.NewReader(fileData)); err != nil {
		return nil, "", fmt.Errorf("failed to copy file: %w", err)
	}

	if err = writer.WriteField("model", "gpt-4o-mini"); err != nil {
		return nil, "", fmt.Errorf("failed to write model field: %w", err)
	}

	// Add numPages field if provided in options
	if len(options) > 0 && options[0].NumPages > 0 {
		if err = writer.WriteField("num_pages", fmt.Sprintf("%d", options[0].NumPages)); err != nil {
			return nil, "", fmt.Errorf("failed to write num_pages field: %w", err)
		}
	}

	contentType := writer.FormDataContentType()
	if err = writer.Close(); err != nil {
		return nil, "", fmt.Errorf("failed to close multipart writer: %w", err)
	}

	return &requestBody, contentType, nil
}

// sendZeroxRequest sends the request to the Zerox API and parses the response
func sendZeroxRequest(
	ctx context.Context,
	requestBody *bytes.Buffer,
	contentType, cyclopsAddress string,
) (*ZeroxResponse, error) {
	req, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		fmt.Sprintf("%s/zerox/markdown", cyclopsAddress),
		requestBody,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create zerox request: %w", err)
	}
	req.Header.Set("Content-Type", contentType)

	client := otel.TracingHTTPClient(60 * time.Second)
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to make zerox request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read zerox response body: %w", err)
	}

	var zeroxResponse ZeroxResponse
	if err = json.Unmarshal(body, &zeroxResponse); err != nil {
		return nil, fmt.Errorf("failed to unmarshal zerox response: %w", err)
	}

	if !zeroxResponse.Success {
		return nil, fmt.Errorf("zerox API error: %s", zeroxResponse.Message)
	}

	return &zeroxResponse, nil
}
