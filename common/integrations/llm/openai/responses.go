package openai

import (
	"context"
	"errors"
	"fmt"
	"net"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/braintrustdata/braintrust-go"
	"github.com/braintrustdata/braintrust-go/shared"
	"github.com/google/uuid"
	openaiSDK "github.com/openai/openai-go"
	openaiOption "github.com/openai/openai-go/option"
	openaiResponses "github.com/openai/openai-go/responses"
	openaiShared "github.com/openai/openai-go/shared"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/integrations/llm/braintrustsdk"
	"github.com/drumkitai/drumkit/common/integrations/llm/openai/env"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/rds"
)

var (
	openaiClient openaiSDK.Client
	clientOnce   sync.Once
	clientErr    error

	defaultModel = openaiShared.ChatModelGPT4oMini
)

type (
	ResponseOptions struct {
		// DeveloperPrompt is the extraction instructions.
		DeveloperPrompt string
		// UserPrompt is the attachment or email data.
		UserPrompt         string
		Model              openaiShared.ChatModel
		Schema             any
		PreviousResponseID string
	}

	GetResponseOutput struct {
		Content         string
		ResponseID      string
		BraintrustLogID string
	}
)

// GetResponse creates a response using the OpenAI responses API.
func (s *service) GetResponse(
	ctx context.Context,
	email models.Email,
	braintrustProjectDetails braintrustsdk.ProjectDetails,
	options ...ResponseOptions,
) (res GetResponseOutput, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "openai.GetResponse", nil)
	defer func() { metaSpan.End(err) }()

	params := s.buildResponseParams(options...)

	var braintrustInput braintrustsdk.BraintrustLogInput
	if len(options) > 0 {
		braintrustInput = braintrustsdk.BraintrustLogInput{
			ID:                   uuid.NewString(),
			Email:                email,
			ProjectDetails:       braintrustProjectDetails,
			DeveloperPrompt:      options[0].DeveloperPrompt,
			UserPrompt:           options[0].UserPrompt,
			OpenAIConversationID: options[0].PreviousResponseID,
		}
	}

	responseContent, responseID, err := s.getResponse(ctx, braintrustInput, params)

	return GetResponseOutput{
		Content:         responseContent,
		ResponseID:      responseID,
		BraintrustLogID: braintrustInput.ID,
	}, err
}

// buildChatParams builds the parameters for the OpenAI responses API.
// The parameters are model, developer prompt, user prompt, and JSON schema for structured output.
func (s *service) buildResponseParams(options ...ResponseOptions) openaiResponses.ResponseNewParams {
	opts := ResponseOptions{}
	if len(options) > 0 {
		opts = options[0]
	}

	model := defaultModel
	if opts.Model != "" {
		model = opts.Model
	}

	params := openaiResponses.ResponseNewParams{
		Model: model,
	}

	if opts.PreviousResponseID != "" {
		params.PreviousResponseID = openaiSDK.String(opts.PreviousResponseID)
	}

	var messages []openaiResponses.ResponseInputItemUnionParam

	if opts.DeveloperPrompt != "" {
		messages = append(messages, openaiResponses.ResponseInputItemUnionParam{
			OfMessage: &openaiResponses.EasyInputMessageParam{
				Role: openaiResponses.EasyInputMessageRoleDeveloper,
				Content: openaiResponses.EasyInputMessageContentUnionParam{
					OfString: openaiSDK.String(opts.DeveloperPrompt),
				},
			},
		})
	}

	// If the user prompt is provided and the previous message ID is not present, add the user prompt to the messages.
	// This is to ensure that the user prompt is always the first message in the conversation.
	// The user prompt is the shipment information that we are extracting from.
	if opts.UserPrompt != "" && !params.PreviousResponseID.IsPresent() {
		messages = append(messages, openaiResponses.ResponseInputItemUnionParam{
			OfMessage: &openaiResponses.EasyInputMessageParam{
				Role: openaiResponses.EasyInputMessageRoleUser,
				Content: openaiResponses.EasyInputMessageContentUnionParam{
					OfString: openaiSDK.String(opts.UserPrompt),
				},
			},
		})
	}

	params.Input = openaiResponses.ResponseNewParamsInputUnion{
		OfInputItemList: messages,
	}

	if opts.Schema != nil {
		params.Text = openaiResponses.ResponseTextConfigParam{
			Format: openaiResponses.ResponseFormatTextConfigUnionParam{
				OfJSONSchema: &openaiResponses.ResponseFormatTextJSONSchemaConfigParam{
					Schema: opts.Schema.(map[string]any),
					Strict: openaiSDK.Bool(true),
					Name:   openaiSDK.String("json_schema"),
				},
			},
		}
	}

	return params
}

// For logging purposes
var openAIObject = models.Integration{Name: models.OpenAI}

// getResponse sends a responses API request to OpenAI and returns the response content and ID.
func (s *service) getResponse(
	ctx context.Context,
	braintrustInput braintrustsdk.BraintrustLogInput,
	params openaiResponses.ResponseNewParams,
) (_ string, _ string, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "openai.getResponse", nil)
	defer func() { metaSpan.End(err) }()

	if !params.Input.IsPresent() {
		return "", "", errors.New("no input messages provided, please provide a developer prompt and/or user prompt")
	}

	// default is 2 retries, 30s timeout for each retry
	response, err := s.client.Responses.New(ctx, params, openaiOption.WithRequestTimeout(30*time.Second))
	if err != nil {
		var apiErr *openaiSDK.Error
		if errors.As(err, &apiErr) && apiErr != nil {
			httplog.LogHTTPResponseCode(ctx, openAIObject, apiErr.StatusCode)

			if helpers.IsArrayElmInString(
				strings.ToLower(apiErr.Message),
				[]string{"timeout", "timed out", "client.timeout"},
			) {
				return "", "", fmt.Errorf("OpenAI API timeout: %w", err)
			}

			log.Error(
				ctx,
				"OpenAI error",
				zap.String("Status Code", apiErr.Code),
				zap.String("Message", apiErr.Message),
			)
		}

		httplog.LogHTTPRequestFailed(ctx, openAIObject, err)

		var httpErr *net.OpError
		if errors.As(err, &httpErr) && httpErr != nil {
			if httpErr.Timeout() {
				return "", "", fmt.Errorf("HTTP request timed out: %w", err)
			}
		}
		return "", "", fmt.Errorf("failed to get completion: %w", err)
	}

	braintrustErr := braintrustsdk.SubmitLog(
		ctx,
		braintrustInput.ProjectDetails,
		getBraintrustLogForChat(ctx, braintrustInput, response),
	)
	if braintrustErr != nil {
		if env.Vars.AppEnv == "prod" {
			log.WarnNoSentry(ctx, "error submitting log to braintrust", zap.Error(braintrustErr))
		} else {
			log.Debug(ctx, "error submitting log to braintrust", zap.Error(braintrustErr))
		}
	}

	content := response.OutputText()
	if content == "" {
		return "", "", fmt.Errorf("empty message content from LLM for completion %s", response.ID)
	}

	return content, response.ID, nil
}

func getBraintrustLogForChat(
	ctx context.Context,
	logInput braintrustsdk.BraintrustLogInput,
	openAIResponse *openaiResponses.Response,
) braintrust.InsertProjectLogsEventParam {
	env := os.Getenv("APP_ENV")
	service, err := rds.GetServiceByID(ctx, logInput.Email.ServiceID)
	if err != nil {
		log.WarnNoSentry(ctx,
			"error getting service for braintrust log",
			zap.Error(err),
			zap.Uint("serviceID", logInput.Email.ServiceID),
		)
	}

	return braintrust.InsertProjectLogsEventParam{
		ID: braintrust.String(logInput.ID),
		Input: braintrust.Raw[any](
			map[string]any{
				"user_prompt":      logInput.UserPrompt,
				"developer_prompt": logInput.DeveloperPrompt,
			},
		),
		Output: braintrust.Raw[any](openAIResponse.OutputText()),
		Tags:   braintrust.Raw[[]string](logInput.ProjectDetails.GetTags()),
		Metrics: braintrust.Raw[shared.InsertProjectLogsEventMetricsParam](
			shared.InsertProjectLogsEventMetricsParam{
				CompletionTokens: braintrust.Int(openAIResponse.Usage.TotalTokens),
				PromptTokens:     braintrust.Int(openAIResponse.Usage.InputTokens),
				Tokens:           braintrust.Int(openAIResponse.Usage.OutputTokens + openAIResponse.Usage.InputTokens),
			},
		),
		Metadata: braintrust.Raw[shared.InsertProjectLogsEventMetadataParam](
			shared.InsertProjectLogsEventMetadataParam{
				ExtraFields: map[string]any{
					"environment":     env,
					"conversation_id": logInput.OpenAIConversationID,

					// Email fields
					"email_address":     logInput.Email.Account,
					"email_id":          logInput.Email.ID,
					"email_subject":     logInput.Email.Subject,
					"email_external_id": logInput.Email.ExternalID,
					"email_thread_id":   logInput.Email.ThreadID,

					// User and Service fields
					"user_id":               logInput.Email.UserID,
					"service_id":            logInput.Email.ServiceID,
					"service_name":          service.Name,
					"service_email_domains": service.EmailDomains,

					// Attachment fields
					"attachment_name": logInput.Attachment.OriginalFileName,
					"attachment_url":  logInput.Attachment.S3URL,
					"attachment_type": logInput.Attachment.MimeType,
				},
			},
		),
		SpanAttributes: braintrust.Raw[shared.SpanAttributesParam](
			shared.SpanAttributesParam{
				Name: braintrust.String(string(logInput.ProjectDetails.StepName)),
			},
		),
	}
}
