package mycarrierportal

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
)

const BaseURL = "https://api.mycarrierpackets.com/"

type MyCarrierPortal struct {
	integration models.Integration
}

type API interface {
	InitialOnboard(
		context.Context, models.Service, models.OnboardCarrierVerificationRequest,
	) (models.OnboardCarrierVerificationResponse, error)
	GetIntegrationModel() *models.Integration
	GetToken(ctx context.Context, username, password string) (accessToken string, expiresIn int, err error)
}

func New(ctx context.Context, integration models.Integration) (MyCarrierPortal, error) {
	log.With(ctx, zap.Uint("axleIntegrationID", integration.ID), zap.String("integration", "mycarrierportal"))

	var err error
	if integration.AccessToken != "" && integration.NeedsRefresh() {

		var client MyCarrierPortal

		decryptedPassword, err := crypto.DecryptAESGCM(ctx, string(integration.EncryptedPassword), nil)
		if err != nil {
			return client, fmt.Errorf("error decrypting password: %w", err)
		}

		accessToken, expiresIn, err := client.GetToken(ctx, integration.Username, decryptedPassword)
		if err != nil {
			return client, fmt.Errorf("error getting token: %w", err)
		}

		// access token is valid for 1 year
		integration.AccessToken = accessToken
		integration.AccessTokenExpirationDate = models.NullTime{
			Time:  time.Now().Add(time.Duration(expiresIn) * time.Second),
			Valid: true,
		}

		if integration.ID != 0 {
			if err = integrationDB.Update(ctx, &integration); err != nil {
				log.ErrorNoSentry(
					ctx,
					"failed to update mcp info on integration db",
					zap.Any("integration", integration),
				)
				return client, fmt.Errorf("integration db update failed: %w", err)
			}
		}
		client.integration = integration
		return client, err
	}

	return MyCarrierPortal{integration: integration}, err
}

func (m MyCarrierPortal) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	onboardRequest models.OnboardCarrierVerificationRequest,
) (_ models.OnboardCarrierVerificationResponse, err error) {
	if onboardRequest.Username == "" || onboardRequest.Password == "" {
		return models.OnboardCarrierVerificationResponse{}, errors.New("missing MyCarrierPortal API credentials")
	}

	return m.authenticate(ctx, onboardRequest.Username, onboardRequest.Password)
}

func (m *MyCarrierPortal) GetIntegrationModel() *models.Integration {
	return &m.integration
}

func (m MyCarrierPortal) authenticate(
	ctx context.Context,
	username, password string) (models.OnboardCarrierVerificationResponse, error) {
	accessToken, expiresIn, err := m.GetToken(ctx, username, password)
	if err != nil {
		return models.OnboardCarrierVerificationResponse{}, fmt.Errorf("GetToken failed: %w", err)
	}
	log.Info(ctx, "Successfully authenticated MyCarrierPortal client")

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, password, nil)
	if err != nil {
		return models.OnboardCarrierVerificationResponse{}, fmt.Errorf("password encryption failed: %w", err)
	}

	return models.OnboardCarrierVerificationResponse{
		AccessToken:               accessToken,
		EncryptedPassword:         encryptedPassword,
		Username:                  username,
		AccessTokenExpirationDate: time.Now().Add(time.Duration(expiresIn) * time.Second),
	}, err
}

func (m MyCarrierPortal) GetToken(ctx context.Context, username, password string) (
	accessToken string, expiresIn int, err error) {
	client := otel.TracingHTTPClient()
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, BaseURL+"token", strings.NewReader(url.Values{
		"grant_type": {"password"},
		"username":   {username},
		"password":   {password},
	}.Encode()))
	if err != nil {
		log.Error(ctx, "error with mcp")
		return
	}
	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")

	res, err := client.Do(req)
	if err != nil {
		log.Error(ctx, "error with mcp")
		return
	}
	defer res.Body.Close()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		log.Error(ctx, "error with mcp")
		return
	}

	if err != nil || res.StatusCode != http.StatusOK {
		log.Error(ctx, "error with mcp")
		return "", 0, errors.New("error with mcp")
	}

	var dst AuthResponse
	if err = json.Unmarshal(body, &dst); err != nil {
		log.Error(ctx, "error unmarshaling mcp token")
		return
	}

	m.integration.AccessToken = dst.AccessToken
	return dst.AccessToken, dst.ExpiresIn, nil
}

func (m MyCarrierPortal) GetCarrier(
	ctx context.Context,
	email string,
) (
	carrier models.CarrierVerificationResponse,
	err error,
) {
	emailSearchResponse := EmailSearchResponse{}
	emailSearchCarrier := EmailSearchCarrier{}
	carrierResponse := []Carrier{}

	err = m.post(
		ctx,
		fmt.Sprintf("api/v1/Carrier/FindAssociatedCarriers?email=%s", email),
		nil,
		&emailSearchResponse,
	)
	if err != nil {
		return carrier, err
	}

	if len(emailSearchResponse.AssociatedCarriers) > 0 {
		emailSearchCarrier = emailSearchResponse.AssociatedCarriers[0]
	} else {
		return carrier, errors.New("no associated carriers found")
	}

	err = m.post(
		ctx,
		fmt.Sprintf("api/v1/carrier/previewcarrier?DOTNumber=%d", emailSearchCarrier.DOTNumber),
		nil,
		&carrierResponse,
	)
	if len(carrierResponse) > 0 {
		carrier = models.CarrierVerificationResponse{
			CarrierName:                   carrierResponse[0].CompanyName,
			DBA:                           carrierResponse[0].DBAName,
			DOTNumber:                     carrierResponse[0].DotNumber,
			DocketNumber:                  carrierResponse[0].DocketNumber,
			RiskRating:                    carrierResponse[0].RiskAssessmentDetails.OverallRating,
			TotalIncidentReports:          carrierResponse[0].IncidentReports.TotalIncidentReports,
			TotalIncidentReportsWithFraud: carrierResponse[0].IncidentReports.TotalIncidentReportsWithFraud,
			CompletedPacket:               carrierResponse[0].CarrierRating.HasCompletedPacket,
			IntegrationName:               string(models.MyCarrierPortal),
		}
		return carrier, nil
	}
	return
}

func (m MyCarrierPortal) InviteCarrier(ctx context.Context, dotnumber, email string) (err error) {
	carrierResponse := ""

	err = m.post(
		ctx,
		fmt.Sprintf(
			"api/v1/Carrier/EmailPacketInvitation?dotNumber=%s&carrierEmail=%s&userName=%s&sendConfirmationEmail=true",
			dotnumber, email, m.integration.Username),
		nil,
		&carrierResponse,
	)

	return
}

func (m MyCarrierPortal) post(
	ctx context.Context,
	endPoint string,
	postRequest []byte,
	dst any,
) error {
	queryURL := BaseURL + endPoint
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, queryURL, bytes.NewBuffer(postRequest))
	if err != nil {
		return fmt.Errorf("failed to build POST request: %w", err)
	}

	req.Header.Add("Accept", "application/json")
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("Authorization", "Bearer "+m.integration.AccessToken)

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if code := resp.StatusCode; code >= http.StatusBadRequest {
		return errtypes.NewHTTPResponseError(m.integration, req, resp, body)
	}

	if err = json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return nil
}
