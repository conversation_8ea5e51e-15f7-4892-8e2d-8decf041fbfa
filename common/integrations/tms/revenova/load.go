package revenova

import (
	"context"
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"time"

	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (r Revenova) GetLoadIDs(context.Context, models.SearchLoadsQuery) (
	[]string,
	error,
) {
	return nil, helpers.NotImplemented(models.Revenova, "GetLoadIDs")
}

func (r Revenova) GetLoad(ctx context.Context, loadID string) (models.Load, models.LoadAttributes, error) {
	var err error
	var metaSpan otel.MetaSpan

	spanAttrs := append(otel.IntegrationAttrs(r.tms), attribute.String("freight_tracking_id", loadID))
	//nolint:staticcheck // False positive
	ctx, metaSpan = otel.StartSpan(ctx, "GetLoadRevenova", spanAttrs)
	defer func() { metaSpan.End(err) }()

	attrs := r.GetDefaultLoadAttributes()

	endPoint := fmt.Sprintf("/services/apexrest/rtms/tmsloadservice/%s", loadID)
	var loadResp GetLoadData
	err = r.get(ctx, endPoint, nil, &loadResp, &r.tms.AccessToken, s3backup.TypeLoads)
	if err != nil {
		return models.Load{}, attrs, err
	}
	loadData := r.RevenovaLoadToDrumkitLoad(ctx, loadResp)

	return loadData, attrs, nil
}

func (r Revenova) CreateLoad(ctx context.Context, load models.Load, _ *models.TMSUser) (result models.Load, err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms), otel.LoadAttrs(load)...)
	ctx, metaSpan := otel.StartSpan(ctx, "CreateLoadRevenova", spanAttrs)
	defer func() { metaSpan.End(err) }()

	reqBody := r.DrumkitLoadToRevenovaLoad(load)
	var response GetLoadData
	err = r.post(ctx, "/services/apexrest/rtms/tmsloadservice", nil, reqBody, &response,
		&r.tms.AccessToken, s3backup.TypeLoads)
	if err != nil {
		return result, err
	}
	result = r.RevenovaLoadToDrumkitLoad(ctx, response)
	if err != nil {
		return result, err
	}

	return result, nil
}

func (r Revenova) UpdateLoad(
	ctx context.Context,
	_ *models.Load,
	load *models.Load,
) (result models.Load, attr models.LoadAttributes, err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms), otel.LoadAttrs(*load)...)
	ctx, metaSpan := otel.StartSpan(ctx, "UpdateLoadRevenova", spanAttrs)
	defer func() { metaSpan.End(err) }()

	reqBody := r.DrumkitLoadToRevenovaLoad(*load)
	queryParams := url.Values{}
	queryParams.Add("loadId", load.FreightTrackingID)
	queryParams.Add("loadName", "") // TODO : from where we can get load name
	var response GetLoadData
	err = r.put(ctx, "/services/apexrest/rtms/tmsloadservice", queryParams, reqBody, &response,
		nil, s3backup.TypeLoads)
	if err != nil {
		return result, attr, fmt.Errorf("updating Load failed: %w", err)
	}
	result = r.RevenovaLoadToDrumkitLoad(ctx, response)
	return result, attr, nil
}

func (r Revenova) RevenovaLoadToDrumkitLoad(ctx context.Context, loadResp GetLoadData) (load models.Load) {
	load.ServiceID = r.tms.ServiceID
	load.ExternalTMSID = loadResp.LoadID
	load.Status = loadResp.LoadStatus
	load.Specifications.TotalWeight = models.ValueUnit{
		Val:  float32(loadResp.TotalWeight),
		Unit: "lbs",
	}

	if loadResp.PoNumber != nil {
		load.PONums = *loadResp.PoNumber
	}

	for _, data := range loadResp.Stops {
		if data.IsPickup {
			// pickup
			if data.PickupDeliveryNumber != nil {
				load.Pickup.ExternalTMSID = *data.PickupDeliveryNumber
			}
			load.Pickup.Name = data.ShippingContact.FirstName + " " + data.ShippingContact.LastName
			load.Pickup.Email = data.ShippingContact.Email
			load.Pickup.Phone = data.ShippingContact.Phone
			load.Pickup.AddressLine1 = data.Address
			load.Pickup.City = data.Location.ShippingCity
			load.Pickup.State = data.Location.ShippingStateProvince
			load.Pickup.Country = data.Location.ShippingCountry
			load.Pickup.Zipcode = data.Location.ShippingPostalCode
			if data.References != nil {
				re := regexp.MustCompile(`\d+`)
				// Find all matches of digits in the string
				matches := re.FindAllString(*data.References, -1)
				refNo := matches[len(matches)-1] // because the value is present at last
				load.Pickup.RefNumber = refNo
			}

			var err error
			load.Pickup.Timezone, err = timezone.GetTimezone(ctx, data.Location.ShippingCity,
				data.Location.ShippingStateProvince, data.Location.ShippingCountry)
			if err != nil {
				log.Warn(ctx, "error in finding timezone according to location details",
					zap.String("city", data.Location.ShippingCity),
					zap.String("state", data.Location.ShippingStateProvince),
					zap.String("country", data.Location.ShippingCountry))
			}
			apptTime, timeErr := time.Parse(time.TimeOnly, data.AppointmentTime)
			if timeErr != nil {
				log.Warn(ctx, "unable to parse appointment time string",
					zap.String("timeString", data.AppointmentTime))
			}
			load.Pickup.ApptStartTime = models.NullTime{
				Time:  apptTime,
				Valid: true,
			}

			if data.ExpectedDate != nil {
				expectedPickupTime, timeErr := time.Parse(time.DateOnly, *data.ExpectedDate)
				if timeErr != nil {
					log.Warn(ctx, "unable to parse appointment time string",
						zap.String("timeString", data.AppointmentTime))
				}

				load.Carrier.ExpectedPickupTime = models.NullTime{
					Time:  expectedPickupTime,
					Valid: true,
				}
			}

		} else {
			// delivery
			load.Carrier.Name = loadResp.Carrier
			load.Carrier.Email = data.ShippingContact.Email
			load.Carrier.Phone = data.ShippingContact.Phone
			load.Carrier.DispatchCity = data.Location.ShippingCity
			load.Carrier.DispatchState = data.Location.ShippingStateProvince
			if data.DepartureTime != nil {
				dispatchTime, timeErr := time.Parse("", *data.DepartureTime)
				if timeErr != nil {
					log.Warn(ctx, "unable to parse departure time string",
						zap.String("timeString", *data.DepartureTime))
				}
				load.Carrier.DispatchedTime = models.NullTime{
					Time:  dispatchTime,
					Valid: true,
				}
			}
			if data.ExpectedDate != nil {
				expectedDate, timeErr := time.Parse(time.DateOnly, *data.ExpectedDate)
				if timeErr != nil {
					log.Warn(ctx, "unable to parse Expected Date string",
						zap.String("timeString", *data.DepartureTime))
				}
				load.Carrier.ExpectedDeliveryTime = models.NullTime{
					Time:  expectedDate,
					Valid: true,
				}
			}

			// customer
			load.Customer.Name = data.ShippingContact.FirstName + " " + data.ShippingContact.LastName
			load.Customer.Email = data.ShippingContact.Email
			load.Customer.Phone = data.ShippingContact.Phone
			load.Customer.City = data.Location.ShippingCity
			load.Customer.State = data.Location.ShippingStateProvince
			load.Customer.Country = data.Location.ShippingCountry
			load.Customer.Zipcode = data.Location.ShippingPostalCode
		}
	}

	// specifications
	if len(loadResp.LineItems) > 0 {
		lineItem := loadResp.LineItems[0]
		if lineItem.HazMatNumber != nil {
			load.Specifications.Hazmat = true
		}
	}
	for _, spec := range loadResp.Accessorials {
		switch spec.AccessorialName {
		case "Lift Gate Pick Up":
			load.Specifications.LiftgatePickup = true
		case "Lift Gate Delivery":
			load.Specifications.LiftgateDelivery = true
		case "Inside Pick Up":
			load.Specifications.InsidePickup = true
		case "Inside Delivery":
			load.Specifications.InsideDelivery = true
		case "Extra Labor Pick Up":
			load.Specifications.Labor = true
		case "Extra Labor Delivery":
			load.Specifications.Labor = true
		}
	}

	return load
}

func (r Revenova) DrumkitLoadToRevenovaLoad(load models.Load) (req PostLoad) {
	// pickup
	pickupNames := strings.Split(load.Pickup.Name, " ")
	expectedDate := load.Carrier.ExpectedPickupTime.Time.Format(time.DateOnly)
	pickup := Stop{
		IsPickup:     true,          // required, true for pickup
		IsDropOff:    false,         // required, false for pickup
		StopNumber:   1,             // stopNumber is 1 for pickup
		ExpectedDate: &expectedDate, // required
		ShippingContact: ShippingContact{
			Phone:     load.Pickup.Phone,
			Email:     load.Pickup.Email,
			FirstName: pickupNames[0],
			LastName:  pickupNames[1],
		},
		Location: Location{
			ShippingCity:          load.Pickup.City,    // required
			ShippingStateProvince: load.Pickup.State,   // required
			ShippingCountry:       load.Pickup.Country, // required
			ShippingPostalCode:    load.Pickup.Zipcode, // required
			ShippingAddress:       load.Pickup.AddressLine1,
		},
	}

	// carrier
	carrierNames := strings.Split(load.Carrier.Name, " ")
	carrier := Stop{
		IsPickup:   false, // required, false for carrier
		IsDropOff:  true,  // required, true for carrier
		StopNumber: 2,     // stopNumber is 2 for carrier
		ShippingContact: ShippingContact{
			Phone:     load.Carrier.Phone,
			Email:     load.Carrier.Email,
			FirstName: carrierNames[0],
			LastName:  carrierNames[1],
		},
		Location: Location{
			ShippingCity:          load.Carrier.DispatchCity,  // required
			ShippingStateProvince: load.Carrier.DispatchState, // required
			ShippingCountry:       load.Customer.Country,      // required, customer country is same as delivery
			ShippingPostalCode:    load.Customer.Zipcode,      // required, customer postal code is same as delivery
			ShippingAddress:       load.Customer.AddressLine1,
		},
	}

	lineItem := LineItem{
		Weight:             int(load.Specifications.TotalWeight.Val), // required
		WeightUnits:        "lbs",                                    // required, TODO : need to confirm unit
		Stackable:          false,                                    // required, default
		Turnable:           false,                                    // required, default
		PickupStopNumber:   1,                                        // required, default
		DeliveryStopNumber: 2,                                        // required, default
		NmfcClass:          "50",                                     // required, default
		ItemNumber:         "Item",                                   // required, default
		ItemDescription:    "Fooda",                                  // required, default
		HandlingUnitWidth:  44,                                       // required, default
		HandlingUnitLength: 44,                                       // required, default
		HandlingUnitHeight: 44,                                       // required, default
		HandlingUnitCount:  2,                                        // required, default
		HandlingUnits:      "Pallets",                                // required, default
		DimensionUnits:     "in",                                     // required, default
	}

	accessoryForPickup := Accessory{
		AccessorialID:   "a01410000069DXmAAM", // TODO : need to confirm that these ids are fixed or not
		AccessorialName: "Lift Gate Pick Up",
		StopNumber:      1, // 1 for pickup
	}

	accessoryForDelivery := Accessory{
		AccessorialID:   "a01410000069DXlAAM",
		AccessorialName: "Lift Gate Delivery",
		StopNumber:      2, // 2 for delivery
	}

	req.Wsl.TotalWeight = int(load.Specifications.TotalWeight.Val) // required
	// TODO : need to confirm weight unit
	req.Wsl.WeightUnits = "lbs"              // required
	req.Wsl.TemperatureControlled = false    // required
	req.Wsl.Stops = []Stop{pickup, carrier}  // required
	req.Wsl.LineItems = []LineItem{lineItem} // required
	req.Wsl.Accessorials = []Accessory{accessoryForPickup, accessoryForDelivery}
	req.Wsl.ModeName = "LTL"              // required , default
	req.Wsl.LoadNumber = "Temp Load Name" // required, written in docs

	return req
}
