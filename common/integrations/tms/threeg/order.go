package threeg

import (
	"bytes"
	"context"
	"encoding/xml"
	"fmt"
	"io"
	"net/http"
	"strconv"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	orderdb "github.com/drumkitai/drumkit/common/rds/order"
)

// Company represents a company with its contact information
type Company struct {
	ID      string  `xml:"id"`
	Name    string  `xml:"name"`
	Address Address `xml:"address"`
	Contact string  `xml:"contact"`
	Phone   string  `xml:"phone"`
	Email   string  `xml:"email"`
}

// Location represents a company location with appointment information
type Location struct {
	Company
	RefNumber    string `xml:"refNumber"`
	ApptRequired bool   `xml:"apptRequired"`
	ApptType     string `xml:"apptType"`
	ApptStart    string `xml:"apptStart"`
	ApptEnd      string `xml:"apptEnd"`
	ApptNote     string `xml:"apptNote"`
	Timezone     string `xml:"timezone"`
}

// DeliveryLocation represents a delivery location with additional delivery-specific fields
type DeliveryLocation struct {
	Location
	MustDeliver string `xml:"mustDeliver"`
}

// Specifications represents the specifications of a load or order
type Specifications struct {
	TotalPallets   int     `xml:"totalPallets"`
	TotalPieces    int     `xml:"totalPieces"`
	Commodities    string  `xml:"commodities"`
	TotalWeight    float32 `xml:"totalWeight"`
	WeightUnit     string  `xml:"weightUnit"`
	TotalDistance  float32 `xml:"totalDistance"`
	DistanceUnit   string  `xml:"distanceUnit"`
	TransportType  string  `xml:"transportType"`
	TransportSize  string  `xml:"transportSize"`
	IsRefrigerated bool    `xml:"isRefrigerated"`
	MinTemp        float32 `xml:"minTemp"`
	MaxTemp        float32 `xml:"maxTemp"`
	Hazmat         bool    `xml:"hazmat"`
}

// OrderResponse represents the XML response from 3G TMS for an order
type OrderResponse struct {
	XMLName        xml.Name         `xml:"OrderResponse"`
	OrderID        string           `xml:"orderId"`
	OrderNumber    string           `xml:"orderNumber"`
	Status         string           `xml:"status"`
	Mode           string           `xml:"mode"`
	PONumbers      string           `xml:"poNumbers"`
	Customer       Company          `xml:"customer"`
	Pickup         Location         `xml:"pickup"`
	Consignee      DeliveryLocation `xml:"consignee"`
	Specifications Specifications   `xml:"specifications"`
	RateData       struct {
		CustomerRate Rate `xml:"customerRate"`
	} `xml:"rateData"`
	Notes []Note `xml:"notes>note"`
}

// parseOrderFromResponse converts a 3G TMS order response to a models.Order
func parseOrderFromResponse(orderResponse OrderResponse) models.Order {
	order := models.Order{
		OrderCoreInfo: models.OrderCoreInfo{
			ExternalOrderID: orderResponse.OrderID,
			OrderTrackingID: orderResponse.OrderID,
			Status:          orderResponse.Status,
			Mode:            orderResponse.Mode,
			Customer: models.Customer{
				CompanyCoreInfo: models.CompanyCoreInfo{
					ExternalTMSID: orderResponse.Customer.ID,
					Name:          orderResponse.Customer.Name,
					AddressLine1:  orderResponse.Customer.Address.Line1,
					AddressLine2:  orderResponse.Customer.Address.Line2,
					City:          orderResponse.Customer.Address.City,
					State:         orderResponse.Customer.Address.State,
					Zipcode:       orderResponse.Customer.Address.Zipcode,
					Country:       orderResponse.Customer.Address.Country,
					Contact:       orderResponse.Customer.Contact,
					Phone:         orderResponse.Customer.Phone,
					Email:         orderResponse.Customer.Email,
				},
			},
			Pickup: models.Pickup{
				CompanyCoreInfo: models.CompanyCoreInfo{
					ExternalTMSID: orderResponse.Pickup.ID,
					Name:          orderResponse.Pickup.Name,
					AddressLine1:  orderResponse.Pickup.Address.Line1,
					AddressLine2:  orderResponse.Pickup.Address.Line2,
					City:          orderResponse.Pickup.Address.City,
					State:         orderResponse.Pickup.Address.State,
					Zipcode:       orderResponse.Pickup.Address.Zipcode,
					Country:       orderResponse.Pickup.Address.Country,
					Contact:       orderResponse.Pickup.Contact,
					Phone:         orderResponse.Pickup.Phone,
					Email:         orderResponse.Pickup.Email,
				},
				RefNumber:    orderResponse.Pickup.RefNumber,
				ApptRequired: orderResponse.Pickup.ApptRequired,
				ApptType:     orderResponse.Pickup.ApptType,
				ApptNote:     orderResponse.Pickup.ApptNote,
				Timezone:     orderResponse.Pickup.Timezone,
			},
			Consignee: models.Consignee{
				CompanyCoreInfo: models.CompanyCoreInfo{
					ExternalTMSID: orderResponse.Consignee.ID,
					Name:          orderResponse.Consignee.Name,
					AddressLine1:  orderResponse.Consignee.Address.Line1,
					AddressLine2:  orderResponse.Consignee.Address.Line2,
					City:          orderResponse.Consignee.Address.City,
					State:         orderResponse.Consignee.Address.State,
					Zipcode:       orderResponse.Consignee.Address.Zipcode,
					Country:       orderResponse.Consignee.Address.Country,
					Contact:       orderResponse.Consignee.Contact,
					Phone:         orderResponse.Consignee.Phone,
					Email:         orderResponse.Consignee.Email,
				},
				RefNumber:    orderResponse.Consignee.RefNumber,
				ApptRequired: orderResponse.Consignee.ApptRequired,
				ApptType:     orderResponse.Consignee.ApptType,
				ApptNote:     orderResponse.Consignee.ApptNote,
				Timezone:     orderResponse.Consignee.Timezone,
			},
			Specifications: models.Specifications{
				TotalInPalletCount: orderResponse.Specifications.TotalPallets,
				TotalPieces: models.ValueUnit{
					Val:  float32(orderResponse.Specifications.TotalPieces),
					Unit: "pieces",
				},
				Commodities: orderResponse.Specifications.Commodities,
				TotalWeight: models.ValueUnit{
					Val:  orderResponse.Specifications.TotalWeight,
					Unit: orderResponse.Specifications.WeightUnit,
				},
				TotalDistance: models.ValueUnit{
					Val:  orderResponse.Specifications.TotalDistance,
					Unit: orderResponse.Specifications.DistanceUnit,
				},
				TransportType:     orderResponse.Specifications.TransportType,
				TransportSize:     orderResponse.Specifications.TransportSize,
				IsRefrigerated:    orderResponse.Specifications.IsRefrigerated,
				MinTempFahrenheit: orderResponse.Specifications.MinTemp,
				MaxTempFahrenheit: orderResponse.Specifications.MaxTemp,
				Hazmat:            orderResponse.Specifications.Hazmat,
			},
			RateData: models.RateData{
				CustomerRateType: orderResponse.RateData.CustomerRate.Type,
				CustomerLineHaulCharge: models.ValueUnit{
					Val:  orderResponse.RateData.CustomerRate.LineHaul,
					Unit: orderResponse.RateData.CustomerRate.Currency,
				},
				CustomerRateNumUnits: orderResponse.RateData.CustomerRate.NumUnits,
				CustomerLineHaulRate: orderResponse.RateData.CustomerRate.LineHaul /
					orderResponse.RateData.CustomerRate.NumUnits,
				CustomerLineHaulUnit: orderResponse.RateData.CustomerRate.Unit,
				CustomerTotalCharge: models.ValueUnit{
					Val:  orderResponse.RateData.CustomerRate.TotalCharge,
					Unit: orderResponse.RateData.CustomerRate.Currency,
				},
			},
		},
		Reference: orderResponse.OrderID,
	}

	// Convert notes
	notes := make([]models.Note, len(orderResponse.Notes))
	for i, note := range orderResponse.Notes {
		notes[i] = models.Note{
			Note:      note.Note,
			UpdatedBy: note.UpdatedBy,
			Source:    note.Source,
		}
	}
	order.Notes = notes

	return order
}

// GetOrder retrieves an order from 3G TMS by its ID
func (t *ThreeG) GetOrder(ctx context.Context, externalOrderID string) (*models.Order, models.OrderAttributes, error) {
	url := fmt.Sprintf("%s/orders/?orderNum=%s", t.baseURL, externalOrderID)
	url = t.addAuthParams(url)

	// Log the request URL for debugging
	log.Info(ctx, "Making TMS request",
		zap.String("url", url),
		zap.String("orderID", externalOrderID))

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, models.OrderAttributes{}, fmt.Errorf("error creating request: %w", err)
	}

	resp, err := t.client.Do(req)
	if err != nil {
		return nil, models.OrderAttributes{}, fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return nil, models.OrderAttributes{}, fmt.Errorf("error reading response body: %w", err)
		}
		return nil, models.OrderAttributes{}, fmt.Errorf(
			"error response from 3G TMS: %s - %s",
			resp.Status,
			string(body),
		)
	}

	// Read the response body to see what we're actually getting
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, models.OrderAttributes{}, fmt.Errorf("error reading response body: %w", err)
	}

	// Try to decode as OrderResponse first
	var orderResponse OrderResponse
	if err := xml.Unmarshal(body, &orderResponse); err != nil {
		// If that fails, try to decode as LoadData (which contains orders)
		var loadData LoadData
		if err := xml.Unmarshal(body, &loadData); err != nil {
			// If both fail, return the original error with the response body for debugging
			return nil,
				models.OrderAttributes{},
				fmt.Errorf(
					"error decoding response (tried OrderResponse and LoadData): %w, response body: %s",
					err,
					string(body),
				)
		}

		// Look for the order in the LoadData.Orders
		for _, orderData := range loadData.Orders.Order {
			if orderData.OrdNum == externalOrderID {
				// Convert the order data to our Order model
				order := models.Order{
					OrderCoreInfo: models.OrderCoreInfo{
						ExternalOrderID: orderData.OrdNum,
						OrderTrackingID: orderData.OrdNum,
						Status:          orderData.OrderTMSStatus,
						Mode:            orderData.OrdType,
						FreightTerms:    orderData.FreightTerms,
						IsPrePayment:    orderData.IsPrePayment,
						Currency:        orderData.Currency,
						TotalWeight:     float64(parseFloat(orderData.TotalGrossWeight.WeightValue.Value)),
						TotalVolume:     float64(parseFloat(orderData.TotalGrossVolume.VolumeValue.Value)),
						PieceCount:      orderData.TotalPieceCount,
						IsHazmat:        orderData.IsHazmat,
						RateData: models.RateData{
							CustomerTotalCharge: models.ValueUnit{
								Val:  float32(parseFloat(orderData.TotalNetCharge.CurrencyValue.Value)),
								Unit: orderData.Currency,
							},
						},
						Specifications: models.Specifications{
							TotalWeight: models.ValueUnit{
								Val:  parseFloat(orderData.TotalGrossWeight.WeightValue.Value),
								Unit: orderData.TotalGrossWeight.WeightValue.UOM,
							},
						},
					},
					RequestedPickupDate:   parseTime(orderData.ScheduledEarlyPickup).Time,
					RequestedDeliveryDate: parseTime(orderData.ScheduledLatePickup).Time,
					Reference:             orderData.OrdNum,
					Type:                  orderData.OrdType,
				}

				// Add destination contact if available
				if orderData.DestinationContact.ContactName != "" {
					order.Consignee = models.Consignee{
						CompanyCoreInfo: models.CompanyCoreInfo{
							Contact: orderData.DestinationContact.ContactName,
							Phone:   orderData.DestinationContact.Phone1,
							Email:   orderData.DestinationContact.Email,
						},
					}
				}

				// Add comments if available
				if len(orderData.OrderComments.Comment) > 0 {
					notes := make([]models.Note, len(orderData.OrderComments.Comment))
					for i, comment := range orderData.OrderComments.Comment {
						notes[i] = models.Note{
							Note:      comment.CommentValue,
							UpdatedBy: orderData.EntityHeader.LastModifiedBy,
						}
					}
					order.Notes = notes
				}

				return &order, models.OrderAttributes{}, nil
			}
		}

		return nil, models.OrderAttributes{}, fmt.Errorf("order %s not found in LoadData response", externalOrderID)
	}

	// If we get here, it was a valid OrderResponse
	order := parseOrderFromResponse(orderResponse)
	return &order, models.OrderAttributes{}, nil
}

// CreateOrder creates a new order in 3G TMS
func (t *ThreeG) CreateOrder(_ context.Context, _ models.Order) (models.Order, error) {
	return models.Order{}, helpers.NotImplemented(models.ThreeG, "CreateOrder")
}

// UpdateOrder updates an existing order in 3G TMS
func (t *ThreeG) UpdateOrder(_ context.Context, _ models.Order) (models.Order, error) {
	// TODO: Implement order update in 3G TMS
	return models.Order{}, helpers.NotImplemented(models.ThreeG, "UpdateOrder")
}

// DeleteOrder deletes an order from 3G TMS
func (t *ThreeG) DeleteOrder(_ context.Context, _ string) error {
	return helpers.NotImplemented(models.ThreeG, "DeleteOrder")
}

// ListOrders lists orders from 3G TMS
func (t *ThreeG) ListOrders(_ context.Context, _ string, _ string, _ string) ([]models.Order, error) {
	return nil, helpers.NotImplemented(models.ThreeG, "ListOrders")
}

// AssociateLoadWithOrder associates a load with an order in 3G TMS
func (t *ThreeG) AssociateLoadWithOrder(ctx context.Context, orderID uint, loadID uint) error {
	// First, get the order to verify it exists
	order, _, err := t.GetOrder(ctx, strconv.FormatUint(uint64(orderID), 10))
	if err != nil {
		return fmt.Errorf("error getting order: %w", err)
	}

	// Get the load to verify it exists
	load, _, err := t.GetLoad(ctx, strconv.FormatUint(uint64(loadID), 10))
	if err != nil {
		return fmt.Errorf("error getting load: %w", err)
	}

	// Update the load's order ID in 3G TMS
	url := fmt.Sprintf("%s/loads/%s", t.baseURL, load.ExternalTMSID)
	url = t.addAuthParams(url)

	// Create request body with order ID
	reqBody := struct {
		OrderID string `xml:"orderId"`
	}{
		OrderID: order.Reference,
	}

	// Marshal request body to XML
	xmlBody, err := xml.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("error marshaling request body: %w", err)
	}

	// Create PUT request
	req, err := http.NewRequestWithContext(ctx, http.MethodPut, url, bytes.NewBuffer(xmlBody))
	if err != nil {
		return fmt.Errorf("error creating request: %w", err)
	}
	req.Header.Set("Content-Type", "application/xml")

	// Send request
	resp, err := t.client.Do(req)
	if err != nil {
		return fmt.Errorf("error making request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("error reading response body: %w", err)
		}
		return fmt.Errorf(
			"error response from 3G TMS: %s - %s",
			resp.Status,
			string(body),
		)
	}

	// Update the load's order ID in our database
	if err := orderdb.AddLoadToOrderInDB(ctx, orderID, loadID); err != nil {
		return fmt.Errorf("error updating load in database: %w", err)
	}

	return nil
}
