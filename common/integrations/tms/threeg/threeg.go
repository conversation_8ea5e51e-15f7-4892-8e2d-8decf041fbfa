package threeg

import (
	"context"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/http/cookiejar"
	"net/url"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	// Base URL for 3G TMS API
	baseURLFormat = "http://%s.3gtms.com/integration/rest"
)

// ThreeG implements the TMS interface for 3G TMS
type ThreeG struct {
	integration models.Integration
	baseURL     string
	client      *http.Client
	username    string
	password    string
}

// New creates a new 3G TMS integration
func New(ctx context.Context, integration models.Integration) (*ThreeG, error) {
	baseURL := fmt.Sprintf(baseURLFormat, integration.Tenant)

	// Decrypt the password
	password, err := crypto.DecryptAESGCM(ctx, string(integration.EncryptedPassword), nil)
	if err != nil {
		log.Error(
			ctx,
			"Error decrypting 3G TMS password",
			zap.String("username", integration.Username),
			zap.Uint("integration_id", integration.ID),
			zap.Error(err),
		)
		return nil, fmt.Errorf("error decrypting password: %w", err)
	}

	// Create a cookie jar for session management
	jar, err := cookiejar.New(nil)
	if err != nil {
		return nil, fmt.Errorf("error creating cookie jar: %w", err)
	}

	httpClient := otel.TracingHTTPClient()
	httpClient.Timeout = 20 * time.Second
	httpClient.Jar = jar

	return &ThreeG{
		integration: integration,
		baseURL:     baseURL,
		client:      httpClient,
		username:    integration.Username,
		password:    password,
	}, nil
}

// InitialOnboard implements the TMS interface
func (t *ThreeG) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	req models.OnboardTMSRequest,
) (models.OnboardTMSResponse, error) {
	// Validate required fields
	if req.Username == "" {
		return models.OnboardTMSResponse{}, errors.New("username is required for 3G TMS integration")
	}
	if req.Password == "" {
		return models.OnboardTMSResponse{}, errors.New("password is required for 3G TMS integration")
	}
	if req.Tenant == "" {
		return models.OnboardTMSResponse{}, errors.New("tenant (host) is required for 3G TMS integration")
	}

	// Create a test client to validate credentials
	encryptedPassword, err := crypto.EncryptAESGCM(ctx, req.Password, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("failed to encrypt password for test client: %w", err)
	}

	testClient, err := New(ctx, models.Integration{
		Username:          req.Username,
		EncryptedPassword: []byte(encryptedPassword),
		Tenant:            req.Tenant,
	})
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("error creating 3G TMS client: %w", err)
	}

	// Test the connection by making a simple API call
	// This returns an error but will return 200 if authenticated correctly
	testURL := fmt.Sprintf("%s/loads/", testClient.baseURL)
	testURL = testClient.addAuthParams(testURL)

	testReq, err := http.NewRequestWithContext(ctx, http.MethodGet, testURL, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("error creating test request: %w", err)
	}

	resp, err := testClient.client.Do(testReq)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("error connecting to 3G TMS: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusUnauthorized {
		return models.OnboardTMSResponse{}, errors.New("invalid credentials for 3G TMS")
	}

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return models.OnboardTMSResponse{}, fmt.Errorf("error reading response body: %w", err)
		}
		return models.OnboardTMSResponse{}, fmt.Errorf("error response from 3G TMS: %s - %s", resp.Status, string(body))
	}

	// Connection successful, return the response
	return models.OnboardTMSResponse{
		Username:          req.Username,
		EncryptedPassword: encryptedPassword,
		Tenant:            req.Tenant,
	}, nil
}

// GetTestLoads implements the TMS interface
func (t *ThreeG) GetTestLoads() map[string]bool {
	return map[string]bool{}
}

// addAuthParams adds authentication parameters to the URL
func (t *ThreeG) addAuthParams(urlStr string) string {
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return urlStr
	}

	q := parsedURL.Query()
	q.Set("username", t.username)
	q.Set("password", t.password)
	parsedURL.RawQuery = q.Encode()

	return parsedURL.String()
}

// GetCustomers implements the TMS interface
func (t *ThreeG) GetCustomers(_ context.Context) ([]models.TMSCustomer, error) {
	// Implementation would depend on 3G TMS API capabilities
	// For now, we'll return an empty slice
	return []models.TMSCustomer{}, helpers.NotImplemented(models.ThreeG, "GetCustomers")
}

// GetUsers implements the TMS interface
func (t *ThreeG) GetUsers(_ context.Context) ([]models.TMSUser, error) {
	// Implementation would depend on 3G TMS API capabilities
	// For now, we'll return an empty slice
	return []models.TMSUser{}, helpers.NotImplemented(models.ThreeG, "GetUsers")
}

// GetLocations implements the TMS interface
func (t *ThreeG) GetLocations(_ context.Context, _ ...models.TMSOption) ([]models.TMSLocation, error) {
	// Implementation would depend on 3G TMS API capabilities
	// For now, we'll return an empty slice
	return []models.TMSLocation{}, helpers.NotImplemented(models.ThreeG, "GetLocations")
}

// GetCarriers implements the TMS interface
func (t *ThreeG) GetCarriers(_ context.Context) ([]models.TMSCarrier, error) {
	// Implementation would depend on 3G TMS API capabilities
	// For now, we'll return an empty slice
	return []models.TMSCarrier{}, helpers.NotImplemented(models.ThreeG, "GetCarriers")
}

// CreateQuote implements the TMS interface
func (t *ThreeG) CreateQuote(_ context.Context, _ models.CreateQuoteBody) (*models.CreateQuoteResponse, error) {
	// Implementation would depend on 3G TMS API capabilities
	// For now, we'll return nil
	return nil, helpers.NotImplemented(models.ThreeG, "CreateQuote")
}
