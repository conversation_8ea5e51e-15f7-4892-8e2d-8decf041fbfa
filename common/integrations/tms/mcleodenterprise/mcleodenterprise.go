package mcleodenterprise

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	httputil "github.com/drumkitai/drumkit/common/helpers/http"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type McleodEnterprise struct {
	tms models.Integration
}

func New(tms models.Integration) (*McleodEnterprise, error) {
	if strings.TrimSpace(tms.Tenant) == "" {
		return nil, errors.New("missing tenant")
	}
	return &McleodEnterprise{tms: tms}, nil
}

func (m *McleodEnterprise) PostException(context.Context, *models.Load, models.Exception) error {
	return helpers.NotImplemented(models.McleodEnterprise, "PostException")
}

func (m *McleodEnterprise) GetExceptionHistory(context.Context, uint, string) ([]models.Exception, error) {
	return nil, helpers.NotImplemented(models.McleodEnterprise, "GetExceptionHistory")
}

func (m *McleodEnterprise) PostNote(context.Context, *models.Load, models.Note) ([]models.Note, error) {
	return nil, helpers.NotImplemented(models.McleodEnterprise, "PostNote")
}

func (m *McleodEnterprise) GetTestLoads() map[string]bool {
	// IMPORTANT NOTE: Dev is a backup store for prod and is often behind prod.
	// Thus, the same load ID may exist in dev and prod environments but be different because
	// one is a test load and the other is not. This is why we need to check the tenant ID
	// to ensure we don't allow dev writes to prod.
	isTridentDev := strings.Contains(m.tms.Tenant, ":5790") && strings.Contains(m.tms.Tenant, "trident")
	isFetchFreightDev := strings.Contains(m.tms.Tenant, ":8390") && strings.Contains(m.tms.Tenant, "fcfm")

	return map[string]bool{
		"0749978": isTridentDev,
		"0750005": isTridentDev, // ExternalTMSID
		"758948":  isTridentDev, // MovementID for 0750005

		"0750006": isTridentDev, // ExternalTMSID
		"758953":  isTridentDev, // MovementID for 0750006

		"0750013": isTridentDev, // ExternalTMSID

		"0037480": isFetchFreightDev, // ExternalTMSID
		"TBD":     isFetchFreightDev,
	}
}

func (m *McleodEnterprise) get(
	ctx context.Context,
	path string,
	queryParams url.Values,
	dst any,
	dataType s3backup.DataType,
	headers ...map[string]string,
) (err error) {

	fullPath := "ws/api/" + path
	if strings.HasPrefix(path, "/") {
		fullPath = "ws" + path
	}
	headerMap := make(map[string]string)
	headerMap["Accept"] = "application/json"
	headerMap["Authorization"] = "Token " + m.tms.AccessToken
	if len(headers) > 0 {
		for k, v := range headers[0] {
			headerMap[k] = v
		}
	}

	// Mcleod requires changedAfterDate to be in t-n format (today minus n days)
	if _, ok := queryParams["changedAfterDate"]; ok {
		dateStr := queryParams.Get("changedAfterDate")

		// Skip conversion if already in McLeod's t-n format
		if !strings.HasPrefix(dateStr, "t-") {
			var changedAfterDate time.Time
			var err error

			changedAfterDate, err = time.Parse(time.RFC3339, dateStr)
			if err != nil {
				// dateStr may be in YYYY-MM-DD format, try to parse that
				changedAfterDate, err = time.Parse("2006-01-02", dateStr)
				if err != nil {
					return fmt.Errorf(
						"failed to parse changedAfterDate (expected YYYY-MM-DD or RFC3339 format): %w",
						err,
					)
				}
			}

			daysSinceLastUpdate := int(time.Since(changedAfterDate).Hours() / 24)
			queryParams.Set("changedAfterDate", fmt.Sprintf("t-%d", daysSinceLastUpdate))
		}
	}

	addr := url.URL{Scheme: "https", Host: m.tms.Tenant, Path: fullPath, RawQuery: queryParams.Encode()}
	//nolint:bodyclose // False positive
	body, resp, err := httputil.GetBytesWithToken(ctx, m.tms, addr, headerMap, nil, dataType)
	if err != nil {
		return err
	}
	// log.Infof(ctx, "GET response body: %s", string(body))

	// Mcleod returns 200s or 204s with no content for unsuccessful requests
	if len(body) == 0 && dst != nil {
		body = fmt.Appendf(nil, "mcleod returned %d but no content", resp.StatusCode)
		resp.StatusCode = http.StatusNotFound

		return errtypes.NewHTTPResponseError(m.tms, &http.Request{Method: http.MethodGet, URL: &addr}, resp, body)
	}

	switch h := helpers.Or(headerMap["Accept"], headerMap["accept"]); h {
	case "application/json":
		if err = json.Unmarshal(body, dst); err != nil {
			return fmt.Errorf("json unmarshal failed: %w", err)
		}

	case "text/plain":
		if strPtr, ok := dst.(*string); ok {
			*strPtr = string(body)
		} else {
			log.Warn(ctx, "dst should be a *string for text/plain response", zap.Any("dst", dst))
		}

	default:
		log.Warn(ctx, "unsupported Accept header", zap.String("header", h))
	}

	return
}

func (m *McleodEnterprise) post(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	dataType s3backup.DataType,
	headers ...map[string]string,
) (err error) {

	fullPath := "ws/" + path
	if strings.HasPrefix(path, "/") {
		fullPath = "ws" + path

	}

	headerMap := make(map[string]string)
	headerMap["Accept"] = "application/json"
	headerMap["Content-Type"] = "application/json"
	headerMap["Authorization"] = "Token " + m.tms.AccessToken
	if len(headers) > 0 {
		for k, v := range headers[0] {
			headerMap[k] = v
		}
	}

	addr := url.URL{Scheme: "https", Host: m.tms.Tenant, Path: fullPath, RawQuery: queryParams.Encode()}
	//nolint:bodyclose // False positive
	body, resp, err := httputil.PostBytesWithToken(ctx, m.tms, addr, reqBody, headerMap, nil, dataType)
	if err != nil {
		return err
	}
	// log.Infof(ctx, "PUT response body: %s", string(body))

	if len(body) == 0 && dst != nil {
		body = []byte(fmt.Sprintf("mcleod returned %d but no content", resp.StatusCode))
		resp.StatusCode = http.StatusInternalServerError

		return errtypes.NewHTTPResponseError(m.tms, &http.Request{Method: http.MethodGet, URL: &addr}, resp, body)
	}

	switch h := helpers.Or(headerMap["Accept"], headerMap["accept"]); h {
	case "application/json":
		if err = json.Unmarshal(body, dst); err != nil {
			return fmt.Errorf("json unmarshal failed: %w", err)
		}

	case "text/plain":
		if strPtr, ok := dst.(*string); ok {
			*strPtr = string(body)
		} else {
			log.Warn(ctx, "dst should be a *string for text/plain response", zap.Any("dst", dst))
		}

	default:
		log.Warn(ctx, "unsupported Accept header", zap.String("header", h))
	}

	return
}

func (m *McleodEnterprise) put(
	ctx context.Context,
	path string,
	reqBody, dst any,
	dataType s3backup.DataType) (err error) {

	fullPath := "ws/" + path
	if strings.HasPrefix(path, "/") {
		fullPath = "ws" + path
	}

	headerMap := make(map[string]string)
	headerMap["Accept"] = "application/json"
	headerMap["Content-Type"] = "application/json"
	headerMap["Authorization"] = "Token " + m.tms.AccessToken

	addr := url.URL{Scheme: "https", Host: m.tms.Tenant, Path: fullPath}
	//nolint:bodyclose // False positive
	body, _, err := httputil.PutBytesWithToken(ctx, m.tms, addr, reqBody, headerMap, nil, dataType)
	if err != nil {
		return err
	}

	if err = json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}
	return
}
