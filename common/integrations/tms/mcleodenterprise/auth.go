package mcleodenterprise

import (
	"context"
	"encoding/base64"
	"errors"
	"fmt"

	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (m *McleodEnterprise) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	onboardRequest models.OnboardTMSRequest,
) (_ models.OnboardTMSResponse, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "InitialOnBoardMcleodEnterprise", otel.IntegrationAttrs(m.tms))
	defer func() { metaSpan.End(err) }()

	if onboardRequest.Username == "" || onboardRequest.Password == "" {
		return models.OnboardTMSResponse{}, errors.New("missing McleodEnterprise API credentials")
	}
	return m.authenticate(ctx, onboardRequest.Username, onboardRequest.Password, onboardRequest.Tenant)
}

// FIXME: Not working in Postman. Dev token had to be manually generated in app
func (m *McleodEnterprise) authenticate(
	ctx context.Context,
	username, password,
	tenantName string) (models.OnboardTMSResponse, error) {
	accessToken, err := m.getToken(ctx, username, password)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("getToken failed: %w", err)
	}
	log.Info(ctx, "Successfully authenticated McleodEnterprise client")

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, password, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("password encryption failed: %w", err)
	}

	return models.OnboardTMSResponse{
		AccessToken:       accessToken,
		EncryptedPassword: encryptedPassword,
		Username:          username,
		Tenant:            tenantName,
	}, err
}

func (m *McleodEnterprise) getToken(
	ctx context.Context,
	username, password string,
) (string, error) {
	var result LoginResp
	auth := "Basic " + base64.StdEncoding.EncodeToString([]byte(fmt.Sprintf("%s:%s", username, password)))
	headers := map[string]string{"Authorization": auth}
	err := m.post(ctx, "/users/login", nil, nil, &result, s3backup.TypeTokens, headers)
	if err != nil {
		return "", fmt.Errorf("POST token failed: %w", err)
	}

	return result.AccessToken, nil
}
