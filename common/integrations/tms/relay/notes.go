package relay

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (r *Relay) PostNote(ctx context.Context, load *models.Load, note models.Note) (notes []models.Note, err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms), otel.LoadAttrs(*load)...)

	ctx, metaSpan := otel.StartSpan(ctx, "PostNoteRelay", spanAttrs)
	defer func() { metaSpan.End(err) }()

	ccNote := models.CheckCall{
		FreightTrackingID: load.FreightTrackingID,
		Status:            "add tracking note",
		Notes:             note.Note,
		IsOnTime:          note.IsOnTime,
		IsException:       note.IsException,
		Source:            note.Source,
	}

	if err = r.PostCheckCall(ctx, load, ccNote); err != nil {
		return nil, fmt.Errorf("error posting note: %w", err)
	}

	// Give Relay time to update the carrier page
	time.Sleep(200 * time.Millisecond)

	err = r.parseCarrierInfoHTML(ctx, load)
	if err != nil {
		return nil, fmt.Errorf("error parsing carrier info: %w", err)
	}

	return load.Notes, nil
}

// Adapted from parseCheckCallHTML, but parses only Notes, not all types of check calls
func parseNotes(ctx context.Context, doc *goquery.Document) (notes []models.Note) {
	updateDivs := doc.Find("div.col-lg-3.stop-detail .tracking-update")

	updateDivs.Each(func(_ int, s *goquery.Selection) {
		var note models.Note
		title := strings.TrimSpace(s.Find(".update-title").Text())

		if !strings.EqualFold(title, "note captured") {
			return
		}

		note.Note = strings.TrimSpace(s.Find(".notes span").Text())
		note.Source = strings.TrimSpace(
			strings.ReplaceAll(s.Find(".source.d-inline").Text(), "Source:", ""))

		onTimeStr := strings.TrimSpace(strings.ReplaceAll(s.Find(".on-time").Text(), "On Time:", ""))
		if onTimeStr != "" {
			onTime := strings.ToLower(onTimeStr) == "true"
			note.IsOnTime = &onTime
		}

		exceptionStr := strings.TrimSpace(strings.ReplaceAll(s.Find(".has-issue, .issue").Text(), "Issue:", ""))
		if exceptionStr != "" {
			isException := strings.ToLower(exceptionStr) == "true"
			note.IsException = &isException
		}

		userStr := strings.TrimSpace(s.Find(".user").Text())
		prefix := "By User:"
		index := strings.Index(userStr, prefix)
		if index != -1 {
			// Extract the substring after "(Marked Dispatched/Marked Delivered/Updated/etc) By User:"
			note.UpdatedBy = strings.TrimSpace(userStr[index+len(prefix):])
		} else if userStr != "" {
			log.WarnNoSentry(ctx, "'By User:' prefix not found in "+userStr)
		}

		capturedDT := strings.TrimSpace(strings.ReplaceAll(s.Find(".captured-at").Text(), "Captured at:", ""))
		if capturedDT != "" {
			dt, err := parseTime(ctx, capturedDT, false, nyLoc)
			if err != nil {
				log.Error(ctx, "error parsing note captured time", zap.Error(err))
			} else {
				note.CreatedAt = dt
			}
		}

		notes = append(notes, note)
	})

	return notes
}
