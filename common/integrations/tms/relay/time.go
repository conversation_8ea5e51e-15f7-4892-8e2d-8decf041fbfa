package relay

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

// parseTime parses a time string that may be in one of two formats:
// 1. "1/2/2006 15:04 MST" (includes year and timezone)
// 2. "1/2 15:04" (omits year and timezone)
//
// 'tz' is an optional timezone to use for the parsed time. If provided, it will override the default UTC timezone.
// When the year is omitted, it is inferred based on the current date.
// The function also accepts a `future` hint to handle dates near the end of the year.
// If `future` is true, it assumes the date is in the future (e.g., an ETA).
// If `future` is false, it assumes the date is in the past (e.g., a timestamp for an event that already occurred).
var timeNowFunc = time.Now
var tzRegex = regexp.MustCompile(`\s+([A-Za-z]{2,3})$`)

func parseTime(ctx context.Context, dtStr string, future bool, tz *time.Location) (models.NullTime, error) {
	var (
		dt        time.Time
		err       error
		parsedLoc = time.UTC
	)
	dtStr = strings.TrimSpace(dtStr)

	// Check for a timezone abbreviation at the end of the string using regex.
	// If a valid one is found, we parse it and trim it from the original string.
	if matches := tzRegex.FindStringSubmatch(dtStr); len(matches) > 1 {
		abbreviation := strings.TrimSpace(matches[1])
		if loc, tzErr := timezone.GetLocationByAbbrv(abbreviation); tzErr == nil {
			parsedLoc = loc
			dtStr = tzRegex.ReplaceAllString(dtStr, "")
		}
	}

	// List of formats to try, from most specific to least specific.
	formats := []string{
		// For dates in the previous year, the year is included as 2 or 4 digits.
		"1/2/2006 15:04",
		"1/2/06 15:04",
	}

	for _, format := range formats {
		dt, err = time.Parse(format, dtStr)
		if err != nil {
			log.WarnNoSentry(ctx, "error parsing time with format", zap.String("format", format), zap.Error(err))
			continue
		}

		finalLoc := parsedLoc
		if tz != nil {
			finalLoc = tz // Timezone parameter takes precedence
		}

		if finalLoc != nil {
			dt = time.Date(
				dt.Year(),
				dt.Month(),
				dt.Day(),
				dt.Hour(),
				dt.Minute(),
				dt.Second(),
				dt.Nanosecond(),
				finalLoc,
			)
		}
		return models.NullTime{Time: dt, Valid: true}, nil

	}

	// For timestamps in current year/year-end rollover, the year is omitted.
	dt, err = time.Parse("1/2 15:04", dtStr)
	if err != nil {
		return models.NullTime{}, fmt.Errorf("unrecognized time format: %q", dtStr)
	}

	// Parsed with "1/2 15:04", so we need to infer the year.
	loc := parsedLoc
	if tz != nil {
		loc = tz // Timezone parameter takes precedence
	}
	if loc == nil {
		loc = time.UTC
	}

	now := timeNowFunc().In(loc)
	parsedTime := time.Date(now.Year(), dt.Month(), dt.Day(), dt.Hour(), dt.Minute(), 0, 0, loc)

	if future {
		// If the parsed date is in the past, it's likely for the next year.
		if parsedTime.Before(now) {
			parsedTime = parsedTime.AddDate(1, 0, 0)
		}
	} else {
		// If the parsed date is in the future, it's likely from the previous year.
		if parsedTime.After(now) {
			parsedTime = parsedTime.AddDate(-1, 0, 0)
		}
	}

	return models.NullTime{Time: parsedTime, Valid: true}, nil
}
