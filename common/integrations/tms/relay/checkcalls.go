package relay

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"go.opentelemetry.io/otel/attribute"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

const initCID = 5

func (r *Relay) GetCheckCallsHistory(
	ctx context.Context,
	loadID uint,
	freightTrackingID string,
) (checkcalls []models.CheckCall, err error) {

	spanAttrs := append(
		otel.IntegrationAttrs(r.tms),
		otel.SafeIntAttribute("load_id", loadID),
		attribute.String("freight_tracking_id", freightTrackingID),
	)

	ctx, metaSpan := otel.StartSpan(ctx, "GetCheckCallsHistoryRelay", spanAttrs)
	defer func() { metaSpan.End(err) }()

	respBody, err := r.getHTML(ctx, "tracking/tracking_load_detail/"+url.PathEscape(freightTrackingID),
		nil, s3backup.TypeCheckCalls)
	if err != nil {
		return nil, err
	}

	return r.parseCheckCallHTML(ctx, respBody, loadID, freightTrackingID)
}

// PostCheckCall posts a check call to Relay, with retry logic to handle Relay updating the CID parameter.
// If CID is incorrect, then Relay WS returns phx_error{}.
func (r *Relay) PostCheckCall(
	ctx context.Context,
	load *models.Load,
	cc models.CheckCall,
) (err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms),
		attribute.String("externalTMSID", load.ExternalTMSID))

	ctx, metaSpan := otel.StartSpan(ctx, "PostCheckCallRelay", spanAttrs)
	defer func() { metaSpan.End(err) }()

	badRequestErr := errtypes.HTTPResponseError{
		IntegrationName: r.tms.Name,
		IntegrationType: r.tms.Type,
		AxleTSPID:       r.tms.ID,
		ServiceID:       r.tms.ID,
		HTTPMethod:      "POST",
		URL:             "",
		StatusCode:      http.StatusBadRequest,
		ResponseHeaders: map[string][]string{},
		ResponseBody:    []byte("cannot POST check call without a carrier booked to the load"),
	}

	// Validate carrier exists
	if load.Carrier.Name == "" {
		return badRequestErr
	}

	// Determine form events based on check call status
	var modalFormEvent, submitFormEvent string
	stopIDsRequired := true

	switch strings.ToLower(cc.Status) {
	case "dispatch driver":
		stopIDsRequired = false
		modalFormEvent = "open_dispatch_driver_action_clicked"
		submitFormEvent = "dispatch_driver_clicked"
	case "in transit update":
		modalFormEvent = "open_capture_in_transit_udpate_action_clicked"
		submitFormEvent = "capture_in_transit_update_clicked"
	case "mark arrived at pickup", "mark arrived at delivery":
		modalFormEvent = "open_mark_arrived_at_stop_action_clicked"
		submitFormEvent = "mark_arrived_at_stop_clicked"
	case "mark loaded":
		modalFormEvent = "open_mark_loaded_action_clicked"
		submitFormEvent = "mark_loaded_clicked"
	case "mark delivered":
		modalFormEvent = "open_mark_delivered_action_clicked"
		submitFormEvent = "mark_delivered_clicked"
	case "add tracking note":
		stopIDsRequired = false
		modalFormEvent = "open_capture_tracking_note_action_clicked"
		submitFormEvent = "capture_tracking_note_clicked"
	}

	// Retry logic with incrementing CIDs
	startingCID := r.getWorkingCID(ctx)
	var lastErr error

	for attempt := 0; attempt < 3; attempt++ {
		currentCID := startingCID + attempt
		log.Info(ctx, "attempting check call submission",
			zap.Int("attempt", attempt+1),
			zap.Int("cid", currentCID))

		// Initialize tracking board action
		params, err := r.initTrackingBoardAction(ctx, load, modalFormEvent, stopIDsRequired)
		if err != nil {
			lastErr = fmt.Errorf("error initializing tracking board action on attempt %d: %w", attempt+1, err)
			log.WarnNoSentry(ctx, lastErr.Error())
			continue
		}

		// Create check call payload
		ccQuery, err := r.createCheckCallPayload(ctx, cc, params, load)
		if err != nil {
			badRequestErr.ResponseBody = []byte(err.Error())
			return badRequestErr
		}

		// Create submit message
		submitMsg := wsPayload{
			OperationName: "Tracking Board - Submit check call",
			Message: []any{
				"4",
				"10",
				params.PhxID,
				"event",
				map[string]any{
					"type":  "form",
					"event": submitFormEvent,
					"value": ccQuery.Encode(),
					"cid":   currentCID,
				},
			},
			NumExpectedResponses: 2,
		}

		// Send websocket message
		_, err = r.sendWebSocketMessages(
			ctx,
			true,
			"tracking_board/live/websocket",
			params.QueryParams,
			true,
			submitMsg,
		)
		if err == nil {
			r.updateWorkingCID(ctx, currentCID)
			return nil
		}

		lastErr = fmt.Errorf("error sending submit msg on attempt %d: %w", attempt+1, err)
		log.WarnNoSentry(ctx, "check call submission failed",
			zap.Int("attempt", attempt+1),
			zap.Int("cid", currentCID),
			zap.Error(err))
	}

	return fmt.Errorf("check call submission failed after all retry attempts: %w", lastErr)
}

// getWorkingCID retrieves the current working CID from Redis or returns the initial CID
func (r *Relay) getWorkingCID(ctx context.Context) int {
	cid := initCID
	if cidStr, err := redis.RDB.Get(ctx, "relay_checkcall_working_cid").Result(); err == nil {
		if parsedCID, err := strconv.Atoi(cidStr); err == nil {
			log.Info(ctx, "using relay_checkcall_working_cid from redis", zap.Int("cid", parsedCID))
			return parsedCID
		}
		log.WarnNoSentry(ctx, "error converting redis CID to int, falling back to initCID", zap.Error(err))
	}
	return cid
}

// updateWorkingCID updates the working CID in Redis
func (r *Relay) updateWorkingCID(ctx context.Context, cid int) {
	log.Info(ctx, "updating redis CID", zap.Int("cid", cid))
	if err := redis.RDB.Set(ctx, "relay_checkcall_working_cid", strconv.Itoa(cid), 0).Err(); err != nil {
		log.WarnNoSentry(ctx, "error setting relay_checkcall_working_cid", zap.Error(err), zap.Int("cid", cid))
	}
}

// createCheckCallPayload creates the payload for a check call based on its type and parameters
func (r *Relay) createCheckCallPayload(
	ctx context.Context,
	cc models.CheckCall,
	params trackingBoardParams,
	load *models.Load,
) (url.Values, error) {
	// Map codes
	reasonCode, err := mapReasonToCode(cc.Reason)
	if err != nil {
		return nil, fmt.Errorf("invalid late reason: %w", err)
	}

	sourceCode, err := mapSourceToCode(cc.Source)
	if err != nil {
		return nil, fmt.Errorf("invalid source: %w", err)
	}

	// Format dates
	formattedDate := cc.DateTimeWithoutTimezone.Time.In(time.UTC).Format(time.DateOnly)
	formattedTime := cc.DateTimeWithoutTimezone.Time.In(time.UTC).Format("15:04")

	var formattedOutDate, formattedOutTime string
	if cc.EndDateTimeWithoutTimezone.Valid {
		formattedOutDate = cc.EndDateTimeWithoutTimezone.Time.In(time.UTC).Format(time.DateOnly)
		formattedOutTime = cc.EndDateTimeWithoutTimezone.Time.In(time.UTC).Format("15:04")
	}

	var formattedETADate, formattedETATime string
	if cc.NextStopETAWithoutTimezone.Valid {
		formattedETADate = cc.NextStopETAWithoutTimezone.Time.In(time.UTC).Format(time.DateOnly)
		formattedETATime = cc.NextStopETAWithoutTimezone.Time.In(time.UTC).Format("15:04")
	}

	ccQuery := url.Values{}
	switch s := strings.ToLower(cc.Status); s {
	case "dispatch driver":
		var location string
		if cc.City != "" || cc.State != "" {
			location = fmt.Sprintf("%s, %s", cc.City, cc.State)
		}
		ccQuery = url.Values{
			"_csrf_token":                                          []string{params.CSRFToken},
			"dispatch_driver[truck_load_thing_id]":                 []string{params.LoadThingID},
			"dispatch_driver[source]":                              []string{sourceCode},
			"dispatch_driver[truck_location]":                      []string{location},
			"dispatch_driver[truck_location_time][_persistent_id]": []string{"0"},
			"dispatch_driver[truck_location_time][date]":           []string{formattedDate},
			"dispatch_driver[truck_location_time][time]":           []string{formattedTime},
			"dispatch_driver[eta][_persistent_id]":                 []string{"0"},
			"dispatch_driver[eta][date]":                           []string{formattedETADate},
			"dispatch_driver[eta][time]":                           []string{formattedETATime},
			"dispatch_driver[notes]":                               []string{cc.Notes},
			"dispatch_driver[truck_number]":                        []string{load.Carrier.ExternalTMSTruckID},
			"dispatch_driver[trailer_number]":                      []string{load.Carrier.ExternalTMSTrailerID},
			"dispatch_driver[driver_name]":                         []string{load.Carrier.FirstDriverName},
			"dispatch_driver[driver_phone_number]":                 []string{load.Carrier.FirstDriverPhone},
		}

		if strings.TrimSpace(load.Carrier.SecondDriverName) != "" ||
			strings.TrimSpace(load.Carrier.SecondDriverPhone) != "" {
			ccQuery.Set("dispatch_driver[additional_drivers][0][_persistent_id]", "0")
			ccQuery.Set("dispatch_driver[additional_drivers][0][name]", load.Carrier.SecondDriverName)
			ccQuery.Set("dispatch_driver[additional_drivers][0][phone]", load.Carrier.SecondDriverPhone)
		}

	case "in transit update":
		tz, err := timezone.GetTimezone(ctx, cc.City, cc.State, "")
		if err != nil {
			log.WarnNoSentry(ctx, "error getting check call timezone", zap.Error(err))
		}

		var etaStopID string
		if strings.ToLower(cc.NextStopID) == "pickup" {
			etaStopID = params.OrderedStopIDs[0]
		} else {
			etaStopID = params.OrderedStopIDs[len(params.OrderedStopIDs)-1]
		}

		ccQuery = url.Values{
			"_csrf_token": {params.CSRFToken},
			"capture_in_transit_update[truck_load_thing_id]":                     {params.LoadThingID},
			"capture_in_transit_update[source]":                                  {sourceCode},
			"capture_in_transit_update[eta_stop_id]":                             {etaStopID},
			"capture_in_transit_update[eta][_persistent_id]":                     {"0"},
			"capture_in_transit_update[eta][date]":                               {formattedETADate},
			"capture_in_transit_update[eta][time]":                               {formattedETATime},
			"capture_in_transit_update[selected_location_locality]":              {},
			"capture_in_transit_update[selected_location_administrative_region]": {cc.State},
			"capture_in_transit_update[selected_location_timezone]":              {tz},
			"capture_in_transit_update[truck_location]": {
				fmt.Sprintf("%s, %s", cc.City, cc.State),
			},
			"capture_in_transit_update[truck_location_date_time][_persistent_id]": {"0"},
			"capture_in_transit_update[truck_location_date_time][date]":           {formattedDate},
			"capture_in_transit_update[truck_location_date_time][time]":           {formattedTime},
			"capture_in_transit_update[reason]":                                   {reasonCode},
			"capture_in_transit_update[notes]":                                    {cc.Notes},
		}

	case "mark arrived at pickup", "mark arrived at delivery":
		var stopID string
		if strings.Contains(s, "at pickup") {
			stopID = params.OrderedStopIDs[0]
		} else {
			stopID = params.OrderedStopIDs[len(params.OrderedStopIDs)-1]
		}

		ccQuery = url.Values{
			"_csrf_token": {params.CSRFToken},
			"mark_arrived_at_stop[truck_load_thing_id]":          {params.LoadThingID},
			"mark_arrived_at_stop[source]":                       {sourceCode},
			"mark_arrived_at_stop[stop_id]":                      {stopID},
			"mark_arrived_at_stop[in_date_time][_persistent_id]": {"0"},
			"mark_arrived_at_stop[in_date_time][date]":           {formattedDate},
			"mark_arrived_at_stop[in_date_time][time]":           {formattedTime},
			"mark_arrived_at_stop[reason]":                       {reasonCode},
			"mark_arrived_at_stop[notes]":                        {cc.Notes},
		}

		if cc.IsOnTime != nil {
			ccQuery.Add("mark_arrived_at_stop[is_on_time]", fmt.Sprint(*cc.IsOnTime))
		}

	case "mark delivered":
		ccQuery = url.Values{
			"_csrf_token":                         {params.CSRFToken},
			"mark_delivered[truck_load_thing_id]": {params.LoadThingID},
			"mark_delivered[source]":              {sourceCode},
			"mark_delivered[stop_id]": {
				params.OrderedStopIDs[len(params.OrderedStopIDs)-1],
			},
			"mark_delivered[in_delivery_date_time][_persistent_id]":  {"0"},
			"mark_delivered[in_delivery_date_time][date]":            {formattedDate},
			"mark_delivered[in_delivery_date_time][time]":            {formattedTime},
			"mark_delivered[out_delivery_date_time][_persistent_id]": {"0"},
			"mark_delivered[out_delivery_date_time][date]":           {formattedOutDate},
			"mark_delivered[out_delivery_date_time][time]":           {formattedOutTime},
			"mark_delivered[reason]":                                 {reasonCode},
			"mark_delivered[notes]":                                  {cc.Notes},
		}

		if cc.IsOnTime != nil {
			ccQuery.Add("mark_delivered[is_on_time]", fmt.Sprint(*cc.IsOnTime))
		}

	case "mark loaded":
		ccQuery = url.Values{
			"_csrf_token":                                {params.CSRFToken},
			"mark_loaded[truck_load_thing_id]":           {params.LoadThingID},
			"mark_loaded[source]":                        {sourceCode},
			"mark_loaded[stop_id]":                       {params.OrderedStopIDs[0]},
			"mark_loaded[in_date_time][_persistent_id]":  {"0"},
			"mark_loaded[in_date_time][date]":            {formattedDate},
			"mark_loaded[in_date_time][time]":            {formattedTime},
			"mark_loaded[out_date_time][_persistent_id]": {"0"},
			"mark_loaded[out_date_time][date]":           {formattedOutDate},
			"mark_loaded[out_date_time][time]":           {formattedOutTime},
			"mark_loaded[reason]":                        {reasonCode},
			"mark_loaded[notes]":                         {cc.Notes},
		}

		// Loaded requires is_on_time, even though "delivered" does not
		if cc.IsOnTime == nil {
			ccQuery.Add("mark_loaded[is_on_time]", "true")
		} else {
			ccQuery.Add("mark_loaded[is_on_time]", fmt.Sprint(*cc.IsOnTime))
		}

	case "add tracking note":
		ccQuery = url.Values{
			"_csrf_token": {params.CSRFToken},
			"capture_tracking_note[truck_load_thing_id]": {params.LoadThingID},
			"capture_tracking_note[source]":              {sourceCode},
			"capture_tracking_note[notes]":               {cc.Notes},
		}
		if cc.IsOnTime != nil {
			ccQuery.Add("capture_tracking_note[is_on_time]", fmt.Sprint(*cc.IsOnTime))
		}
		if cc.IsException != nil {
			ccQuery.Add("capture_tracking_note[issue?]", fmt.Sprint(*cc.IsException))
		}
	}

	return ccQuery, nil
}

//nolint:unused
func isValidLateReason(reason string) bool {
	validOptions := map[string]struct{}{
		"":                                       {},
		"accident":                               {},
		"consignee related":                      {},
		"driver related":                         {},
		"mechanical breakdown":                   {},
		"other carrier related":                  {},
		"previous stop":                          {},
		"shipper related":                        {},
		"holiday":                                {},
		"weather":                                {},
		"return to shipper":                      {},
		"driver not available":                   {},
		"processing delay":                       {},
		"trailer not available":                  {},
		"insufficient time to complete delivery": {},
	}

	_, exists := validOptions[(strings.ToLower(reason))]
	return exists
}

func (r *Relay) parseCheckCallHTML(
	ctx context.Context,
	respBody []byte,
	loadID uint,
	freightID string,
) (checkcalls []models.CheckCall, err error) {

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return checkcalls, err
	}

	updateDivs := doc.Find("div.col-lg-3.stop-detail .tracking-update")

	updateDivs.Each(func(_ int, s *goquery.Selection) {
		status := strings.TrimSpace(s.Find(".update-title").Text())
		source := strings.TrimSpace(strings.ReplaceAll(s.Find(".source.d-inline").Text(), "Source:", ""))

		// NOTE: Staging and Prod relay have different HTML, so adding warning in case changes are pushed to prod
		// and we need to update this function
		if status == "" && source == "" && !strings.Contains(r.tms.Tenant, "training") {
			log.Warn(ctx, "check call status and source are empty, check if Relay Prod HTML has changed")
			return
		}

		cc := models.CheckCall{
			LoadID:            loadID,
			FreightTrackingID: freightID,
			Source:            source,
			Status:            status,
			Reason:            "",
			Timezone:          "",
			Notes:             strings.TrimSpace(s.Find(".notes span").Text()),
		}

		// If In Transit Update, capture truck location
		cityState := strings.TrimSpace(strings.ReplaceAll(s.Find(".truck-location").Text(), "Truck Location:", ""))
		cityStateSplit := strings.Split(cityState, ",")
		if len(cityStateSplit) > 0 {
			cc.City = strings.TrimSpace(cityStateSplit[0])

			if len(cityStateSplit) >= 2 {
				cc.State = strings.TrimSpace(cityStateSplit[1])

				cc.Timezone, err = timezone.GetTimezone(ctx, cc.City, cc.State, "")
				if err != nil {
					log.WarnNoSentry(ctx, "error looking up check call timezone", zap.Error(err))
				}
			}
		}

		// If Mark Loaded/Mark Delivered, capture warehouse's location
		if cc.City == "" {
			warehouseAddr := s.Find(".name span")
			cc.City = strings.TrimSpace(warehouseAddr.Eq(0).Text())
			cc.State = strings.TrimSpace(warehouseAddr.Eq(1).Text())
		}

		// If  Mark Arrival at Stop, capture stop number
		if cc.City == "" {
			cc.City = strings.TrimSpace(strings.ReplaceAll(s.Find(".stop-sequence").Text(), ":", ""))
		}

		onTimeStr := strings.TrimSpace(strings.ReplaceAll(s.Find(".on-time").Text(), "On Time:", ""))
		if onTimeStr != "" {
			onTime := strings.ToLower(onTimeStr) == "true"
			cc.IsOnTime = &onTime
		}

		exceptionStr := strings.TrimSpace(strings.ReplaceAll(s.Find(".has-issue, .issue").Text(), "Issue:", ""))
		if exceptionStr != "" {
			isException := strings.ToLower(exceptionStr) == "true"
			cc.IsException = &isException
		}

		userStr := strings.TrimSpace(s.Find(".user").Text())
		prefix1 := "By User:"
		prefix2 := "Exception Reported By:"

		index1 := strings.Index(userStr, prefix1)
		index2 := strings.Index(userStr, prefix2)
		switch {
		case index1 != -1:
			// Extract the substring after "(Marked Dispatched/Marked Delivered/Updated/etc) By User:"
			cc.Author = strings.TrimSpace(userStr[index1+len(prefix1):])
		case index2 != -1:
			// Extract the substring after "Exception Reported By:"
			cc.Author = strings.TrimSpace(userStr[index2+len(prefix2):])
		case userStr != "":
			log.Warn(ctx, "'By User:/Exception Reported By:' prefix not found in "+userStr)
		}

		// Parse truck location's datetime, if provided. Used for in-transit check calls
		dt := strings.TrimSpace(strings.ReplaceAll(s.Find(".truck-location-date-time").Text(), "Datetime:", ""))
		if dt != "" {
			datetime, err := parseTime(ctx, dt, false, nil)
			if err != nil {
				log.Warn(ctx, "error parsing check call datetime", zap.Error(err))
			} else {
				cc.DateTimeWithoutTimezone = datetime
			}
		}

		// Regular expression to match classes ending with '-time'
		regex := regexp.MustCompile(`-time\b`)

		// Find the element(s) with `-time` suffix (except out-date-time)
		s.Find("*").Each(func(_ int, s *goquery.Selection) {

			for _, class := range strings.Split(s.AttrOr("class", ""), " ") {
				if regex.MatchString(class) && class != "out-date-time" {
					dtRegex := regexp.MustCompile(`\b(\d{1,2}/\d{1,2}(/\d{4})? \d{1,2}:\d{2})\b`)

					text := strings.TrimSpace(s.Text())
					matches := dtRegex.FindStringSubmatch(text)

					if len(matches) > 1 {
						datetime, err := parseTime(ctx, matches[1], false, nil)
						if err != nil {
							log.Warn(ctx, "error parsing check call datetime", zap.Error(err))
						} else {
							cc.DateTimeWithoutTimezone = datetime
						}
					}
				}
			}
		})

		// Parse start datetime, if provided. Handle both "In:" and "In Datetime:" format.
		// Used for warehouse check calls
		inDT := strings.TrimSpace(strings.ReplaceAll(
			strings.ReplaceAll(s.Find(".in-date-time").Text(), "In:", ""), "In Datetime:", ""))
		if inDT != "" {
			datetime, err := parseTime(ctx, inDT, false, nil)
			if err != nil {
				log.Warn(ctx, "error parsing check call in-datetime", zap.Error(err))
			} else if cc.DateTimeWithoutTimezone == (models.NullTime{}) {
				cc.DateTimeWithoutTimezone = datetime
			}
		}

		// Parse End datetime, if provided. Handle both "Out:" and "Out Datetime:" format
		// Used for warehouse check calls
		outDT := strings.TrimSpace(strings.ReplaceAll(
			strings.ReplaceAll(s.Find(".out-date-time").Text(), "Out:", ""), "Out Datetime:", ""))
		if outDT != "" {
			datetime, err := parseTime(ctx, outDT, false, nil)
			if err != nil {
				log.Warn(ctx, "error parsing check call out-datetime", zap.Error(err))
			} else {
				cc.EndDateTimeWithoutTimezone = datetime
			}
		}

		etaDT := strings.TrimSpace(strings.ReplaceAll(s.Find(".eta").Text(), "ETA:", ""))
		if etaDT != "" {
			// For ETA, we can assume that if the date has passed, we should rollover to the next year
			datetime, err := parseTime(ctx, etaDT, true, nil)
			if err != nil {
				log.Warn(ctx, "error parsing check call ETA", zap.Error(err))
			} else {
				cc.NextStopETAWithoutTimezone = datetime
			}
		}

		capturedDT := strings.TrimSpace(strings.ReplaceAll(
			strings.ReplaceAll(s.Find(".captured-at").Text(), "Captured at:", ""), "Reported at:", ""))
		if capturedDT != "" {
			// Based on observations, jinyan.zang Relay account is always in ET timezone
			dt, err := parseTime(ctx, capturedDT, false, nyLoc)
			if err != nil {
				log.Error(ctx, "error parsing check call captured time", zap.Error(err))
			} else {
				cc.CapturedDatetime = dt
			}
		}

		checkcalls = append(checkcalls, cc)
	})

	return checkcalls, nil
}

// mapReasonToCode maps human-readable reasons to snake_cased codes and returns an error if the reason
// is not recognized. Important for NFI's tracking compliance with customers.
// Late Reason may be empty, depending on the type of check call
func mapReasonToCode(reason string) (string, error) {
	lowerReason := strings.ToLower(reason)

	switch lowerReason {
	case "":
		return "", nil
	case "accident":
		return "accident", nil
	case "consignee related":
		return "consignee_related", nil
	case "driver related":
		return "driver_related", nil
	case "mechanical breakdown":
		return "mechanical_breakdown", nil
	case "other carrier related":
		return "other_carrier_related", nil
	case "previous stop":
		return "previous_stop", nil
	case "shipper related":
		return "shipper_related", nil
	case "holiday":
		return "holiday", nil
	case "weather related", "weather":
		return "weather", nil
	case "return to shipper":
		return "return_to_shipper", nil
	case "driver not available":
		return "driver_not_available", nil
	case "processing delay":
		return "processing_delay", nil
	case "trailer not available":
		return "trailer_not_available", nil
	case "insufficient time to complete delivery":
		return "insufficient_time_to_complete_delivery", nil
	default:
		return "", fmt.Errorf("unrecognized reason: %s", reason)
	}
}

// mapSourceToCode maps human-readable reasons to snake_cased codes and returns an error if the reason
// is not recognized. Important for NFI's tracking compliance with customers.
// Source must always be provided, empty string is not valid
func mapSourceToCode(source string) (string, error) {
	source = strings.ToLower(source)

	switch source {
	case "driver":
		return "driver", nil
	case "web":
		return "web", nil
	case "dispatcher":
		return "dispatcher", nil
	case "shipper/receiver":
		return "shipper_receiver", nil
	default:
		return "", fmt.Errorf("unrecognized source: %s", source)
	}
}
