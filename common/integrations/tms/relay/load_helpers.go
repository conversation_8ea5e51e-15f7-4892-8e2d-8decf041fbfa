package relay

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/timezone"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	"github.com/drumkitai/drumkit/common/redis"
)

const initDriverEquipmentCID = 5

// submitDriverEquipmentInfo submits the driver equipment info update via websocket
func (r *Relay) submitDriverEquipmentInfo(
	ctx context.Context,
	params trackingBoardParams,
	updatesQuery url.Values,
	cid int,
) error {
	submitObj := make([]any, 5)
	submitObj[0] = "4"
	submitObj[1] = "10"
	submitObj[2] = params.PhxID
	submitObj[3] = "event"
	submitObj[4] = map[string]any{
		"type":  "form",
		"event": "add_driver_and_equipment_info_clicked",
		"value": updatesQuery.Encode(),
		"cid":   cid,
	}
	submitMsg := wsPayload{OperationName: "SubmitDriverEquipmentInfo", Message: submitObj, NumExpectedResponses: 2}

	_, err := r.sendWebSocketMessages(ctx, true, "tracking_board/live/websocket",
		params.QueryParams, true, submitMsg)
	return err
}

func (r *Relay) addDriverEquipmentInfo(ctx context.Context, updatedLoad *models.Load) (err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms), otel.LoadAttrs(*updatedLoad)...)

	ctx, metaSpan := otel.StartSpan(ctx, "addDriverEquipmentInfo", spanAttrs)
	defer func() { metaSpan.End(err) }()

	// Retry logic with incrementing CIDs
	startingCID := r.getWorkingDriverEquipmentCID(ctx)
	var lastErr error

	for attempt := 0; attempt < 3; attempt++ {
		currentCID := startingCID + attempt
		log.Info(ctx, "attempting driver equipment info submission",
			zap.Int("attempt", attempt+1),
			zap.Int("cid", currentCID))

		// Initialize tracking board action
		params, err := r.initTrackingBoardAction(
			ctx, updatedLoad, "open_add_driver_and_equipment_info_action_clicked", false)
		if err != nil {
			lastErr = fmt.Errorf("error initializing tracking board action on attempt %d: %w", attempt+1, err)
			log.WarnNoSentry(
				ctx,
				"driver equipment info initialization failed",
				zap.Int("attempt", attempt+1),
				zap.Int("cid", currentCID),
				zap.Error(err),
			)
			continue
		}

		// Create payload
		updatesQuery := r.createDriverEquipmentPayload(updatedLoad, params)

		// Submit update
		err = r.submitDriverEquipmentInfo(ctx, params, updatesQuery, currentCID)
		if err == nil {
			r.updateWorkingDriverEquipmentCID(ctx, currentCID)
			return nil
		}

		lastErr = fmt.Errorf("error sending submit msg on attempt %d: %w", attempt+1, err)
		log.WarnNoSentry(
			ctx,
			"driver equipment info submission failed",
			zap.Int("attempt", attempt+1),
			zap.Int("cid", currentCID),
			zap.Error(err),
		)
	}

	return fmt.Errorf("driver equipment info submission failed after all retry attempts: %w", lastErr)
}

// getWorkingDriverEquipmentCID retrieves the current working CID from Redis or returns the initial CID
func (r *Relay) getWorkingDriverEquipmentCID(ctx context.Context) int {
	cid := initDriverEquipmentCID
	if cidStr, err := redis.RDB.Get(ctx, "relay_driver_equipment_working_cid").Result(); err == nil {
		if parsedCID, err := strconv.Atoi(cidStr); err == nil {
			log.Info(ctx, "using relay_driver_equipment_working_cid from redis", zap.Int("cid", parsedCID))
			return parsedCID
		}
		log.WarnNoSentry(ctx, "error converting redis CID to int, falling back to initCID", zap.Error(err))
	}
	return cid
}

// updateWorkingDriverEquipmentCID updates the working CID in Redis
func (r *Relay) updateWorkingDriverEquipmentCID(ctx context.Context, cid int) {
	log.Info(ctx, "updating redis CID", zap.Int("cid", cid))
	if err := redis.RDB.Set(ctx, "relay_driver_equipment_working_cid", strconv.Itoa(cid), 0).Err(); err != nil {
		log.WarnNoSentry(ctx, "error setting relay_driver_equipment_working_cid", zap.Error(err), zap.Int("cid", cid))
	}
}

// createDriverEquipmentPayload creates the payload for driver equipment info updates
func (r *Relay) createDriverEquipmentPayload(
	updatedLoad *models.Load,
	params trackingBoardParams,
) url.Values {
	updatesQuery := url.Values{}
	updatesQuery.Add("_csrf_token", params.CSRFToken)
	updatesQuery.Add("add_driver_and_equipment_info[truck_load_thing_id]", params.LoadThingID)
	updatesQuery.Add("add_driver_and_equipment_info[driver_info][truck_load_thing_id]", params.LoadThingID)

	// NOTE: Cannot delete driver info once added, only changed
	updatesQuery.Add(
		"add_driver_and_equipment_info[driver_info][driver_name]",
		updatedLoad.Carrier.FirstDriverName)
	updatesQuery.Add(
		"add_driver_and_equipment_info[driver_info][driver_phone_number]",
		updatedLoad.Carrier.FirstDriverPhone)

	updatesQuery.Add(
		"add_driver_and_equipment_info[additional_drivers][0][name]",
		updatedLoad.Carrier.SecondDriverName,
	)
	updatesQuery.Add(
		"add_driver_and_equipment_info[additional_drivers][0][phone]",
		updatedLoad.Carrier.SecondDriverPhone,
	)

	truck := updatedLoad.Carrier.ExternalTMSTruckID
	updatesQuery.Add("add_driver_and_equipment_info[equipment_info][truck_load_thing_id]", params.LoadThingID)
	updatesQuery.Add("add_driver_and_equipment_info[equipment_info][truck_number]", truck)

	trailer := updatedLoad.Carrier.ExternalTMSTrailerID
	updatesQuery.Add("add_driver_and_equipment_info[equipment_info][trailer_number]", trailer)

	city := updatedLoad.Carrier.DispatchCity
	updatesQuery.Add(
		"add_driver_and_equipment_info[equipment_info][empty_city_state]",
		strings.Join([]string{city, updatedLoad.Carrier.DispatchState}, ", "),
	)

	// Required
	updatesQuery.Add("add_driver_and_equipment_info[empty_date_time][_persistent_id]", "0")
	updatesQuery.Add("add_driver_and_equipment_info[empty_date_time][date]", "")
	updatesQuery.Add("add_driver_and_equipment_info[empty_date_time][time]", "")

	return updatesQuery
}

func (r *Relay) addCarrierContactInfo(ctx context.Context, updatedLoad *models.Load) (err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms), otel.LoadAttrs(*updatedLoad)...)

	ctx, metaSpan := otel.StartSpan(ctx, "addCarrierContactInfo", spanAttrs)
	defer func() { metaSpan.End(err) }()

	params, err := r.initTrackingBoardAction(ctx, updatedLoad,
		"open_capture_tracking_contact_information_action_clicked", false)
	if err != nil {
		return err
	}

	// Update driver/equipment info
	updatesQuery := url.Values{}
	updatesQuery.Set("_csrf_token", params.CSRFToken)
	updatesQuery.Set("capture_tracking_contact_information[truck_load_thing_id]", params.LoadThingID)
	updatesQuery.Set("capture_tracking_contact_information[contact_name]", updatedLoad.Carrier.Dispatcher)
	updatesQuery.Set("capture_tracking_contact_information[contact_phone]", updatedLoad.Carrier.Phone)
	updatesQuery.Set("capture_tracking_contact_information[contact_email]", updatedLoad.Carrier.Email)
	updatesQuery.Set("capture_tracking_contact_information[notes]", updatedLoad.Carrier.Notes)

	submitObj := make([]any, 5)
	submitObj[0] = "4"
	submitObj[1] = "10"
	submitObj[2] = params.PhxID
	submitObj[3] = "event"
	submitObj[4] = map[string]any{
		"type":  "form",
		"event": "capture_tracking_contact_information_clicked",
		"value": updatesQuery.Encode(),
		"cid":   4,
	}
	submitMsg := wsPayload{Message: submitObj, NumExpectedResponses: 3}

	_, err = r.sendWebSocketMessages(ctx, true, "tracking_board/live/websocket", params.QueryParams, true, submitMsg)
	if err != nil {
		return fmt.Errorf("error sending second batch of WS msgs: %w", err)
	}

	return nil
}

func (r *Relay) schedulePlan(ctx context.Context, updatedLoad *models.Load) (err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms), otel.LoadAttrs(*updatedLoad)...)

	ctx, metaSpan := otel.StartSpan(ctx, "schedulePlan", spanAttrs)
	defer func() { metaSpan.End(err) }()

	params, err := r.initPlanningBoardAction(ctx, updatedLoad, "SchedulePlan", true)
	if err != nil {
		return fmt.Errorf("initPlanningBoardAction error: %w", err)
	}
	// Update appt info
	var pickupLoc *time.Location
	if updatedLoad.Pickup.Timezone != "" {
		pickupLoc, err = time.LoadLocation(updatedLoad.Pickup.Timezone)
		if err != nil {
			log.ErrorNoSentry(ctx, "error loading pickup location from load object", zap.Error(err))

			pickupLoc, err = timezone.GetLocationByCity(ctx,
				updatedLoad.Pickup.City, updatedLoad.Pickup.State, updatedLoad.Pickup.Country)
		}
	} else {
		pickupLoc, err = timezone.GetLocationByCity(ctx,
			updatedLoad.Pickup.City, updatedLoad.Pickup.State, updatedLoad.Pickup.Country)
	}
	if err != nil {
		return fmt.Errorf("error loading pickupLoc: %w", err)
	}

	var delLoc *time.Location
	if updatedLoad.Consignee.Timezone != "" {
		delLoc, err = time.LoadLocation(updatedLoad.Consignee.Timezone)
		if err != nil {
			log.ErrorNoSentry(ctx, "error loading delivery location from load object", zap.Error(err))

			delLoc, err = timezone.GetLocationByCity(ctx,
				updatedLoad.Consignee.City, updatedLoad.Consignee.State, updatedLoad.Consignee.Country)
		}
	} else {
		delLoc, err = timezone.GetLocationByCity(ctx,
			updatedLoad.Consignee.City, updatedLoad.Consignee.State, updatedLoad.Consignee.Country)
	}
	if err != nil {
		return fmt.Errorf("error loading delLoc: %w", err)
	}

	// Both state = "new" and state = "edit" work for updating existing appt times
	// But must use state = "cleared" to remove times
	values := url.Values{}
	values.Set("_csrf_token", params.CSRFToken)
	values.Set("schedule_plan[relay_reference_number]", updatedLoad.ExternalTMSID)
	values.Set("schedule_plan[schedule_id]", params.ScheduleID)

	// Add Stops
	values.Set("schedule_plan[stops][0][_persistent_id]", "0")
	values.Set("schedule_plan[stops][0][stop_id]", params.OrderedStopIDs[0])
	values.Set("schedule_plan[stops][0][stop_type]", "pickup")
	values.Set("schedule_plan[stops][0][schedule_type]", "appointment")
	values.Set("schedule_plan[stops][0][schedule][_persistent_id]", "0")
	values.Set("schedule_plan[stops][0][reference]", updatedLoad.Pickup.RefNumber)

	values.Set("schedule_plan[stops][0][reference]", updatedLoad.Pickup.RefNumber)

	if updatedLoad.Pickup.ApptStartTime.Valid {
		values.Set("schedule_plan[stops][0][state]", "new")
		values.Set("schedule_plan[stops][0][schedule][appointment_time][_persistent_id]", "0")
		values.Set("schedule_plan[stops][0][schedule][appointment_time][date]",
			updatedLoad.Pickup.ApptStartTime.Time.In(pickupLoc).Format(time.DateOnly))
		values.Set("schedule_plan[stops][0][schedule][appointment_time][time]",
			updatedLoad.Pickup.ApptStartTime.Time.In(pickupLoc).Format("15:04"))
	} else {
		values.Set("schedule_plan[stops][0][state]", "cleared")
	}

	// TODO Support multi-stop
	values.Set("schedule_plan[stops][1][_persistent_id]", "1")
	values.Set("schedule_plan[stops][1][stop_id]", params.OrderedStopIDs[len(params.OrderedStopIDs)-1])
	values.Set("schedule_plan[stops][1][stop_type]", "delivery")
	values.Set("schedule_plan[stops][1][schedule_type]", "appointment")
	values.Set("schedule_plan[stops][1][schedule][_persistent_id]", "0")
	values.Set("schedule_plan[stops][1][reference]", updatedLoad.Consignee.RefNumber)
	values.Set("schedule_plan[stops][1][reference]", updatedLoad.Consignee.RefNumber)

	if updatedLoad.Consignee.ApptStartTime.Valid {
		values.Set("schedule_plan[stops][1][state]", "new")
		values.Set("schedule_plan[stops][1][schedule][appointment_time][_persistent_id]", "0")
		values.Set("schedule_plan[stops][1][schedule][appointment_time][date]",
			updatedLoad.Consignee.ApptStartTime.Time.In(delLoc).Format(time.DateOnly))
		values.Set("schedule_plan[stops][1][schedule][appointment_time][time]",
			updatedLoad.Consignee.ApptStartTime.Time.In(delLoc).Format("15:04"))
	} else {
		values.Set("schedule_plan[stops][1][state]", "cleared")
	}

	updates := PatchSchedulePlan{
		Type:  "form",
		Event: "save",
		Value: values.Encode(),
		Cid:   6,
	}

	updateObj := make([]any, 5)
	updateObj[0] = "4"
	updateObj[1] = "10"
	updateObj[2] = params.PhxID
	updateObj[3] = "event"
	updateObj[4] = updates

	updateMsg := wsPayload{Message: updateObj, NumExpectedResponses: 2}

	_, err = r.sendWebSocketMessages(ctx, true, "planning_board/live/websocket", params.QueryParams, true, updateMsg)
	if err != nil {
		return fmt.Errorf("error sending update msg: %w", err)
	}

	return nil
}

// Wrapper function for dispatch driver check call
func (r *Relay) dispatchDriver(ctx context.Context, curLoad *models.Load, updatedLoad *models.Load) (err error) {
	spanAttrs := append(otel.IntegrationAttrs(r.tms), otel.LoadAttrs(*updatedLoad)...)

	ctx, metaSpan := otel.StartSpan(ctx, "dispatchDriver", spanAttrs)
	defer func() { metaSpan.End(err) }()

	// Dispatched time is required by Relay but optional in sidebar, so we default to now if empty
	if !updatedLoad.Carrier.DispatchedTime.Valid {
		updatedLoad.Carrier.DispatchedTime = models.ToValidNullTime(time.Now())
	}

	updatedLoad.Carrier.DispatchedTime.Time = updatedLoad.Carrier.DispatchedTime.Time.Truncate(time.Minute)
	updatedLoad.Carrier.ExpectedPickupTime.Time = updatedLoad.Carrier.ExpectedPickupTime.Time.Truncate(time.Minute)

	// Relay requires driver's datetime when the driver is dispatched, but does not require the truck location.
	// To resolve, if the user provides the driver's city & state, then we normalize using the driver's timezone.
	// Otherwise, we use the user's timestamp.
	var normalizedDispTime time.Time

	city := updatedLoad.Carrier.DispatchCity
	state := updatedLoad.Carrier.DispatchState
	if city != "" && state != "" {
		dispLoc, err := timezone.GetTimezone(ctx, city, state, "")
		if err != nil {
			log.WarnNoSentry(ctx, "error getting dispatch location's timezone", zap.Error(err))
		} else {
			dispTime, err := timezone.NormalizeToUTC(updatedLoad.Carrier.DispatchedTime.Time, dispLoc)
			if err != nil {
				log.WarnNoSentry(ctx, "error normalizing dispatchTime in driver's TZ", zap.Error(err))
			} else {
				log.Info(ctx, "normalizing dispatchTime in driver's TZ", zap.String("timezone", dispLoc))
				normalizedDispTime = dispTime
			}
		}
	}
	if normalizedDispTime.IsZero() {
		log.WarnNoSentry(ctx, "using updatedLoad.Carrier.DispatchedTime.Time")
		normalizedDispTime = updatedLoad.Carrier.DispatchedTime.Time
	}

	// Required
	eta, err := timezone.NormalizeToUTC(updatedLoad.Carrier.ExpectedPickupTime.Time, curLoad.Pickup.Timezone)
	if err != nil {
		return fmt.Errorf("error normalizing expectedPickupTime: %w", err)
	}

	cc := models.CheckCall{
		LoadID:                     curLoad.ID,
		FreightTrackingID:          curLoad.FreightTrackingID,
		City:                       updatedLoad.Carrier.DispatchCity,
		State:                      updatedLoad.Carrier.DispatchState,
		Status:                     "dispatch driver",
		Notes:                      updatedLoad.Carrier.Notes,
		DateTimeWithoutTimezone:    models.ToValidNullTime(normalizedDispTime),
		Timezone:                   "",
		NextStopETAWithoutTimezone: models.ToValidNullTime(eta),
		NextStopID:                 "pickup",
		Source:                     updatedLoad.Carrier.DispatchSource,
	}

	if err := r.PostCheckCall(ctx, updatedLoad, cc); err != nil {
		return fmt.Errorf("error submitting dispatch driver check call: %w", err)
	}

	// NOTE: Delay to allow tracking page to update before we re-parse it; otherwise we get false errors
	time.Sleep(150 * time.Millisecond)
	return nil
}

type scheduleParams struct {
	ScheduleID     string
	OrderedStopIDs []string
}

// Parses schedule and stop IDs from schedule plan aka appointment modal
func parseSchedulePlanIDs(input []byte) (res scheduleParams, err error) {
	//nolint:lll
	reScheduleID := regexp.MustCompile(`id=\\"schedule-plan-form_schedule_id\\" name=\\"schedule_plan\[schedule_id\]\\"\s*type=\\"hidden\\"\s*value=\\"([^"]*)\\"`)

	inputStr := string(input)
	scheduleMatches := reScheduleID.FindStringSubmatch(inputStr)

	if count := len(scheduleMatches); count < 2 {
		return res, fmt.Errorf("unexpected scheduleMatches regex matches length: %d", count)
	}
	res.ScheduleID = scheduleMatches[1]

	// If empty, could be because the load is no longer on the planning board
	// (shouldn't happen unless load is cancelled or delivered (?))
	// OR web app has changed HTML and code is broken
	if res.ScheduleID == "" {
		return res, errors.New("parsed empty scheduleID")
	}

	// Parse stop IDs
	//nolint:lll
	stopIDRegex := regexp.MustCompile(`<input\s+id=\\"schedule-plan-form_stops_\d+_stop_id\\"\s+name=\\"schedule_plan\[stops\]\[\d+\]\[stop_id\]\\"\s+type=\\"hidden\\"\s+value=\\"([a-zA-Z0-9]+\-[a-zA-Z0-9\-]+)\\"`)

	matches := stopIDRegex.FindAllStringSubmatch(string(input), -1)
	if len(matches) < 2 {
		return res, fmt.Errorf("unexpected number of stopID matches: %d (%v)", len(matches), matches)
	}

	for i, match := range matches {
		if len(match) < 2 {
			return res, fmt.Errorf("unexpected number of stopID submatches in match %d: %d (%v)", i, len(match), match)
		}
		res.OrderedStopIDs = append(res.OrderedStopIDs, match[1])
	}

	return res, nil
}

func parseTrackingBoardIDs(input []byte) (loadThingID string, customerID string, err error) {
	reTruckLoad := regexp.MustCompile(`phx-value-truck_load_thing_id=\\"([^"]*)\\"`)
	reCustomerID := regexp.MustCompile(`phx-value-customer_id=\\"([^"]*)\\"`)

	inputStr := string(input)
	truckLoadMatches := reTruckLoad.FindStringSubmatch(inputStr)
	customerIDMatches := reCustomerID.FindStringSubmatch(inputStr)

	if count := len(truckLoadMatches); count < 2 {
		return "", "", fmt.Errorf("unexpected truckLoadMatches regex matches length: %d", count)
	}
	loadThingID = truckLoadMatches[1]

	if count := len(customerIDMatches); count < 2 {
		return "", "", fmt.Errorf("unexpected customerIDMatches regex matches length: %d", count)
	}
	customerID = customerIDMatches[1]

	// 1 ID empty and the other not empty should not happen in production unless Relay UI changes
	// If both are empty, could be because the load isn't on the tracking board yet or there's a bug
	if loadThingID == "" || customerID == "" {
		return loadThingID, customerID, fmt.Errorf("parsed empty truckLoadThingID (%s) and/or customerID (%s)",
			loadThingID, customerID)
	}

	return loadThingID, customerID, nil
}

type planningBoardParams struct {
	CSRFToken   string
	PhxID       string
	ScheduleID  string
	QueryParams url.Values
	ConnectMsg  wsPayload
	SearchObj   wsPayload
	ModalObj    wsPayload
	// When a load moves off the load board and to the tracking board, we're no longer able to parse the stop IDs.
	// Then, loadDB.UpsertLoad will reset the stop IDs to empty. Thus, we must parse the stop IDs from the tracking
	// board HTML when performing an action on it.
	OrderedStopIDs []string
}

func (r *Relay) initPlanningBoardAction(
	ctx context.Context,
	load *models.Load,
	modalEvent string,
	stopIDsRequired bool,
) (res planningBoardParams, err error) {

	// First, get planning board HTML to capture CSRF
	respBody, err := r.getHTML(ctx, "planning_board", nil, "")
	if err != nil {
		return res, errors.New("error loading planning board")
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return res, fmt.Errorf("error reading document: %w", err)
	}

	csrf := strings.TrimSpace(doc.Find(`meta[name="csrf-token"]`).AttrOr("content", ""))
	if csrf == "" {
		return res, errors.New("parsed empty csrf token")
	}

	// MESSAGE 1: Connect
	trackStatic1 := doc.Find(`[phx-track-static][href]`).AttrOr("href", "")
	if trackStatic1 == "" {
		return res, errors.New("parsed empty trackStatic1")
	}

	trackStatic2 := doc.Find(`[phx-track-static][src]`).AttrOr("src", "")
	if trackStatic2 == "" {
		return res, errors.New("parsed empty trackStatic2")
	}

	phxMain := doc.Find(`[data-phx-main]`)
	phxSession := strings.TrimSpace(phxMain.AttrOr("data-phx-session", ""))
	if phxSession == "" {
		return res, errors.New("parsed empty phxSession")
	}

	phxStatic := strings.TrimSpace(phxMain.AttrOr("data-phx-static", ""))
	if phxStatic == "" {
		return res, errors.New("parsed empty phxStatic")
	}

	phxID := strings.TrimSpace(phxMain.AttrOr("id", ""))
	if phxID == "" {
		return res, errors.New("parsed empty phxID")
	}
	phxID = "lv:" + phxID

	queryParams := url.Values{}
	queryParams.Set("timezone", "America/New_York")
	queryParams.Set("_csrf_token", csrf)
	queryParams.Set("_live_referer", "undefined")
	queryParams.Set("vsn", "2.0.0")
	queryParams.Set("_track_static[0]", r.baseURL+trackStatic1)
	queryParams.Set("_track_static[1]", r.baseURL+trackStatic2)
	queryParams.Set("_mounts", "0")

	connectMsgObj := make([]any, 5)
	connectMsgObj[0] = "4"
	connectMsgObj[1] = "4"
	connectMsgObj[2] = phxID
	connectMsgObj[3] = "phx_join"
	connectMsgObj[4] = PlanningBoardConnectMessage{
		URL: r.baseURL + "/planning_board",
		Params: Params{
			CsrfToken:   csrf,
			Timezone:    "America/New_York",
			TrackStatic: []string{queryParams.Get("_track_static[0]"), queryParams.Get("_track_static[1]")},
			Mounts:      0,
		},
		Session: phxSession,
		Static:  phxStatic,
	}

	// MESSAGE 2: Search load
	searchQuery := url.Values{}
	searchQuery.Set("filter[search_string]", load.ExternalTMSID)

	searchObj := make([]any, 5)
	searchObj[0] = "4"
	searchObj[1] = "8"
	searchObj[2] = phxID
	searchObj[3] = "event"
	searchObj[4] = WebSocketMsgCore{
		Type:  "form",
		Event: "filter_update",
		Value: searchQuery.Encode(),
	}

	// MESSAGE 3: OPEN MODAL
	modalObj := make([]any, 5)
	modalObj[0] = "4"
	modalObj[1] = "9"
	modalObj[2] = phxID
	modalObj[3] = "event"
	modalObj[4] = map[string]any{
		"type":  "click",
		"event": "modal",
		"value": map[string]string{
			"type":                   modalEvent,
			"relay_reference_number": load.ExternalTMSID,
		},
	}

	connectMsg := wsPayload{OperationName: "Planning Board - Connect", Message: connectMsgObj, NumExpectedResponses: 1}
	searchMsg := wsPayload{OperationName: "Planning Board - Search load", Message: searchObj, NumExpectedResponses: 2}
	modalMsg := wsPayload{OperationName: "Planning Board - Open appt modal", Message: modalObj, NumExpectedResponses: 1}

	// Search for load and parse IDs needed to submit form
	var resp []byte
	if stopIDsRequired {
		resp, err = r.sendWebSocketMessages(ctx, false, "planning_board/live/websocket",
			queryParams, false, connectMsg, searchMsg, modalMsg)
	} else {
		resp, err = r.sendWebSocketMessages(ctx, false, "planning_board/live/websocket",
			queryParams, false, connectMsg, searchMsg)
	}
	if err != nil {
		return res, fmt.Errorf("error with 1st batch of WS messages: %w", err)
	}

	var params scheduleParams
	// StopIDs can only be parsed if modal is opened
	if stopIDsRequired {
		params, err = parseSchedulePlanIDs(resp)
		if err != nil {
			return res, fmt.Errorf("error parsing schedulePlanIDs: %w", err)
		}
	}

	res = planningBoardParams{
		CSRFToken:      csrf,
		PhxID:          phxID,
		ScheduleID:     params.ScheduleID,
		QueryParams:    queryParams,
		ConnectMsg:     connectMsg,
		SearchObj:      searchMsg,
		ModalObj:       modalMsg,
		OrderedStopIDs: params.OrderedStopIDs,
	}

	return res, nil
}

type trackingBoardParams struct {
	CSRFToken   string
	PhxID       string
	LoadThingID string
	CustomerID  string
	QueryParams url.Values
	ConnectMsg  wsPayload
	SearchObj   wsPayload
	ModalObj    wsPayload
	// When a load moves off the load board and to the tracking board, we're no longer able to parse the stop IDs.
	// Then, loadDB.UpsertLoad will reset the stop IDs to empty. Thus, we must parse the stop IDs from the tracking
	// board HTML when performing an action on it.
	OrderedStopIDs []string
}

// Helper function to initialize action on tracking board (i.e. updating carrier info, submitting check call)
// Opens tracking board HTML, parses relevant IDs, and builds and sends
//  1. connect, 2) search load, and 3) open modal messages to websocket
func (r *Relay) initTrackingBoardAction(
	ctx context.Context,
	load *models.Load,
	modalEvent string,
	stopIDsRequired bool,
) (res trackingBoardParams, err error) {
	// First, get tracking board HTML to capture CSRF
	query := url.Values{}
	query.Set("filters[booked_by_id]", "")
	query.Set("filters[customer_id]", "")
	query.Set("filters[delivery_appt]", "")
	query.Set("filters[pickup_appt]", "")
	query.Set("filters[search_term]", load.FreightTrackingID)
	query.Set("filters[status]", "all")
	query.Set("sort_by[1][direction]", "desc")
	query.Set("sort_by[1][field]", "booking_id")

	respBody, err := r.getHTML(ctx, "tracking_board", query, "")
	if err != nil {
		return res, errors.New("error loading tracking board")
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(respBody))
	if err != nil {
		return res, fmt.Errorf("error reading document: %w", err)
	}

	csrf := strings.TrimSpace(doc.Find(`meta[name="csrf-token"]`).AttrOr("content", ""))
	if csrf == "" {
		return res, errors.New("parsed empty csrf token")
	}

	// MESSAGE 1: Connect
	trackStatic1 := doc.Find(`[phx-track-static][href]`).AttrOr("href", "")
	if trackStatic1 == "" {
		return res, errors.New("parsed empty trackStatic1")
	}

	trackStatic2 := doc.Find(`[phx-track-static][src]`).AttrOr("src", "")
	if trackStatic2 == "" {
		return res, errors.New("parsed empty trackStatic2")
	}

	phxMain := doc.Find(`[data-phx-main]`)
	phxSession := strings.TrimSpace(phxMain.AttrOr("data-phx-session", ""))
	if phxSession == "" {
		return res, errors.New("parsed empty phxSession")
	}

	phxStatic := strings.TrimSpace(phxMain.AttrOr("data-phx-static", ""))
	if phxStatic == "" {
		return res, errors.New("parsed empty phxStatic")
	}

	phxID := strings.TrimSpace(phxMain.AttrOr("id", ""))
	if phxID == "" {
		return res, errors.New("parsed empty phxID")
	}
	phxID = "lv:" + phxID

	queryParams := url.Values{}
	queryParams.Set("timezone", "America/New_York")
	queryParams.Set("_csrf_token", csrf)
	queryParams.Set("_live_referer", "undefined")
	queryParams.Set("vsn", "2.0.0")
	queryParams.Set("_track_static[0]", r.baseURL+trackStatic1)
	queryParams.Set("_track_static[1]", r.baseURL+trackStatic2)
	queryParams.Set("_mounts", "0")

	connectMsgObj := make([]any, 5)
	connectMsgObj[0] = "4"
	connectMsgObj[1] = "4"
	connectMsgObj[2] = phxID
	connectMsgObj[3] = "phx_join"
	connectMsgObj[4] = PlanningBoardConnectMessage{
		URL: r.baseURL + "/tracking_board?" +
			strings.ReplaceAll(strings.ReplaceAll(query.Encode(), "%5B", "["), "%5D", "]"),
		Params: Params{
			CsrfToken:   csrf,
			Timezone:    "America/New_York",
			TrackStatic: []string{queryParams.Get("_track_static[0]"), queryParams.Get("_track_static[1]")},
			Mounts:      0,
		},
		Session: phxSession,
		Static:  phxStatic,
	}
	connectMsg := wsPayload{OperationName: "Tracking Board - Connect", Message: connectMsgObj, NumExpectedResponses: 2}

	// MESSAGE 2: SEARCH LOAD
	searchQuery := url.Values{}
	searchQuery.Add("customer_id", "")
	searchQuery.Add("status", "all")
	searchQuery.Add("search_term", load.ExternalTMSID)
	searchQuery.Add("booked_by_id", "")
	searchQuery.Add("pickup_appt", "")
	searchQuery.Add("delivery_appt", "")
	searchQuery.Add("_target", "search_term")

	searchObj := make([]any, 5)
	searchObj[0] = "4"
	searchObj[1] = "8"
	searchObj[2] = phxID
	searchObj[3] = "event"
	searchObj[4] = WebSocketMsgCore{
		Type:  "form",
		Event: "filter_applied",
		Value: searchQuery.Encode(),
	}
	searchMsg := wsPayload{OperationName: "Tracking Board - Search load", Message: searchObj, NumExpectedResponses: 2}

	// Search for load and parse IDs needed to submit form
	resps, err := r.sendWebSocketMessages(ctx, false, "tracking_board/live/websocket",
		queryParams, false, connectMsg, searchMsg)
	if err != nil {
		return res, fmt.Errorf("error sending first batch of WS msgs: %w", err)
	}

	thingID, customerID, err := parseTrackingBoardIDs(resps)
	if err != nil {
		return res, err
	}

	// MESSAGE 3: OPEN MODAL
	modalObj := make([]any, 5)
	modalObj[0] = "4"
	modalObj[1] = "9"
	modalObj[2] = phxID
	modalObj[3] = "event"
	modalObj[4] = map[string]any{
		"type":  "click",
		"event": modalEvent,
		"value": map[string]string{
			"truck_load_thing_id": thingID,
			"customer_id":         customerID,
		},
	}
	modalMsg := wsPayload{
		OperationName:        "Tracking Board - Open check call modal",
		Message:              modalObj,
		NumExpectedResponses: 2,
	}

	res.CSRFToken = csrf
	res.PhxID = phxID
	res.LoadThingID = thingID
	res.CustomerID = customerID
	res.QueryParams = queryParams
	res.ConnectMsg = connectMsg
	res.SearchObj = searchMsg
	res.ModalObj = modalMsg

	// Open modal
	resps, err = r.sendWebSocketMessages(ctx, false, "tracking_board/live/websocket", queryParams, false, modalMsg)
	if err != nil {
		return res, fmt.Errorf("error sending WS msgs to open modal: %w", err)
	}

	// If stop IDs expected and needed in modal, parse them
	if stopIDsRequired {
		stopIDRegex := regexp.MustCompile(`<option\s+value=\\"([a-zA-Z0-9]+\-[a-zA-Z0-9\-]+)\\"`)

		matches := stopIDRegex.FindAllStringSubmatch(string(resps), -1)
		// If Mark Loaded/Mark Delivered and there's only 1 pickup/delivery stop, modal lists only 1 ID
		// If Mark Arrived at Stop, then modal lists 2 IDs
		// If Mark Arrived at Stop, then modal lists 2 IDs
		if len(matches) < 1 {
			return res, fmt.Errorf("unexpected number of stopID matches: %d", len(matches))
		}

		for i, match := range matches {
			if len(match) < 2 {
				return res, fmt.Errorf("unexpected number of stopID submatches in match %d: %d", i, len(match))
			}
			res.OrderedStopIDs = append(res.OrderedStopIDs, match[1])
		}
	}

	return res, nil

}

// A load is not searchable on the Load Board via websocket once a carrier is booked, but it always has a
// https://relaytms.com/sourcing/load_board_load_detail/{loadNumber} page whose HTML we can parse
//
//nolint:unparam
func (r *Relay) getLoadFromLoadBoard(ctx context.Context,
	freightTrackingID string,
) (load models.Load, attrs models.LoadAttributes, err error) {
	attrs = r.planningBoardAttributes()
	// Create placeholder request to represent web socket request in metrics
	req := &http.Request{URL: &url.URL{
		Scheme:   wsScheme,
		Host:     r.tms.Tenant,
		Path:     "socket/websocket",
		RawQuery: "filter_text=" + freightTrackingID,
	}}

	respBody, err := r.getLoadViaWebSocket(ctx, freightTrackingID)
	if err != nil {
		return load, attrs, fmt.Errorf("error getting from load board websocket: %w", err)
	}
	if str := string(respBody); strings.Contains(string(respBody), `"status":"error"`) ||
		strings.Contains(str, "phx_error") {
		resp := &http.Response{StatusCode: http.StatusInternalServerError}
		httplog.LogHTTPResponseCode(ctx, r.tms, http.StatusInternalServerError)

		return load, attrs, errtypes.NewHTTPResponseError(r.tms, req, resp, respBody)
	}

	var arrayList []any
	err = json.Unmarshal(respBody, &arrayList)
	if err != nil {
		return load, attrs, fmt.Errorf("error marshaling into JSON list: %w", err)
	}

	if count := len(arrayList); count < 5 {
		log.WarnNoSentry(ctx, "unexpected response body", zap.Any("responseBody", arrayList))

		return load, attrs, fmt.Errorf("expected 5 items in response body, received %d, view logs for details", count)
	}

	loadData, err := json.Marshal(arrayList[4])
	if err != nil {
		return load, attrs, fmt.Errorf("error marshaling 5th item in JSON array: %w", err)
	}

	var loadResp LoadResp
	err = json.Unmarshal(loadData, &loadResp)
	if err != nil {
		return load, attrs, fmt.Errorf("error unmarshaling response body: %w", err)
	}

	switch loadResp.Data.TotalRows {
	case 0:
		// Not on load board, return 404
		resp := &http.Response{StatusCode: http.StatusNotFound}
		err = errtypes.NewHTTPResponseError(r.tms, req, resp, respBody)
		httplog.LogHTTPResponseCode(ctx, r.tms, http.StatusNotFound)

		return load, attrs, err

	case 1:
		// If load is on Load Board, then no carrier has been assigned yet.
		// No need to check tracking page
		httplog.LogHTTPResponseCode(ctx, r.tms, http.StatusOK)
		load, err = r.toLoadModel(ctx, loadResp.Data.Loads[0])
		return load, attrs, err

	default:
		for _, row := range loadResp.Data.Loads {
			idToCheck := fmt.Sprint(row.LoadNumber)
			// If the ID starts with 7 or 8, then check the booking ID
			if strings.HasPrefix(freightTrackingID, "7") || strings.HasPrefix(freightTrackingID, "8") {
				idToCheck = fmt.Sprint(row.BookingID)
			}

			if idToCheck == freightTrackingID {
				httplog.LogHTTPResponseCode(ctx, r.tms, http.StatusOK)
				load, err = r.toLoadModel(ctx, row)
				return load, attrs, err
			}
		}
		resp := &http.Response{StatusCode: http.StatusConflict}
		err = errtypes.NewHTTPResponseError(r.tms, req, resp, respBody)
		httplog.LogHTTPResponseCode(ctx, r.tms, resp.StatusCode)

		return load, attrs, err
	}
}
