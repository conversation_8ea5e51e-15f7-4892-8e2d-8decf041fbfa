package tai

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	httputil "github.com/drumkitai/drumkit/common/helpers/http"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	loadDB "github.com/drumkitai/drumkit/common/rds/load"
)

const (
	tspHost     = "atl.taicloud.net"
	tokenHeader = "x-api-key"
)

type Tai struct {
	tms models.Integration
}

func New(ctx context.Context, tms models.Integration) *Tai {
	log.With(ctx, zap.Uint("axleTMSID", tms.ID), zap.String("tmsName", "tai"), zap.String("host", tspHost))
	return &Tai{tms: tms}
}

func (t Tai) GetTestLoads() map[string]bool {
	return map[string]bool{
		"119895482": true, // Cargo Express Test Load
	}
}

func (t Tai) GetCustomers(ctx context.Context) (customers []models.TMSCustomer, _ error) {
	// return customers, helpers.NotImplemented(models.Tai, "GetCustomers")
	return loadDB.GetListOfUniqueCustomers(ctx, t.tms.ID)
}

func (t Tai) GetUsers(context.Context) (users []models.TMSUser, _ error) {
	return users, helpers.NotImplemented(models.Tai, "GetUsers")
}

// Implemented as a DB function with webhooks
func (t Tai) GetLocations(context.Context, ...models.TMSOption) (res []models.TMSLocation, _ error) {
	return res, nil
}

func (t Tai) GetCarriers(context.Context) ([]models.TMSCarrier, error) {
	return nil, helpers.NotImplemented(models.Tai, "GetCarriers")
}
func (t Tai) PostException(context.Context, *models.Load, models.Exception) error {
	return helpers.NotImplemented(models.Tai, "PostException")
}

func (t Tai) GetExceptionHistory(context.Context, uint, string) ([]models.Exception, error) {
	return []models.Exception{}, helpers.NotImplemented(models.Tai, "GetExceptionHistory")
}

func (t Tai) PostNote(context.Context, *models.Load, models.Note) ([]models.Note, error) {
	return nil, helpers.NotImplemented(models.Tai, "PostNote")
}

func (t Tai) get(ctx context.Context, path string, queryParams url.Values, dst any, dataType s3backup.DataType) error {
	addr := url.URL{Scheme: "https", Host: tspHost, Path: path, RawQuery: queryParams.Encode()}
	var auth string
	// todo: after discussion change here
	headerMap := make(map[string]string)
	headerMap[tokenHeader] = t.tms.APIKey
	body, _, err := httputil.GetBytesWithToken(ctx, t.tms, addr, headerMap, &auth, dataType)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return nil
}

func (t Tai) post(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody any,
	dst any,
	dataType s3backup.DataType,
) (err error) {
	addr := url.URL{Scheme: "https", Host: tspHost, Path: path, RawQuery: queryParams.Encode()}
	var auth string
	headerMap := make(map[string]string)
	headerMap[tokenHeader] = t.tms.APIKey
	body, _, err := httputil.PostBytesWithToken(ctx, t.tms, addr, reqBody, headerMap, &auth, dataType)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(body, dst); dst != nil && err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return
}

func (t Tai) put(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	dataType s3backup.DataType,
) (err error) {
	addr := url.URL{Scheme: "https", Host: tspHost, Path: path, RawQuery: queryParams.Encode()}
	var auth string
	headerMap := make(map[string]string)
	headerMap[tokenHeader] = t.tms.APIKey
	body, _, err := httputil.PutBytesWithToken(ctx, t.tms, addr, reqBody, headerMap, &auth, dataType)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return
}
