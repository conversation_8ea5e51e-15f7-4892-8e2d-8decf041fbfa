package mercurygate

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/http/cookiejar"
	"os"
	"strings"
	"time"

	"go.uber.org/zap"
	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/redis"
)

type SerializableCookie struct {
	Name    string
	Value   string
	Path    string
	Domain  string
	Expires time.Time
}

type SerializableMercuryGate struct {
	Config  *Config
	Cookies []SerializableCookie
}

type RateLimiter struct {
	RequestNumber int
	UpdatedAt     time.Time
}

func (m *MercuryGate) MarshalMercuryGate() (string, error) {
	serializableCookies := make([]SerializableCookie, len(m.cookies))

	for i, cookie := range m.cookies {
		serializableCookies[i] = SerializableCookie{
			Name:    cookie.Name,
			Value:   cookie.Value,
			Path:    cookie.Path,
			Domain:  cookie.Domain,
			Expires: cookie.Expires,
		}
	}

	sa := SerializableMercuryGate{
		Config:  m.config,
		Cookies: serializableCookies,
	}

	bytes, err := json.Marshal(sa)
	return string(bytes), err
}

func UnmarshalMercuryGate(ctx context.Context, data string) (*MercuryGate, error) {
	var sm SerializableMercuryGate
	err := json.Unmarshal([]byte(data), &sm)
	if err != nil {
		return nil, err
	}

	cookies := make([]*http.Cookie, len(sm.Cookies))
	for i, sc := range sm.Cookies {
		cookies[i] = &http.Cookie{
			Name:    sc.Name,
			Value:   sc.Value,
			Path:    sc.Path,
			Domain:  sc.Domain,
			Expires: sc.Expires,
		}
	}

	client := otel.TracingHTTPClient()
	cookieJar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
	if err != nil {
		log.Debug(ctx, "could not create MercuryGate cookie jar", zap.Error(err))
		client = otel.TracingHTTPClient()
	} else {
		client.Jar = cookieJar
	}

	m := &MercuryGate{
		httpClient: client,
		cookies:    cookies,
		config:     sm.Config,
	}

	return m, nil
}

func redisClientKey(serviceID uint, tmsID uint) string {
	return fmt.Sprintf("service-%d-tms-%d-mercurygate", serviceID, tmsID)
}

func retrieveRedisClient(ctx context.Context, serviceID uint, tmsID uint) (*MercuryGate, error) {
	if redis.RDB == nil {
		msg := "redis RDB has not been initialized"

		if os.Getenv("APP_ENV") == "dev" {
			log.Debug(ctx, msg)
		} else {
			log.Warn(ctx, msg)
		}
		return nil, nil
	}

	str, err := redis.RDB.Get(ctx, redisClientKey(serviceID, tmsID)).Result()
	if err != nil {
		log.Warn(ctx, "failed to get MercuryGate session from Redis", zap.Error(err))
	}

	if str != "" {
		client, err := UnmarshalMercuryGate(ctx, str)

		if err != nil {
			log.Warn(ctx, "failed to unmarshal MercuryGate session from Redis", zap.Error(err))
			return nil, err
		}

		if client != nil {
			log.Info(ctx, "re-using existing MercuryGate client")
		}

		return client, nil
	}

	return nil, nil
}

func (m *MercuryGate) dumpRedisClient(ctx context.Context) {
	if redis.RDB == nil {
		msg := "redis RDB has not been initialized"

		if os.Getenv("APP_ENV") == "dev" {
			log.Debug(ctx, msg)
		} else {
			log.Warn(ctx, msg)
		}
		return
	}

	serializedMercuryGateSession, err := m.MarshalMercuryGate()
	if err != nil {
		log.Warn(ctx, "failed to json marshal MercuryGate session for Redis", zap.Error(err))
	}

	redisKey := redisClientKey(m.tms.ServiceID, m.tms.ID)
	err = redis.RDB.Set(ctx, redisKey, serializedMercuryGateSession, 3*time.Hour).Err()
	if err != nil {
		log.Warn(ctx, "failed to set MercuryGate session in Redis", zap.Error(err))
	}
}

func (m *MercuryGate) sessionRefreshCheck(ctx context.Context, htmlBody string) {
	if strings.Contains(strings.ToLower(htmlBody), "your session has timed out due to inactivity") ||
		strings.Contains(strings.ToLower(htmlBody), "no session credentials found") {

		log.Info(ctx, "refreshing mercurygate session")

		err := m.Auth(ctx)
		if err != nil {
			log.Error(ctx, "mercurygate session refresh failed", zap.Error(err))
			return
		}

		if redis.RDB != nil {
			serializedMercuryGateSession, err := m.MarshalMercuryGate()
			if err != nil {
				log.Warn(ctx, "failed to json marshal MercuryGate session for Redis", zap.Error(err))
			}

			redisKey := redisClientKey(m.tms.ServiceID, m.tms.ID)
			err = redis.RDB.Set(ctx, redisKey, serializedMercuryGateSession, 3*time.Hour).Err()
			if err != nil {
				log.Warn(ctx, "failed to set MercuryGate session in Redis", zap.Error(err))
			}
		}
	}
}

func (m *MercuryGate) rateLimitCheck(ctx context.Context) error {
	// TODO: Find a way to emulate redis in CI tests
	disableRateLimit := os.Getenv("DISABLE_RATE_LIMIT")

	if disableRateLimit == "true" {
		return nil
	}

	if redis.RDB == nil {
		return errors.New("failed to connect to Redis")
	}

	var limiter *RateLimiter
	var serializedLimiter string

	key := fmt.Sprintf("%s-mercurygate-rate-limiter", m.config.Tenant)
	str, err := redis.RDB.Get(ctx, key).Result()
	if err != nil {
		log.WarnNoSentry(ctx, "failed to get MercuryGate rate limiter from Redis",
			zap.String("tenant", m.config.Tenant), zap.Error(err))
	}

	if str != "" {
		limiter = &RateLimiter{}
		if err = json.Unmarshal([]byte(str), limiter); err != nil {
			log.Warn(ctx, "failed to unmarshal MercuryGate rate limiter from Redis",
				zap.String("tenant", m.config.Tenant), zap.Error(err))

			return err
		}

		if limiter.RequestNumber >= 600 && time.Since(limiter.UpdatedAt).Seconds() <= 60 {
			return errors.New("MercuryGate rate limit exceeded")
		}
	} else {
		limiter = &RateLimiter{
			RequestNumber: 0,
			UpdatedAt:     time.Now(),
		}
	}

	limiter.RequestNumber++
	limiter.UpdatedAt = time.Now()

	bytes, err := json.Marshal(limiter)
	if err != nil {
		log.Warn(ctx, "failed to marshal MercuryGate rate limiter",
			zap.String("tenant", m.config.Tenant), zap.Error(err))

		return err
	}

	serializedLimiter = string(bytes)
	err = redis.RDB.Set(ctx, key, serializedLimiter, 1*time.Minute).Err()
	if err != nil {
		log.Warn(ctx, "failed to set MercuryGate rate limiter in Redis",
			zap.String("tenant", m.config.Tenant), zap.Error(err))

		return err
	}

	return nil
}
