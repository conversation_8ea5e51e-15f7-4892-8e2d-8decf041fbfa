package freightflow

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	// Production TMS host
	tmsHost = "api.freightflow.co"
	// tmsHost = "api.preprod.freightflow.com"
)

type FreightFlow struct {
	tms models.Integration
}

func New(ctx context.Context, tms models.Integration) (*FreightFlow, error) {
	log.With(ctx, zap.Uint("drumkitTMSID", tms.ID), zap.String("tmsName", "freightflow"))

	if tms.Disabled {
		return nil, errtypes.DisabledIntegrationError(tms)
	}

	return &FreightFlow{
		tms: tms,
	}, nil
}

// UpdateLoad implements the TMS interface
func (f *FreightFlow) UpdateLoad(
	context.Context,
	*models.Load,
	*models.Load,
) (load models.Load, attrs models.LoadAttributes, err error) {
	return models.Load{}, models.LoadAttributes{}, helpers.NotImplemented(models.FreightFlow, "UpdateLoad")
}

// GetLoadsByIDType implements the TMS interface
func (f *FreightFlow) GetLoadsByIDType(
	context.Context,
	string,
	string,
) (loads []models.Load, attrs models.LoadAttributes, err error) {
	return nil, models.LoadAttributes{}, helpers.NotImplemented(models.FreightFlow, "GetLoadsByIDType")
}

// GetTestLoads implements the TMS interface
func (f *FreightFlow) GetTestLoads() map[string]bool {
	return map[string]bool{}
}

// PostException implements the TMS interface
func (f *FreightFlow) PostException(
	context.Context,
	*models.Load,
	models.Exception,
) (err error) {
	return helpers.NotImplemented(models.FreightFlow, "PostException")
}

// GetExceptionHistory implements the TMS interface
func (f *FreightFlow) GetExceptionHistory(
	context.Context,
	uint,
	string,
) (exceptions []models.Exception, err error) {
	return []models.Exception{}, helpers.NotImplemented(models.FreightFlow, "GetExceptionHistory")
}

// PostNote implements the TMS interface
func (f *FreightFlow) PostNote(
	context.Context,
	*models.Load,
	models.Note,
) (notes []models.Note, err error) {
	return nil, helpers.NotImplemented(models.FreightFlow, "PostNote")
}

// GetUsers implements the TMS interface
func (f *FreightFlow) GetUsers(context.Context) (users []models.TMSUser, err error) {
	return nil, helpers.NotImplemented(models.FreightFlow, "GetUsers")
}

// GetCarriers implements the TMS interface
func (f *FreightFlow) GetCarriers(context.Context) (carriers []models.TMSCarrier, err error) {
	return nil, helpers.NotImplemented(models.FreightFlow, "GetCarriers")
}

// HTTP methods
func (f *FreightFlow) get(
	ctx context.Context,
	path string,
	queryParams url.Values,
	out any,
	_ s3backup.DataType,
) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetFreightFlow", otel.IntegrationAttrs(f.tms))
	defer func() { metaSpan.End(err) }()

	reqURL := (&url.URL{
		Scheme:   "https",
		Host:     tmsHost,
		Path:     path,
		RawQuery: queryParams.Encode(),
	}).String()

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, reqURL, nil)
	if err != nil {
		return fmt.Errorf("failed to build GET %s request: %w", reqURL, err)
	}

	token, err := f.getAuthToken(ctx)
	if err != nil {
		return fmt.Errorf("failed to get auth token: %w", err)
	}

	req.Header.Add("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")

	httpClient := otel.TracingHTTPClient()
	res, err := httpClient.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, f.tms, err)
		return fmt.Errorf("failed to execute GET %s request: %w", reqURL, err)
	}
	defer res.Body.Close()

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("failed to read GET %s response body: %w", reqURL, err)
	}

	if res.StatusCode != http.StatusOK {
		httplog.LogHTTPResponseCode(ctx, f.tms, res.StatusCode)
		return fmt.Errorf("GET %s request failed with status %d: %s", reqURL, res.StatusCode, string(resBody))
	}

	if err = json.Unmarshal(resBody, out); err != nil {
		return fmt.Errorf("failed to unmarshal GET %s response: %w", reqURL, err)
	}

	return nil
}

func (f *FreightFlow) post(
	ctx context.Context,
	path string,
	queryParams url.Values,
	body any,
	out any,
	headerMap map[string]string,
	_ s3backup.DataType,
) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "PostFreightFlow", otel.IntegrationAttrs(f.tms))
	defer func() { metaSpan.End(err) }()

	reqURL := (&url.URL{
		Scheme:   "https",
		Host:     tmsHost,
		Path:     path,
		RawQuery: queryParams.Encode(),
	}).String()

	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("failed to marshal POST %s request body: %w", reqURL, err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, reqURL, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return fmt.Errorf("failed to build POST %s request: %w", reqURL, err)
	}

	token, err := f.getAuthToken(ctx)
	if err != nil {
		return fmt.Errorf("failed to get auth token: %w", err)
	}

	req.Header.Add("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")

	for k, v := range headerMap {
		req.Header.Set(k, v)
	}

	httpClient := otel.TracingHTTPClient()
	res, err := httpClient.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, f.tms, err)
		return fmt.Errorf("failed to execute POST %s request: %w", reqURL, err)
	}
	defer res.Body.Close()

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("failed to read POST %s response body: %w", reqURL, err)
	}

	if res.StatusCode != http.StatusOK {
		httplog.LogHTTPResponseCode(ctx, f.tms, res.StatusCode)
		return fmt.Errorf("POST %s request failed with status %d: %s", reqURL, res.StatusCode, string(resBody))
	}

	if err = json.Unmarshal(resBody, out); err != nil {
		return fmt.Errorf("failed to unmarshal POST %s response: %w", reqURL, err)
	}

	return nil
}

func (f *FreightFlow) postNoAuth(
	ctx context.Context,
	path string,
	queryParams url.Values,
	body any,
	out any,
	headerMap map[string]string,
	_ s3backup.DataType,
) (err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "PostNoAuthFreightFlow", otel.IntegrationAttrs(f.tms))
	defer func() { metaSpan.End(err) }()

	reqURL := (&url.URL{
		Scheme:   "https",
		Host:     tmsHost,
		Path:     path,
		RawQuery: queryParams.Encode(),
	}).String()

	bodyBytes, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("failed to marshal POST %s request body: %w", reqURL, err)
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, reqURL, bytes.NewBuffer(bodyBytes))
	if err != nil {
		return fmt.Errorf("failed to build POST %s request: %w", reqURL, err)
	}

	req.Header.Set("Content-Type", "application/json")

	for k, v := range headerMap {
		req.Header.Set(k, v)
	}

	httpClient := otel.TracingHTTPClient()
	res, err := httpClient.Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, f.tms, err)
		return fmt.Errorf("failed to execute POST %s request: %w", reqURL, err)
	}
	defer res.Body.Close()

	resBody, err := io.ReadAll(res.Body)
	if err != nil {
		return fmt.Errorf("failed to read POST %s response body: %w", reqURL, err)
	}

	if res.StatusCode != http.StatusOK {
		httplog.LogHTTPResponseCode(ctx, f.tms, res.StatusCode)
		return fmt.Errorf("POST %s request failed with status %d: %s", reqURL, res.StatusCode, string(resBody))
	}

	if err = json.Unmarshal(resBody, out); err != nil {
		return fmt.Errorf("failed to unmarshal POST %s response: %w", reqURL, err)
	}

	return nil
}

// getAuthToken gets the auth token, refreshing if necessary
func (f *FreightFlow) getAuthToken(ctx context.Context) (string, error) {
	if f.tms.NeedsRefresh() {
		if _, err := f.RefreshToken(ctx); err != nil {
			return "", fmt.Errorf("failed to refresh token: %w", err)
		}
	}

	return f.tms.AccessToken, nil
}

// GetCustomers implements the TMS interface
func (f *FreightFlow) GetCustomers(context.Context) (customers []models.TMSCustomer, err error) {
	return nil, helpers.NotImplemented(models.FreightFlow, "GetCustomers")
}

// GetLocations implements the TMS interface
func (f *FreightFlow) GetLocations(
	context.Context,
	...models.TMSOption,
) (locations []models.TMSLocation, err error) {
	return nil, helpers.NotImplemented(models.FreightFlow, "GetLocations")
}

// CreateQuote implements the TMS interface
func (f *FreightFlow) CreateQuote(
	context.Context,
	models.CreateQuoteBody,
) (response *models.CreateQuoteResponse, err error) {
	return nil, helpers.NotImplemented(models.FreightFlow, "CreateQuote")
}
