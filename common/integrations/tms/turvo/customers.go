package turvo

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"go.uber.org/zap"
	"gorm.io/gorm"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
	integrationDB "github.com/drumkitai/drumkit/common/rds/integration"
	tmsCustomerDB "github.com/drumkitai/drumkit/common/rds/tms_customers"
	"github.com/drumkitai/drumkit/common/redis"
)

const (
	customerListPath = "/v1/customers/list"
)

func (t *Turvo) GetCustomers(ctx context.Context) ([]models.TMSCustomer, error) {
	ctx, metaSpan := otel.StartSpan(ctx, "GetCustomersTurvo", otel.IntegrationAttrs(t.tms))
	var err error
	defer func() { metaSpan.End(err) }()

	queryParams := make(url.Values)

	// First, check if we have a saved job state in redis
	updatedAt, cursor, err := redis.GetIntegrationState(ctx, t.tms.ID, redis.CustomerJob)
	if err != nil {
		log.WarnNoSentry(ctx, "failed to get integration state", zap.Error(err))
	} else {
		if updatedAt != "" {
			queryParams.Set("updated[gte]", updatedAt)
		}
		if cursor != "" {
			queryParams.Set("start", cursor)
		}
	}

	// if we haven't set the updated[gte] query param by now, check the DB for the last update time
	if queryParams.Get("updated[gte]") == "" {
		latestUpdatedAt, err := integrationDB.GetColumn(ctx, t.tms.ID, integrationDB.LastCustomerUpdatedAt)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				log.Info(
					ctx,
					"record not found, fetching all customers for integration",
					zap.Uint("integration_id", t.tms.ID),
				)
			} else {
				log.WarnNoSentry(
					ctx,
					"failed to get integration state, fetching all customers for integration",
					zap.Error(err),
				)
			}
		} else {
			queryParams.Set("updated[gte]", latestUpdatedAt.Format("2006-01-02T15:04:05Z"))
		}
	}

	var customers []models.TMSCustomer

	for {
		var customerResp CustomerListResponse
		// returns a list of partial customers
		if err = t.getWithAuth(
			ctx,
			customerListPath,
			queryParams,
			&customerResp,
			s3backup.TypeCustomers,
		); err != nil {
			if err := redis.SetIntegrationState(
				ctx,
				t.tms.ID,
				redis.CustomerJob,
				queryParams.Get("updated[gte]"),
				queryParams.Get("start"),
			); err != nil {
				log.Error(ctx, "failed to set integration state", zap.Error(err))
			}
			break
		}

		if strings.EqualFold(customerResp.Status, "error") {
			err = fmt.Errorf("turvo API error: %s", customerResp.Details.ErrorMessage)
			if err := redis.SetIntegrationState(
				ctx,
				t.tms.ID,
				redis.CustomerJob,
				queryParams.Get("updated[gte]"),
				queryParams.Get("start"),
			); err != nil {
				log.Error(ctx, "failed to set integration state", zap.Error(err))
			}
			break
		}

		customersToRefresh := &[]models.TMSCustomer{}
		*customersToRefresh = make([]models.TMSCustomer, 0, len(customerResp.Details.Customers))

		for _, customer := range customerResp.Details.Customers {
			*customersToRefresh = append(*customersToRefresh, ToCustomerModel(t.tms.ID, customer))
		}

		if err = tmsCustomerDB.RefreshTMSCustomers(ctx, customersToRefresh); err != nil {
			if err := redis.SetIntegrationState(
				ctx,
				t.tms.ID,
				redis.CustomerJob,
				queryParams.Get("updated[gte]"),
				queryParams.Get("start"),
			); err != nil {
				log.Error(ctx, "failed to set integration state", zap.Error(err))
			}
			break
		}

		// after successfully upserting, add the customers to the function's return slice
		customers = append(customers, *customersToRefresh...)

		if !customerResp.Details.Pagination.MoreAvailable {
			// delete the redis key once we've successfully reached the end of the job
			redis.RDB.Del(ctx, fmt.Sprintf("integration-id-%d-%s", t.tms.ID, redis.CustomerJob))
			break
		}

		queryParams.Set("start", strconv.Itoa(customerResp.Details.Pagination.Start))
	}

	return customers, err
}

func ToCustomerModel(tmsID uint, c ListedCustomerDetails) models.TMSCustomer {

	var address Address
	if len(c.Address) > 0 {
		address = c.Address[0]
	} else if len(c.Addresses) > 0 {
		address = c.Addresses[0]
	}

	cus := models.TMSCustomer{
		TMSIntegrationID: tmsID,
		ExternalID:       strconv.Itoa(c.ID),
		CompanyCoreInfo: models.CompanyCoreInfo{
			ExternalTMSID: strconv.Itoa(c.ID),
			Name:          c.Name,
			AddressLine1:  address.Line1,
			AddressLine2:  address.Line2,
			City:          address.City,
			State:         address.State,
			Zipcode:       address.Zip,
		},
	}

	return cus
}
