package turvo

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"github.com/drumkitai/drumkit/common/helpers"
	httputil "github.com/drumkitai/drumkit/common/helpers/http"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	// NOTE: Use in local development, especially for write operations. TODO: ENG-3002
	stagingTMSHost    = "my-sandbox-publicapi.turvo.com"
	stagingAppTMSHost = "my-sandbox.turvo.com"

	prodTMSHost = "publicapi.turvo.com"
	appTMSHost  = "app.turvo.com"
	tokenHeader = "x-api-key"
)

type Turvo struct {
	tms     models.Integration
	tmsHost string
}

func New(_ context.Context, tms models.Integration, opts ...models.TMSOption) *Turvo {
	options := &models.TMSOptions{}
	options.Apply(opts...)

	// Set the default host
	host := prodTMSHost
	if options.ChangeHostName {
		host = appTMSHost
	}

	if tms.AppID == "staging" {
		host = stagingTMSHost
		if options.ChangeHostName {
			host = stagingAppTMSHost
		}
	}

	return &Turvo{
		tms:     tms,
		tmsHost: host,
	}
}

func (t *Turvo) PostException(context.Context, *models.Load, models.Exception) error {
	return helpers.NotImplemented(models.Turvo, "PostException")
}

func (t *Turvo) GetExceptionHistory(context.Context, uint, string) ([]models.Exception, error) {
	return []models.Exception{}, helpers.NotImplemented(models.Turvo, "GetExceptionHistory")
}

func (t *Turvo) PostNote(context.Context, *models.Load, models.Note) ([]models.Note, error) {
	return nil, helpers.NotImplemented(models.Turvo, "PostNote")
}

func (t *Turvo) GetTestLoads() map[string]bool {
	return map[string]bool{
		"31408-01721": true, // aka Load.FreightTrackingID
		"77847003":    true, // aka Load.ExternalTMSID, refers to same load as 31408-01721
		"31476-25772": true, // NOTE: Older than 120 days so shipments/list returns 404
		"63996719":    true, // NOTE: Refers to same load as 31476-25772. GET by ID still succeeds
		"31438-14877": true, // aka Load.FreightTrackingID - NCMC Test Load
	}
}

func (t *Turvo) GetUsers(context.Context) (users []models.TMSUser, _ error) {
	return users, helpers.NotImplemented(models.Turvo, "GetUsers")
}

func (t *Turvo) postNoAuth(ctx context.Context, path string, queryParams url.Values,
	reqBody any, dst any, dataType s3backup.DataType) error {
	return t.post(ctx, path, queryParams, reqBody, dst, nil, dataType)
}

func (t *Turvo) getWithAuth(ctx context.Context, path string, queryParams url.Values,
	dst any, dataType s3backup.DataType) error {
	authorization, err := t.getAuthToken(ctx)
	if err != nil {
		return err
	}

	return t.get(ctx, path, queryParams, dst, &authorization, dataType)
}

func (t *Turvo) postWithAuth(ctx context.Context, path string, queryParams url.Values,
	reqBody any, dst any, dataType s3backup.DataType) error {
	authorization, err := t.getAuthToken(ctx)
	if err != nil {
		return err
	}

	return t.post(ctx, path, queryParams, reqBody, dst, &authorization, dataType)
}

func (t *Turvo) putWithAuth(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody any, dst any,
	dataType s3backup.DataType,
) error {

	authorization, err := t.getAuthToken(ctx)
	if err != nil {
		return err
	}

	return t.put(ctx, path, queryParams, reqBody, dst, &authorization, dataType)
}

func (t *Turvo) get(
	ctx context.Context,
	path string,
	queryParams url.Values,
	dst any,
	authorization *string,
	dataType s3backup.DataType) error {

	var addr url.URL
	if strings.HasPrefix(path, "https://") {
		// If path is a full URL, parse it directly
		parsedURL, err := url.Parse(path)
		if err != nil {
			return fmt.Errorf("failed to parse URL: %w", err)
		}
		addr = *parsedURL
		// Merge existing query params with the ones from the parsed URL
		q := addr.Query()
		for k, v := range queryParams {
			for _, val := range v {
				q.Add(k, val)
			}
		}
		addr.RawQuery = q.Encode()
	} else {
		// Use the standard tmsHost with the provided path
		addr = url.URL{Scheme: "https", Host: t.tmsHost}

		// If using app.turvo.com/api, the path should not include /v1
		if strings.Contains(t.tmsHost, "app.turvo.com") {
			// Remove /v1 prefix if present
			path = strings.TrimPrefix(path, "/v1")
		}
		addr.Path = path
		addr.RawQuery = queryParams.Encode()
	}

	headerMap := make(map[string]string)

	var prevAttemptErr error
	var maxRetries = 2 // Retry once if token needs refreshing

	for attempt := 1; attempt <= maxRetries; attempt++ {

		// Only retry if the last error was an invalid token error
		if attempt > 1 && !isInvalidTokenError(prevAttemptErr) {
			break
		}

		headerMap[tokenHeader] = t.tms.APIKey

		body, _, err := httputil.GetBytesWithToken(ctx, t.tms, addr, headerMap, authorization, dataType)
		if err == nil {
			return json.Unmarshal(body, dst)
		}

		if isInvalidTokenError(err) {
			log.Info(ctx, "Found Invalid access token error, refreshing token")
			if refreshErr := t.RefreshToken(ctx); refreshErr != nil {
				return fmt.Errorf("error refreshing token: %w", refreshErr)
			}

			prevAttemptErr = err
			continue
		}
	}

	return nil
}

func (t *Turvo) post(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody, dst any,
	authorization *string,
	dataType s3backup.DataType) (err error) {

	// If using app.turvo.com/api, the path should not include /v1
	if strings.Contains(t.tmsHost, "app.turvo.com") {
		path = strings.TrimPrefix(path, "/v1")
	}

	addr := url.URL{Scheme: "https", Host: t.tmsHost, Path: path, RawQuery: queryParams.Encode()}

	headerMap := make(map[string]string)
	headerMap[tokenHeader] = t.tms.APIKey

	body, _, err := httputil.PostBytesWithToken(ctx, t.tms, addr, reqBody, headerMap, authorization, dataType)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return
}

func (t *Turvo) put(
	ctx context.Context,
	path string,
	queryParams url.Values,
	reqBody any, dst any,
	authorization *string,
	dataType s3backup.DataType,
) (err error) {
	// If using app.turvo.com/api, the path should not include /v1
	if strings.Contains(t.tmsHost, "app.turvo.com") {
		path = strings.TrimPrefix(path, "/v1")
	}

	addr := url.URL{Scheme: "https", Host: t.tmsHost, Path: path, RawQuery: queryParams.Encode()}

	headerMap := make(map[string]string)
	headerMap[tokenHeader] = t.tms.APIKey

	body, _, err := httputil.PutBytesWithToken(ctx, t.tms, addr, reqBody, headerMap, authorization, dataType)
	if err != nil {
		return err
	}

	if err := json.Unmarshal(body, dst); err != nil {
		return fmt.Errorf("json unmarshal failed: %w", err)
	}

	return
}

func (t *Turvo) getAuthToken(ctx context.Context) (string, error) {
	if time.Now().After(t.tms.AccessTokenExpirationDate.Time) {
		if err := t.RefreshToken(ctx); err != nil {
			return "", fmt.Errorf("error refreshing token: %w", err)
		}
	}

	authorization := fmt.Sprintf("Bearer %s", t.tms.AccessToken)
	return authorization, nil
}

func isInvalidTokenError(err error) bool {
	return err != nil && strings.Contains(err.Error(), "Invalid access token")
}
