package turvo

import (
	"context"
	"fmt"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/models"
)

func lookupTimezone(ctx context.Context, pickup models.Pickup, consignee models.Consignee) (string, string, error) {
	pickupZip, err := helpers.GetZipOrLookup(ctx, pickup.Zipcode, pickup.City, pickup.State)
	if err != nil {
		return "", "", fmt.Errorf("failed to resolve pickup zip code: %w", err)
	}

	deliveryZip, err := helpers.GetZipOrLookup(ctx, consignee.Zipcode, consignee.City, consignee.State)
	if err != nil {
		return "", "", fmt.Errorf("failed to resolve delivery zip code: %w", err)
	}

	pickupTZ, err := helpers.AwsGetTimezoneFromZip(ctx, pickupZip, "pickup")
	if err != nil {
		return "", "", fmt.Errorf("failed to lookup pickup timezone: %w", err)
	}

	deliveryTZ, err := helpers.AwsGetTimezoneFromZip(ctx, deliveryZip, "delivery")
	if err != nil {
		return "", "", fmt.Errorf("failed to lookup delivery timezone: %w", err)
	}

	return pickupTZ, deliveryTZ, nil
}

// mapToValue converts a human-readable value to a Turvo-specific KeyValuePair using a reverse mapping.
// This function is used to translate standard values (like "USD", "kg", "Flatbed") into Turvo's
// internal representation, which consists of numeric codes paired with their human-readable values.
//
// For example, if we have a reverse mapping of {"USD": "1", "EUR": "2"}, then:
//   - mapToValue("USD", reverseMap) returns KeyValuePair{Key: "1", Value: "USD"}
//   - mapToValue("GBP", reverseMap) returns an empty KeyValuePair{}
//
// The reverse mappings are defined in enums.go and are used throughout the Turvo integration
// to convert our standard values into Turvo's expected format for API requests.
func mapToValue(value string, rm map[string]string) KeyValuePair {
	if rm[value] != "" {
		return KeyValuePair{
			Key:   rm[value],
			Value: value,
		}
	}

	return KeyValuePair{}
}

// BuildTurvoSpecifications takes LLM load building output and builds the specifications struct specifically
// for Turvo Loads.
func BuildTurvoSpecifications(rawSpecs models.SuggestedSpecifications) (specs models.SuggestedSpecifications) {

	specs = rawSpecs

	if helpers.IsStringInArray([]string{"Any", "Standard", "Expedite"}, rawSpecs.ServiceType) {
		specs.ServiceType = rawSpecs.ServiceType
	} else {
		specs.ServiceType = "Any"
	}

	if helpers.IsStringInArray([]string{"VAN", "REEFER", "FLATBED"}, rawSpecs.TransportType) {
		specs.TransportType = rawSpecs.TransportType
	} else {
		specs.TransportType = "VAN"
	}

	// Match units first
	specs.TotalWeight.Unit = helpers.FindMatchingKey(rawSpecs.TotalWeight.Unit, WeightUnitReverseMap)
	specs.NetWeight.Unit = helpers.FindMatchingKey(rawSpecs.NetWeight.Unit, WeightUnitReverseMap)

	// If either weight is zero, copy both value and unit from the non-zero weight
	if rawSpecs.NetWeight.Val == 0 && rawSpecs.TotalWeight.Val != 0 {
		specs.NetWeight = specs.TotalWeight
	} else if rawSpecs.TotalWeight.Val == 0 && rawSpecs.NetWeight.Val != 0 {
		specs.TotalWeight = specs.NetWeight
	}

	specs.TransportSize = helpers.FindMatchingKey(rawSpecs.TransportSize, EquipmentSizeReverseMap)

	specs.TotalPieces.Unit = helpers.FindMatchingKey(rawSpecs.TotalPieces.Unit, ItemUnitReverseMap)

	return specs
}
