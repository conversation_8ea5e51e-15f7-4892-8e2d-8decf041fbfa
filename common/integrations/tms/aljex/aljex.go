package aljex

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"net/http/cookiejar"
	"time"

	"golang.org/x/net/publicsuffix"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/crypto"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type Aljex struct {
	tms        models.Integration
	httpClient httpClient
	cookies    []*http.Cookie
	config     *Config
	creds      *Credentials
}

// Define interface for testing purposes
type httpClient interface {
	Do(req *http.Request) (*http.Response, error)
}

type Credentials struct {
	Prcnam    string
	Qual      string
	Name      string
	Company   string
	Qualifier string
	Token     string
}

type Config struct {
	AppID         string
	AppSymbol     string
	Options       string
	Password      string
	ReturnURL     string
	Tenant        string
	TestIndicator string
	UserName      string
}

func New(ctx context.Context, tms models.Integration) (*Aljex, error) {
	// Try retrieve an existing client
	cachedClient, err := retrieveRedisClient(ctx, tms.ServiceID, tms.ID)
	if err != nil {
		return nil, err
	}

	if cachedClient != nil {
		// We've successfully found a cached client for Aljex

		// For security reasons, do not rely on the serialized TMS
		cachedClient.tms = tms

		return cachedClient, nil
	}

	// Initialize a new Aljex client
	aljex, err := initialize(ctx, tms)
	if err != nil {
		return nil, err
	}

	// Authenticate the newly created client
	err = aljex.Auth(ctx)
	if err != nil {
		return nil, fmt.Errorf("authentication failed: %w", err)
	}

	aljex.dumpRedisClient(ctx)

	return aljex, nil
}

func initialize(ctx context.Context, tms models.Integration) (*Aljex, error) {
	password, err := crypto.DecryptAESGCM(ctx, string(tms.EncryptedPassword), nil)
	if err != nil {
		return nil, fmt.Errorf("error decrypting password: %w", err)
	}

	if tms.AppID == "" || password == "" {
		return nil, errors.New("missing Aljex password and/or app ID")
	}

	config := &Config{
		AppID:         tms.AppID,
		AppSymbol:     "",
		Password:      password,
		Options:       "hideDefaultDomain=true",
		ReturnURL:     fmt.Sprintf("https://%s.aljex.com/sso/visionlogin.php", tms.Tenant),
		Tenant:        tms.Tenant,
		TestIndicator: "",
		UserName:      tms.Username,
	}

	cookieJar, err := cookiejar.New(&cookiejar.Options{PublicSuffixList: publicsuffix.List})
	if err != nil {
		return nil, fmt.Errorf("could not create Aljex cookie jar: %w", err)
	}

	// NOTE: There are multiple Prcnam inputs in the page, but only 1 works for auth & getting the PRO
	// Value seems consistent across sessions, unclear if this value is the same across accounts
	creds := &Credentials{Prcnam: "t3aretin"}

	httpClient := otel.TracingHTTPClient()
	httpClient.Timeout = 20 * time.Second
	httpClient.Jar = cookieJar
	aljex := Aljex{
		tms:        tms,
		httpClient: httpClient,
		config:     config,
		creds:      creds,
	}

	return &aljex, nil
}

// Only these loads may be used for testing updates
// IMPORTANT: DO NOT MODIFY REAL CUSTOMER DATA.
var axleTestPROs = map[string]bool{
	"2080005": true,
	"2080914": true,
	"2080915": true,
	"2080916": true, // Avoid developing against to minimize conflicts with ./load_test.go
}

func (a *Aljex) GetTestLoads() map[string]bool {
	return axleTestPROs
}

func (a *Aljex) GetCustomers(_ context.Context) (customers []models.TMSCustomer, _ error) {
	return customers, helpers.NotImplemented(models.Aljex, "GetCustomers")
}

func (a *Aljex) GetLocations(context.Context, ...models.TMSOption) (res []models.TMSLocation, _ error) {
	return res, helpers.NotImplemented(models.Aljex, "GetLocations")
}

func (a *Aljex) CreateQuote(
	_ context.Context,
	_ models.CreateQuoteBody,
) (quoteResponse *models.CreateQuoteResponse, err error) {
	return nil, helpers.NotImplemented(models.Aljex, "CreateQuote")
}

func (a *Aljex) GetCarriers(context.Context) ([]models.TMSCarrier, error) {
	return nil, helpers.NotImplemented(models.Aljex, "GetCarriers")
}

func (a *Aljex) InitialOnboard(
	ctx context.Context,
	_ models.Service,
	onboardRequest models.OnboardTMSRequest,
) (_ models.OnboardTMSResponse, err error) {
	ctx, metaSpan := otel.StartSpan(ctx, "InitialOnBoardAljex", otel.IntegrationAttrs(a.tms))
	defer func() { metaSpan.End(err) }()

	if onboardRequest.AppID == "" || onboardRequest.Password == "" {
		return models.OnboardTMSResponse{}, errors.New("missing Aljex API credentials")
	}

	encryptedPassword, err := crypto.EncryptAESGCM(ctx, onboardRequest.Password, nil)
	if err != nil {
		return models.OnboardTMSResponse{}, fmt.Errorf("password encryption failed: %w", err)
	}

	return models.OnboardTMSResponse{
		AppID:             onboardRequest.AppID,
		EncryptedPassword: encryptedPassword,
		Username:          onboardRequest.Username,
		Tenant:            onboardRequest.Tenant,
		TwoFactorSecret:   onboardRequest.TwoFactorSecret,
	}, nil
}
