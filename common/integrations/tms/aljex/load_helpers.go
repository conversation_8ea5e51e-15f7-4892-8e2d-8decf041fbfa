package aljex

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/url"
	"regexp"
	"strconv"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/helpers/s3backup"
	"github.com/drumkitai/drumkit/common/log"
)

// Internal methods
func (a *Aljex) getLoad(ctx context.Context, freightTrackingID string) (*goquery.Document, error) {
	body, err := a.getLoadHTML(ctx, freightTrackingID)
	if err != nil {
		return nil, err
	}

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("could not parse HTML from Aljex load info - GET response: %w", err)
	}

	return doc, nil
}

// helper functions

// Loads PRO's webpage and returns the HTML doc
func (a *Aljex) getLoadHTML(ctx context.Context, freightTrackingID string) ([]byte, error) {
	formData := url.Values{}
	formData.Add("prcnam", a.creds.Prcnam)
	formData.Add("qual", a.creds.Qual)
	formData.Add("name", a.creds.Name)
	formData.Add("company", a.creds.Company) // NOTE: this is actually empty, but doesn't impact result
	formData.Add("c_tok", a.creds.Token)
	formData.Add("pword", "")
	formData.Add("sys", "3a")
	formData.Add("ctlrec", "")
	formData.Add("ctlval", "")
	formData.Add("type", "")
	formData.Add("recno", "")
	formData.Add("pro", freightTrackingID)
	formData.Add("jobnam", "")
	formData.Add("webprint", "")
	formData.Add("addcar", "")
	formData.Add("trailers", "")
	formData.Add("what", "")
	formData.Add("select", "")
	formData.Add("qualifier", a.config.Tenant)
	formData.Add("prorecno", "")
	formData.Add("notenum", "")
	formData.Add("group", "")
	formData.Add("custid", "")
	formData.Add("carid", "")
	formData.Add("fpweb_fn", "")
	formData.Add("fpweb_ix", "")
	formData.Add("fpweb_ky", "")
	formData.Add("fpweb_rn", "")
	formData.Add("fpweb_pg", "")
	formData.Add("fpweb_xt", "")
	formData.Add("isfbox", "")
	formData.Add("justthis", "")

	resp, _, err := a.postForm(ctx, a.getAPIURL(), strings.NewReader(formData.Encode()), true, s3backup.TypeLoads)
	if err != nil {
		return nil, fmt.Errorf("failed to get Aljex load: %w", err)
	}

	return resp, nil
}

func (a *Aljex) parseLoadHTML(ctx context.Context, doc *goquery.Document, freightID string) *LoadData {
	res := &LoadData{
		Status:         parseStatus(doc),
		OutPalletCount: parseOutPalletCount(ctx, doc),
		Operator:       toStringField(doc, "fld62"),
		Carrier: Carrier{
			Carrier:          toStringField(doc, "fldcarr"),
			Dot:              toStringField(doc, "dotno"),
			Mc:               toStringField(doc, "mcno"),
			Scac:             toStringField(doc, "scac"),
			Seal:             toStringField(doc, "di_fld158"),
			Phone:            toStringField(doc, "fld172"),
			Email:            toStringField(doc, "fld196"),
			FirstDriver:      toStringField(doc, "fld74"),
			FirstDriverCell:  toStringField(doc, "di_fld177"),
			SecondDriver:     toStringField(doc, "di_fld188"),
			SecondDriverCell: toStringField(doc, "di_fld189"),
			DispCity:         toStringField(doc, "fld91"),
			DispState:        toStringField(doc, "fld92"),
			Dispatcher:       toStringField(doc, "fld176"),
			Trailer:          toStringField(doc, "di_fld89"),
			Truck:            toStringField(doc, "di_fld191"),
			ConfSentDate:     toStringField(doc, "fld549"),
			ConfSentTime:     toStringField(doc, "fld559"),
			ConfRecdDate:     toStringField(doc, "fld561"),
			ConfRecdTime:     toStringField(doc, "fld562"),
			DispatchedDate:   toStringField(doc, "di_hdate"),
			DispatchedTime:   toStringField(doc, "di_fld53"),
			WillPuDate:       toStringField(doc, "di_wdate"),
			WillPuTime:       toStringField(doc, "di_fld54"),
			ArrivedPuDate:    toStringField(doc, "di_avdate"),
			ArrivedPuTime:    toStringField(doc, "di_avtime"),
			LoadedDate:       toStringField(doc, "di_ldate"),
			LoadedTime:       toStringField(doc, "di_ltime"),
			DelEtaDate:       toStringField(doc, "di_edate"),
			DelEtaTime:       toStringField(doc, "di_etime"),
			ArrivedConsDate:  toStringField(doc, "di_addate"),
			ArrivedConsTime:  toStringField(doc, "di_addtime"),
			DeliveredDate:    toStringField(doc, "di_fld145"),
			DeliveredTime:    toStringField(doc, "di_dtime"),
			SignedBy:         toStringField(doc, "di_fld147"),
		},
		Customer: Customer{
			Name:    toStringField(doc, "s_fld2"),
			Address: toStringField(doc, "fld3"),
			City:    toStringField(doc, "s_fld7"),
			State:   toStringField(doc, "fld5"),
			Zip:     toStringField(doc, "fld6"),
			Country: toStringField(doc, "fld333"),
			Contact: toStringField(doc, "fld9"),
			Email:   toStringField(doc, "fldp4"),
			Email2:  toStringField(doc, "fldp5"),
			Email3:  toStringField(doc, "fldp6"),
			Email4:  toStringField(doc, "fldp7"),
			Email5:  toStringField(doc, "fldp8"),
			Email6:  toStringField(doc, "fldp9"),
			Phone:   toStringField(doc, "fld8"),
			Ref:     toStringField(doc, "fld59"),
		},
		BillTo: BillTo{
			Name:    toStringField(doc, "s_fld169"),
			Address: toStringField(doc, "btaddr"),
			Zip:     toStringField(doc, "btzip"),
			Country: toStringField(doc, "btcountry"),
			Contact: toStringField(doc, "btcontact"),
			// Parse BillTo fields that are read-only, autofilled and don't have a form name
			// The lack of form names aren't a concern until we add load building feature
			City:  toStringFieldByID(doc, "btcity"),
			State: toStringFieldByID(doc, "btstate"),
			Phone: toStringFieldByID(doc, "btphone"),
		},
		PickUp: PickUp{
			Name:          toStringField(doc, "s_fld30"),
			Address:       toStringField(doc, "fld31"),
			Address2:      toStringField(doc, "fld181"),
			City:          toStringField(doc, "fld32"),
			State:         toStringField(doc, "fld33"),
			Zip:           toStringField(doc, "fld34"),
			Country:       toStringField(doc, "fld334"),
			Contact:       toStringField(doc, "fld35"),
			Email:         toStringField(doc, "fld197"),
			Phone:         toStringField(doc, "fld36"),
			ReadyDate:     toStringField(doc, "fld39"),
			ReadyTime:     toStringField(doc, "fld14"),
			ApptDate:      toStringField(doc, "fld185"),
			ApptNote:      toStringField(doc, "fld187"),
			ApptStartTime: toStringField(doc, "fld186"),
			BusinessHours: toStringField(doc, "fld15"),
			Ref:           toStringField(doc, "fld155"),
		},
		Consignee: Consignee{
			Name:          toStringField(doc, "fld29"),
			Address:       toStringField(doc, "fld60"),
			Address2:      toStringField(doc, "fld182"),
			State:         toStringField(doc, "fld41"),
			Zip:           toStringField(doc, "fld70"),
			Country:       toStringField(doc, "fld335"),
			Appt:          toStringField(doc, "fld150"),
			ApptNote:      toStringField(doc, "fld152"),
			ApptStartTime: toStringField(doc, "fld151"),
			BusinessHours: toStringField(doc, "fld42"),
			City:          toStringField(doc, "fld10"),
			Contact:       toStringField(doc, "fld43"),
			Email:         toStringField(doc, "fld198"),
			MustDeliver:   toStringField(doc, "fld602"),
			Phone:         toStringField(doc, "fld58"),
			Ref:           toStringField(doc, "fld156"),
		},

		RateData: RateData{
			CustomerRateType:  parseCustomerRateType(doc),
			CarrierRateType:   parseCarrierRateType(doc),
			CarrierNumHours:   toFloatField(ctx, doc, "fld527"),
			CustomerNumHours:  toFloatField(ctx, doc, "fld526"),
			CarrierLHRateUSD:  toFloatField(ctx, doc, "payrate"),
			CustomerLHRateUSD: toFloatFieldByID(ctx, doc, "fld24", "fld24"),
			Fsc:               toFloatField(ctx, doc, "fld664"),
			FscMile:           toFloatField(ctx, doc, "fld665"),
			MaxRate:           toFloatField(ctx, doc, "fld604"),
			NetProfitUSD:      toFloatField(ctx, doc, "fldp0"),
			NetProfitPercent:  toFloatField(ctx, doc, "fldp1"),
		},

		Specifications: Specifications{
			MinTempFahrenheit: toFloatField(ctx, doc, "fld95"),
			MaxTempFahrenheit: toFloatField(ctx, doc, "fldextra2"),
		},
	}

	// Parse PO nums for appointment scheduling
	// NOTE: that we'd have to change this if we need to support updating these POs as well
	var poNums []string
	for _, ref := range parseRefs(doc) {
		// Assume 7-digit REF numbers starting with a 3 are PO numbers
		if len(ref.Value) == 7 && strings.HasPrefix(ref.Value, "3") {
			poNums = append(poNums, ref.Value)
		}
	}
	res.PONums = StringValue{Value: strings.Join(poNums, ","), IsReadOnly: true}

	// parse notes
	res.Notes = parseNotes(doc)

	// Parse Operator name, which has its nuances
	var err error
	res.Operator, err = a.parseLoadOperator(ctx, doc, freightID)
	if err != nil {
		// Fail-open. If there are issues loading Smart Search page, fallback to field's `readonly` attribute
		log.WarnNoSentry(ctx, "error parsing Operator read-only attr", zap.Error(err))
	}

	res.Consignee.Name.OtherFormNames = []string{"fld29x", "s_fld29"}
	res.PickUp.Name.OtherFormNames = []string{"fld30x", "s_fld30", "fld30"}
	res.Customer.Ref.OtherFormNames = []string{"s_fld59"}

	res.Carrier.DispatchedDate.OtherFormNames = []string{"hdate", "tr_hdate", "di_hdate"}
	res.Carrier.DispatchedTime.OtherFormNames = []string{"fld53", "tr_fld53", "di_fld53"}
	res.Carrier.WillPuDate.OtherFormNames = []string{"di_wdate", "wdate"}
	res.Carrier.WillPuTime.OtherFormNames = []string{"di_fld54", "fld54"}
	res.Carrier.ArrivedPuDate.OtherFormNames = []string{"avdate", "di_avdate"}
	res.Carrier.ArrivedPuTime.OtherFormNames = []string{"avtime", "di_avtime"}
	res.Carrier.LoadedDate.OtherFormNames = []string{"ldate", "di_ldate"}
	res.Carrier.LoadedTime.OtherFormNames = []string{"ltime", "di_ltime"}
	res.Carrier.DelEtaDate.OtherFormNames = []string{"edate", "di_edate", "tr_edate"}
	res.Carrier.DelEtaTime.OtherFormNames = []string{"etime", "di_etime", "tr_etime"}
	res.Carrier.ArrivedConsDate.OtherFormNames = []string{"di_addate", "addate", "tr_addate"}
	res.Carrier.ArrivedConsTime.OtherFormNames = []string{"di_addtime", "addtime", "tr_addtime"}
	res.Carrier.DeliveredDate.OtherFormNames = []string{"fld145", "di_fld145", "im_fld145", "tr_fld145", "af_fld145"}
	res.Carrier.DeliveredTime.OtherFormNames = []string{"dtime", "di_dtime", "im_dtime", "af_dtime", "tr_dtime"}
	res.Carrier.Truck.OtherFormNames = []string{"di_fld191", "fld191x", "fld191"}
	res.Carrier.Trailer.OtherFormNames = []string{"fld89x", "fld89", "di_fld89"}
	res.Carrier.FirstDriver.OtherFormNames = []string{"fld74"}
	res.Carrier.FirstDriverCell.OtherFormNames = []string{"tr_fld177", "di_fld177", "fld177"}
	res.Carrier.SecondDriver.OtherFormNames = []string{"fld188", "di_fld188"}
	res.Carrier.SecondDriverCell.OtherFormNames = []string{"fld189", "di_fld189"}
	res.Carrier.Seal.OtherFormNames = []string{"di_fld158", "fld158"}
	res.Carrier.SignedBy.OtherFormNames = []string{"fld147", "di_fld147"}

	return res

}

func toStringField(doc *goquery.Document, formName string) StringValue {
	// Since these are all form inputs, name is typically as unique as ID
	// so we can use it for both GET and POST
	input := doc.Find(fmt.Sprintf("input[name='%s']", formName))
	_, isReadOnly := input.Attr("readonly")
	_, isDisabled := input.Attr("disabled")
	maxLengthStr := input.AttrOr("maxlength", "0")
	//nolint:errcheck // Non-critical int conversion
	maxLength, _ := strconv.Atoi(maxLengthStr)

	return StringValue{
		Value:      strings.TrimSpace(input.AttrOr("value", "")),
		FormName:   formName,
		IsReadOnly: isReadOnly || isDisabled,
		MaxLength:  maxLength,
	}
}

// Used for a select few inputs that are read-only, autofilled and don't have a form name.
// So while we don't update them, we do want to get that data to show to the user
func toStringFieldByID(doc *goquery.Document, id string) StringValue {
	input := doc.Find(fmt.Sprintf("#%s", id))
	_, isReadOnly := input.Attr("readonly")
	_, isDisabled := input.Attr("disabled")
	maxLengthStr := input.AttrOr("maxlength", "0")
	//nolint:errcheck // Non-critical int conversion
	maxLength, _ := strconv.Atoi(maxLengthStr)

	return StringValue{
		Value:      strings.TrimSpace(input.AttrOr("value", "")),
		FormName:   input.AttrOr("name", ""),
		IsReadOnly: isReadOnly || isDisabled,
		MaxLength:  maxLength,
	}
}

// Similar to toStringFieldByID. Used when multiple inputs have the same form name, causing the wrong
// value to be parsed (e.g. )
func toFloatFieldByID(ctx context.Context, doc *goquery.Document, id, formName string) FloatValue {
	input := doc.Find(fmt.Sprintf("#%s", id))

	return floatFieldHelper(ctx, input, formName)
}

func toFloatField(ctx context.Context, doc *goquery.Document, formName string) FloatValue {
	input := doc.Find(fmt.Sprintf("input[name='%s']", formName))
	return floatFieldHelper(ctx, input, formName)
}

func floatFieldHelper(ctx context.Context, input *goquery.Selection, formName string) FloatValue {
	_, isReadOnly := input.Attr("readonly")
	_, isDisabled := input.Attr("disabled")

	str := strings.TrimSpace(input.AttrOr("value", "0"))
	// CarrierLHRateUSD aka "payrate" is weird
	if str == "" || str == "USD" || str == "CAD" {
		return FloatValue{
			FormName:   formName,
			IsReadOnly: isReadOnly || isDisabled,
		}
	}

	value, err := strconv.ParseFloat(str, 32)
	if err != nil {
		log.WarnNoSentry(ctx, "error converting input to float32",
			zap.String("formName", formName), zap.Error(err))
	}

	return FloatValue{
		Value:      float32(value),
		FormName:   formName,
		IsReadOnly: isReadOnly || isDisabled,
	}
}

func parseCarrierRateType(doc *goquery.Document) StringValue {
	selectInput := doc.Find("#paytype")
	_, isReadOnly := selectInput.Attr("readonly")
	_, isDisabled := selectInput.Attr("disabled")

	// Selected value is the first option in the dropdown
	rateType := strings.TrimSpace(selectInput.Find("option").First().Text())

	return StringValue{
		Value:          rateType,
		FormName:       "paytype",
		OtherFormNames: []string{"paytype", "fld102"},
		IsReadOnly:     isReadOnly || isDisabled,
	}
}

func parseCustomerRateType(doc *goquery.Document) StringValue {
	hiddenInput := doc.Find("#im_fld23-")

	var rateType string

	v := hiddenInput.AttrOr("value", "")

	switch v {
	case "I":
		rateType = "All In"
	case "F":
		rateType = "Flat Rate"
	case "A":
		rateType = "Auto Rate"
	case "C":
		rateType = "CWT"
	case "T":
		rateType = "Ton"
	case "P":
		rateType = "Pieces"
	case "M":
		rateType = "Mileage"
	case "G":
		rateType = "Gainshare"
	case "H":
		rateType = "Hourly"
	default:
		rateType = "Unknown"
	}

	selectInput := doc.Find("#fld23")
	_, isReadOnly := selectInput.Attr("readonly")
	_, isDisabled := selectInput.Attr("disabled")

	return StringValue{
		Value:      rateType,
		FormName:   "fld23", // NOTE: that we use #im_fld23- to get its value, but fld23 for the POST
		IsReadOnly: isDisabled || isReadOnly,
	}
}

// Special helper to parse operator value and read-only attribute. The field may be marked as read-only,
// but there may be a "minus" icon to remove it or the possibility to update via Smart Search.
// So far, observed cases of when it's truly read-only is when the status is Hold, Covered, or Delivered.
func (a *Aljex) parseLoadOperator(
	ctx context.Context,
	doc *goquery.Document,
	freightTrackingID string,
) (res StringValue, err error) {

	// Init return value. Caller fails-open if we can't get read-only attribute from Search page.
	input := doc.Find("#fld62")
	_, isReadOnly := input.Attr("readonly")
	maxLengthStr := input.AttrOr("maxlength", "0")
	maxLength, err := strconv.Atoi(maxLengthStr)
	if err != nil {
		log.WarnNoSentry(ctx, "error getting operator's max length", zap.Error(err))
	}

	res = StringValue{
		Value:      input.AttrOr("value", ""),
		FormName:   "fld62",
		IsReadOnly: isReadOnly,
		MaxLength:  maxLength,
	}

	// Condition 1: Is there the option to remove the assigned operator?
	canRemoveRep := !strings.Contains(strings.ReplaceAll(
		doc.Find("#rmvrep").AttrOr("style", ""), " ", ""), "display:none")

	if canRemoveRep {
		res.IsReadOnly = false

		return res, nil
	}

	// Condition 2: If there's no icon to remove existing operator, can we (re-)assign via the SmartSearch page?
	log.Info(ctx, "aljex.OperatorSmartSearch")

	formData := url.Values{}
	formData.Set("pro", freightTrackingID)
	formData.Set("prcnam", "ssweb")
	formData.Set("type", "ssweb")
	formData.Set("qual", a.creds.Qual)
	formData.Set("name", a.creds.Name)
	formData.Set("company", a.creds.Company) // NOTE: this is actually empty, but doesn't impact result
	formData.Set("c_tok", a.creds.Token)
	formData.Set("sys", "3a")

	resp, _, err := a.get(ctx, a.getAPIURL()+"/?"+formData.Encode(), s3backup.TypeLoads)
	if err != nil {
		return res, fmt.Errorf("failed to get Aljex operator SmartSearch: %w", err)
	}

	searchDoc, err := goquery.NewDocumentFromReader(bytes.NewReader(resp))
	if err != nil {
		err = fmt.Errorf("error creating searchDoc: %w", err)
		return res, err
	}

	canAssignRep := strings.EqualFold(searchDoc.Find("#doassrep").AttrOr("value", ""), "Y")

	// Final isReadOnly condition for operator field: no option to remove or change dispatcher
	res.IsReadOnly = !canRemoveRep && !canAssignRep

	return res, nil
}

// Gets the form name (i.e. "fld172") of an Aljex input and sets the updated value in the HTML form
func addDataToForm(
	ctx context.Context,
	formData *url.Values,
	updatedLoad map[string]FormAttributes,
	formMetadata map[string]FormAttributes,
) {
	for field, newValue := range updatedLoad {
		if !formMetadata[field].IsReadOnly {
			// Aljex sometimes has multiple form names for the same field and either 1 or all versions need to be set
			// for the request to succeed
			//nolint:gocritic // intentional
			allFormNames := append(formMetadata[field].OtherFormNames, formMetadata[field].FormName)
			for _, name := range allFormNames {
				formData.Set(name, newValue.Value)
			}
		} else {
			log.Debug(ctx, "skipping read-only field",
				zap.String("field", field), zap.String("formName", formMetadata[field].FormName))
		}
	}
}

func parseStatus(doc *goquery.Document) StringValue {
	statusSpan := doc.Find("#headlbl")
	status := strings.TrimSpace(statusSpan.Find("b").Text())

	return StringValue{
		Value:      strings.ToLower(status),
		IsReadOnly: true,
	}
}

// Parse pallet count by ID and not by name because hidden input with the same `fld97` name
// gives us incorrect value
func parseOutPalletCount(ctx context.Context, doc *goquery.Document) FloatValue {
	str := strings.TrimSpace(doc.Find("#fld97").AttrOr("value", "0"))
	if str == "" {
		str = "0"
	}

	value, err := strconv.Atoi(str)
	if err != nil {
		log.WarnNoSentry(ctx, "error converting #fld97 to int",
			zap.String("value", str), zap.Error(err))
	}

	return FloatValue{
		Value:      float32(value),
		IsReadOnly: false,
		FormName:   "fld97",
	}
}

func parseRefs(doc *goquery.Document) []StringValue {
	var result []StringValue

	for _, id := range []string{"#fld292", "#fld293", "#fld294", "#fld295", "#fld296", "#fld297"} {
		val := strings.TrimSpace(doc.Find(id).AttrOr("value", ""))
		if val != "" {
			result = append(result, StringValue{Value: val, IsReadOnly: false})
		}
	}

	return result
}

func toStructMap(a *LoadData) (*rawFreightData, error) {
	if a == nil {
		return nil, errors.New("nil pointer")
	}

	data, err := json.Marshal(a)
	if err != nil {
		return nil, err
	}

	var res rawFreightData
	err = json.Unmarshal(data, &res)

	return &res, err
}

// GetLoadByIDType helpers
var re = regexp.MustCompile(`^getLoad\(`)

func parseSearchResults(ctx context.Context, body []byte) (proNums []string, err error) {
	_, metaSpan := otel.StartSpan(ctx, "parseSearchResults", nil)
	defer func() { metaSpan.End(err) }()

	doc, err := goquery.NewDocumentFromReader(bytes.NewReader(body))
	if err != nil {
		return nil, fmt.Errorf("could not parse HTML from search results: %w", err)
	}

	// Find all <a> tags with an onclick attribute that starts with "getLoad("
	doc.Find("a").FilterFunction(func(_ int, s *goquery.Selection) bool {
		onclick, exists := s.Attr("onclick")
		return exists && re.MatchString(onclick)
	}).Each(func(_ int, s *goquery.Selection) {
		pro := strings.TrimSpace(s.Text())
		if pro != "" {
			proNums = append(proNums, pro)
		}
	})

	return proNums, nil
}

func parseNotes(doc *goquery.Document) []Note {
	var notes []Note

	doc.Find("table#notetab tr").Each(func(i int, s *goquery.Selection) {
		if i == 0 { // Skip header row
			return
		}

		var note Note
		s.Find("td").Each(func(j int, td *goquery.Selection) {
			switch j {
			case 0:
				note.CreatedAt.Value = strings.TrimSpace(td.Text())
			case 1:
				note.UpdatedBy.Value = strings.TrimSpace(regexp.MustCompile(`\s\d{2}/\d{2}/\d{2}$`).
					ReplaceAllString((td.Text()), ""))
			case 2:
				note.Note.Value = strings.TrimSpace(td.Text())
			}
		})

		if note.CreatedAt.Value != "" && note.UpdatedBy.Value != "" && note.Note.Value != "" {
			notes = append(notes, note)
		}
	})

	return notes
}

// Extract PRO number from Aljex load table cell which looks like this:
// <td class="lo" style="text-align:center;">
//
//	<a href="/route.php?pro=2080914" target="_blank">2080914 Pcs: 15     Wgt: 15000 FASTENERS </a>
//
// </td>
func extractPRO(cellText string) string {
	re := regexp.MustCompile(`\b\d{7}\b`)

	match := re.FindString(cellText)

	return match
}
