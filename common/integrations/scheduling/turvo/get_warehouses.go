package turvo

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"strings"

	"github.com/drumkitai/drumkit/common/models"
)

func (t *Turvo) GetAllWarehouses(ctx context.Context) ([]models.Warehouse, error) {
	var result GetAllWarehousesResp
	var warehouses []models.Warehouse
	nextPage := true
	offset := 0

	for nextPage {
		query := url.Values{
			"isWarehouse[eq]": {"true"},
			"pageSize":        {strconv.Itoa(limit)},
			"start":           {strconv.Itoa(offset)},
		}

		path := "/v1/locations/list"
		if err := t.get(ctx, path, query, &result); err != nil {
			return nil, fmt.Errorf("error getting all warehouses: %w", err)
		}

		for _, wh := range result.Details.Locations {
			address, err := extractAddress(wh.Addresses)
			if err != nil {
				return nil, fmt.Errorf("error extracting address for warehouse %s: %w", wh.Name, err)
			}

			whRecord := models.Warehouse{
				WarehouseID:           strconv.Itoa(wh.ID),
				WarehouseName:         wh.Name,
				WarehouseAddressLine1: address.Line1,
				WarehouseAddressLine2: address.Line2,
				WarehouseFullAddress:  formatFullAddress(address),
				Source:                models.TurvoSource,
			}

			warehouses = append(warehouses, whRecord)
		}

		nextPage = result.Details.Pagination.MoreAvailable
		offset += limit
	}

	return warehouses, nil
}

func extractAddress(addresses []Address) (Address, error) {
	if len(addresses) == 0 {
		return Address{}, errors.New("no address available")
	}

	return addresses[0], nil
}

func formatFullAddress(address Address) string {
	parts := []string{
		address.Line1,
		address.Line2,
		address.City,
		address.State,
		address.Zip,
	}

	var nonEmptyParts []string
	for _, part := range parts {
		if part != "" {
			nonEmptyParts = append(nonEmptyParts, part)
		}
	}

	return strings.Join(nonEmptyParts, ", ")
}
