package turvo

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	openSlotsEndpoint       = "/v1/calendar/list"
	openSlotsResourceType   = "LOCATION"
	openSlotsResourceIDType = "turvoLocation"
)

func (t *Turvo) GetOpenSlots(ctx context.Context, _ string, req models.GetOpenSlotsRequest) ([]models.Slot, error) {
	if err := validateOpenSlotsRequest(req); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	apiReq := buildOpenSlotsRequest(req)

	var resp GetOpenSlotsResp
	if err := t.post(ctx, openSlotsEndpoint, url.Values{}, apiReq, &resp); err != nil {
		return nil, fmt.Errorf("failed to fetch open slots: %w", err)
	}

	return processOpenSlots(ctx, resp, req.WarehouseID)
}

func validateOpenSlotsRequest(req models.GetOpenSlotsRequest) error {
	if req.WarehouseID == "" {
		return errors.New("warehouse ID is required")
	}

	if req.Start.IsZero() {
		return errors.New("start time is required")
	}

	if req.End.IsZero() {
		return errors.New("end time is required")
	}

	if req.End.Before(req.Start) {
		return errors.New("end time cannot be before start time")
	}

	return nil
}

func buildOpenSlotsRequest(req models.GetOpenSlotsRequest) GetOpenSlotsReq {
	return GetOpenSlotsReq{
		StartDate: TimeSlot{
			Date: req.Start.Format(time.RFC3339),
		},
		EndDate: TimeSlot{
			Date: req.End.Format(time.RFC3339),
		},
		Resource: Resource{
			ResourceID: req.WarehouseID,
			Type:       openSlotsResourceType,
			IDType:     openSlotsResourceIDType,
		},
	}
}

func processOpenSlots(ctx context.Context, resp GetOpenSlotsResp, warehouseID string) ([]models.Slot, error) {
	var (
		slots []models.Slot
		errs  []error
	)

	for _, slotDef := range resp.SlotsDefinition {
		startTime, err := parseSlotTime(slotDef.StartDate)
		if err != nil {
			log.Error(
				ctx,
				"failed to parse slot time",
				zap.String("timeString", slotDef.StartDate),
				zap.Error(err),
			)

			errs = append(errs, err)
			continue
		}

		slot := models.Slot{
			Warehouse: models.Warehouse{
				WarehouseID: warehouseID,
			},
			StartTimes: []time.Time{startTime},
		}

		slots = append(slots, slot)
	}

	if len(slots) == 0 {
		log.Warn(ctx, "no valid slots found", zap.String("warehouseID", warehouseID))
		if len(errs) > 0 {
			return nil, fmt.Errorf("failed to process slots: %v", errs)
		}
	}

	return slots, nil
}

func parseSlotTime(timeStr string) (time.Time, error) {
	parsedTime, err := helpers.ParseDatetime(timeStr)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse time string '%s': %w", timeStr, err)
	}

	return parsedTime, nil
}
