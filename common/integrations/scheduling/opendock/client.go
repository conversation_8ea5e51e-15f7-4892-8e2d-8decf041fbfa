package opendock

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/httplog"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	neutronHost                   = "neutron.opendock.com"
	SevenElevenNeutronWarehouseID = "9b1beeac-c174-4282-860e-36eac63779c1" // Warehouse ID under Neutron API
)

// General usage, copied from Opendock API documentation:
// Due to the high variability between warehouses, the flow for scheduling an appointment involves a few "discovery"
// steps before the final call to create the appointment:
// 1. Choose a warehouse ID by either supplying it directly or looking it up via "Warehouse Query".
// 2. Query that warehouse for its "Services" offered, and select one of those services (by ID).
// 3. Query that warehouse for its "Booking Rules", which will return a list of rules dictating how appointments have to
// be booked/scheduled.
// 4. Query for "Open Slots" with the selected service ID.
// 5. Combine information from the returned data above to make the final "Appointments" call to schedule the
// appointment.

type (
	authenticateResponse struct {
		AccessToken string `json:"access_token"`
	}

	AccessTokenIntegrationData struct {
		AccessToken               string
		AccessTokenExpirationDate models.NullTime
	}
)

//nolint:unparam
func (o *Opendock) post(ctx context.Context, path string, query url.Values, body, out any) error {
	return o.do(ctx, http.MethodPost, path, query, body, out)
}

func (o *Opendock) get(ctx context.Context, path string, query url.Values, out any) error {
	return o.do(ctx, http.MethodGet, path, query, nil, out)
}

func (o *Opendock) patch(ctx context.Context, path string, body any, out any) error {
	return o.do(ctx, http.MethodPatch, path, nil, body, out)
}

func (o *Opendock) do(ctx context.Context, method, path string, query url.Values, body any, out any) (err error) {
	reqURL := (&url.URL{
		Scheme:   "https",
		Host:     neutronHost,
		Path:     path,
		RawQuery: query.Encode(),
	}).String()

	var bodyBytes []byte
	if body != nil {
		if bodyBytes, err = json.Marshal(body); err != nil {
			return fmt.Errorf("error marshaling body: %w", err)
		}
	}

	req, err := http.NewRequestWithContext(ctx, method, reqURL, bytes.NewReader(bodyBytes))
	if err != nil {
		return fmt.Errorf("failed to build %s %s request: %w", method, reqURL, err)
	}
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	// Bearer token required for all endpoints except POST /login (which creates the neutron token)
	if path != "login" {
		req.Header.Add("Authorization", "Bearer "+o.neutronAPIAccessToken)
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		httplog.LogHTTPRequestFailed(ctx, o.Source, err)
		return fmt.Errorf("failed to send %s %s request: %w", method, reqURL, err)
	}
	defer resp.Body.Close()
	httplog.LogHTTPResponseCode(ctx, o.Source, resp.StatusCode)

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read %s response body: %w", reqURL, err)
	}

	if code := resp.StatusCode; code >= http.StatusBadRequest {
		return errtypes.NewHTTPResponseError(o.Source, req, resp, respBody)
	}

	if out != nil {
		if err := json.Unmarshal(respBody, out); err != nil {
			log.Error(
				ctx,
				"json unmarshal failed for Opendock response body",
				zap.ByteString("body", respBody),
			)

			return fmt.Errorf("%s %s json unmarshal failed: %w", method, reqURL, err)
		}
	}

	return nil
}
