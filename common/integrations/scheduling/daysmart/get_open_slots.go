package daysmart

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	openSlotsEndpoint = "/Appointments/GetOpenSlots"
)

const (
	paramLocationID     = "location_id"
	paramLimitStartTime = "limit_start_time"
	paramLimitEndTime   = "limit_end_time"
)

func (d *DaySmart) GetOpenSlots(ctx context.Context, _ string, req models.GetOpenSlotsRequest) ([]models.Slot, error) {
	if err := validateOpenSlotsRequest(req); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	resp, err := d.fetchOpenSlots(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch open slots: %w", err)
	}

	slots, err := d.processOpenSlots(ctx, resp)
	if err != nil {
		return nil, fmt.Errorf("failed to process open slots: %w", err)
	}

	return slots, nil
}

func validateOpenSlotsRequest(req models.GetOpenSlotsRequest) error {
	if req.WarehouseID == "" {
		return errors.New("warehouse ID is required")
	}

	if req.Start.IsZero() {
		return errors.New("start time is required")
	}

	if req.End.IsZero() {
		return errors.New("end time is required")
	}

	if req.End.Before(req.Start) {
		return errors.New("end time cannot be before start time")
	}

	return nil
}

func (d *DaySmart) fetchOpenSlots(ctx context.Context, req models.GetOpenSlotsRequest) (GetOpenSlotsResp, error) {
	query := makeOpenSlotsQuery(req)

	var resp GetOpenSlotsResp
	if err := d.post(ctx, openSlotsEndpoint, query, nil, &resp); err != nil {
		return GetOpenSlotsResp{}, fmt.Errorf("API request failed: %w", err)
	}

	return resp, nil
}

func makeOpenSlotsQuery(req models.GetOpenSlotsRequest) url.Values {
	return url.Values{
		paramLocationID:     {req.WarehouseID},
		paramLimitStartTime: {strconv.FormatInt(req.Start.Unix(), 10)},
		paramLimitEndTime:   {strconv.FormatInt(req.End.Unix(), 10)},
	}
}

func (d *DaySmart) processOpenSlots(ctx context.Context, resp GetOpenSlotsResp) ([]models.Slot, error) {
	startTime, err := parseSlotTime(resp.Data.StartTime)
	if err != nil {
		log.Warn(ctx, "failed to parse start time",
			zap.String("startTime", resp.Data.StartTime),
			zap.Error(err),
		)

		return nil, fmt.Errorf("invalid start time format: %w", err)
	}

	slot := models.Slot{
		Warehouse: models.Warehouse{
			WarehouseID:       resp.Data.CID,
			WarehouseTimezone: resp.Data.TimeZone,
		},
		StartTimes: []time.Time{startTime},
	}

	return []models.Slot{slot}, nil
}

func parseSlotTime(timeStr string) (time.Time, error) {
	if timeStr == "" {
		return time.Time{}, errors.New("empty start time string")
	}

	parsedTime, err := helpers.ParseDatetime(timeStr)
	if err != nil {
		return time.Time{}, fmt.Errorf("failed to parse time string '%s': %w", timeStr, err)
	}

	return parsedTime, nil
}
