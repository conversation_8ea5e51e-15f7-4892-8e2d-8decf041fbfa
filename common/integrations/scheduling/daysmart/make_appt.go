package daysmart

import (
	"context"
	"errors"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/models"
)

const (
	createAppointmentEndpoint = "/Appointments/CreateAppointments"
)

const (
	paramCID           = "c_id"
	paramCustomerID    = "customer_id"
	paramDate          = "date"
	paramEmployeeID    = "employee_id"
	paramServiceID     = "service_id"
	paramStartTime     = "start_time"
	paramCustomerNotes = "customer_notes"
)

func (d *DaySmart) MakeAppointment(
	ctx context.Context,
	req models.MakeAppointmentRequest,
	_ ...models.SchedulingOption,
) (models.Appointment, error) {

	if err := validateAppointmentRequest(req); err != nil {
		return models.Appointment{}, fmt.Errorf("invalid appointment request: %w", err)
	}

	result, err := d.createAppointment(ctx, req)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create appointment: %w", err)
	}

	appointment, err := d.processAppointmentResponse(result.Data)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to process appointment response: %w", err)
	}

	return appointment, nil
}

func (d *DaySmart) createAppointment(ctx context.Context, req models.MakeAppointmentRequest) (AppointmentResp, error) {
	queryParams := buildAppointmentParams(req)

	var result AppointmentResp
	if err := d.post(ctx, createAppointmentEndpoint, queryParams, nil, &result); err != nil {
		return AppointmentResp{}, fmt.Errorf("API request failed: %w", err)
	}

	if err := validateAppointmentResponse(result); err != nil {
		return AppointmentResp{}, fmt.Errorf("invalid response: %w", err)
	}

	return result, nil
}

func buildAppointmentParams(req models.MakeAppointmentRequest) url.Values {
	totalMinutes := calculateTotalMinutes(req.StartTime)

	return url.Values{
		paramCID:           {req.WarehouseID},
		paramCustomerID:    {""}, // required field
		paramDate:          {req.StartTime.Format(dateFormat)},
		paramEmployeeID:    {""}, // required field
		paramServiceID:     {""}, // required field
		paramStartTime:     {strconv.Itoa(totalMinutes)},
		paramCustomerNotes: {req.Notes},
	}
}

func calculateTotalMinutes(t time.Time) int {
	return t.Hour()*60 + t.Minute()
}

func validateAppointmentRequest(req models.MakeAppointmentRequest) error {
	if req.WarehouseID == "" {
		return errors.New("warehouse ID is required")
	}

	if req.WarehouseTimezone == "" {
		return errors.New("warehouse timezone is required")
	}

	if req.StartTime.IsZero() {
		return errors.New("start time is required")
	}

	return nil
}

func (d *DaySmart) processAppointmentResponse(data AppointmentData) (models.Appointment, error) {
	startTime, err := minutesSinceMidnight(data.Date, data.StartTime)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to calculate appointment time: %w", err)
	}

	return models.Appointment{
		ExternalID:  data.ApptID,
		WarehouseID: data.LocationIDCID,
		Date:        startTime.Format(time.DateOnly),
		StartTime:   startTime,
		Notes:       data.CustomerNotes,
	}, nil
}

func (d *DaySmart) MakeAppointmentWithLoad(
	_ context.Context,
	_ models.MakeAppointmentRequest,
	_ models.Load,
) (models.Appointment, error) {

	return models.Appointment{}, errtypes.NotImplemented(models.DaySmart, "MakeAppointmentWithLoad")
}
