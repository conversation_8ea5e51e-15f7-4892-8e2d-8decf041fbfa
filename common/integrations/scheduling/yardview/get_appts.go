package yardview

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

func (y *YardView) GetAppointments(
	ctx context.Context,
	status models.AppointmentStatus,
	startDate,
	endDate,
	customerID string,
) ([]models.Appointment, error) {

	return y.GetAppointmentsWithCyclops(ctx, status, startDate, endDate, customerID)
}

func (y *YardView) GetAppointmentsWithCyclops(
	ctx context.Context,
	status models.AppointmentStatus,
	startDate,
	endDate,
	customerID string,
) ([]models.Appointment, error) {

	req := models.CyclopsGetAppointmentsRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    YardViewPlatform,
			Action:      models.ActionGetAppointment,
			UserID:      y.scheduler.Username,
			Mode:        models.CyclopsModeAPI,
			Credentials: models.CyclopsCredentials{
				Username: y.creds.Username,
				Password: y.creds.Password,
			},
		},
		Status:     status,
		StartDate:  startDate,
		EndDate:    endDate,
		CustomerID: customerID,
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return []models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := helpers.GetCyclopsURL()
	if err != nil {
		return []models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return []models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return []models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return []models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	var res models.CyclopsGetAppointmentResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return []models.Appointment{}, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return []models.Appointment{}, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	if len(res.Appointments) < 1 {
		return []models.Appointment{}, &models.CyclopsError{
			Message: "no appointments found",
			Errors:  []string{"no appointments found"},
		}
	}

	var appts []models.Appointment
	for _, appt := range res.Appointments {
		item, err := convertToAppointment(appt)
		if err != nil {
			log.Info(ctx, "failed to convert cyclops appointment", zap.Any("appt", appt), zap.Error(err))
			continue
		}

		appts = append(appts, item)
	}

	return appts, nil
}
