package yardview

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strconv"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	WarehouseInfo struct {
		Area     string `json:"area"`
		Facility string `json:"facility"`
		Name     string `json:"name"`
		Pid      int    `json:"pid"`
	}

	WarehouseResponse struct {
		models.CyclopsBaseResponse
		warehouses []WarehouseInfo
	}
)

func (y *YardView) GetAllWarehouses(ctx context.Context) ([]models.Warehouse, error) {
	return y.GetAllWarehousesWithCyclops(ctx)
}

func (y *YardView) GetAllWarehousesWithCyclops(ctx context.Context) ([]models.Warehouse, error) {
	req := models.CyclopsBaseRequest{
		Integration: models.CyclopsSchedulingIntegration,
		Platform:    YardViewPlatform,
		Action:      models.ActionGetWarehouse,
		UserID:      y.scheduler.Username,
		Mode:        models.CyclopsModeAPI,
		Credentials: models.CyclopsCredentials{
			Username: y.creds.Username,
			Password: y.creds.Password,
		},
	}

	reqBody, err := json.Marshal(req)
	if err != nil {
		return []models.Warehouse{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	url, err := helpers.GetCyclopsURL()
	if err != nil {
		return []models.Warehouse{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		url,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return []models.Warehouse{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return []models.Warehouse{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return []models.Warehouse{}, fmt.Errorf("failed to read response: %w", err)
	}

	var res WarehouseResponse
	if err = json.Unmarshal(body, &res); err != nil {
		return []models.Warehouse{}, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !res.Success {
		return []models.Warehouse{}, &models.CyclopsError{
			Message: res.Message,
			Errors:  res.Errors,
		}
	}

	if len(res.warehouses) < 1 {
		return []models.Warehouse{}, &models.CyclopsError{
			Message: "no warehouses found",
			Errors:  []string{"no warehouses found"},
		}
	}

	var warehouses []models.Warehouse
	for _, wh := range res.warehouses {
		item := convertToWarehouse(wh)
		warehouses = append(warehouses, item)
	}

	return warehouses, nil
}

func convertToWarehouse(wh WarehouseInfo) models.Warehouse {
	id := strconv.Itoa(wh.Pid)
	return models.Warehouse{
		WarehouseName:           wh.Name + " - " + wh.Facility + " - " + wh.Area,
		WarehouseFullIdentifier: wh.Name + " - " + wh.Facility + " - " + wh.Area,
		WarehouseID:             id,
		Source:                  models.YardViewSource,
	}
}
