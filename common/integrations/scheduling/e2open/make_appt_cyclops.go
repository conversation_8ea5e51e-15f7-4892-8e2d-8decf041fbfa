package e2open

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	CyclopsMakeAppointmentRequest struct {
		models.CyclopsBaseRequest
		Appointments []AppointmentData  `json:"appointments"`
		RequestType  string             `json:"requestType"`
		Operation    string             `json:"operation"`
		Company      string             `json:"company"`
	}
)

func (e *E2open) MakeAppointmentWithCyclops(
	ctx context.Context,
	req models.MakeAppointmentRequest,
) (models.Appointment, error) {

	if req.RequestType == "" {
		req.RequestType = string(models.RequestTypePickup)
	}

	reqType := models.RequestType(req.RequestType)
	if !reqType.IsValid() {
		return models.Appointment{}, fmt.Errorf("invalid request type: %s", req.RequestType)
	}

	cyclopsReq := CyclopsMakeAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    E2openPlatform,
			Action:      models.ActionMakeAppointment,
			UserID:      e.scheduler.Username,
			Credentials: models.CyclopsCredentials{
				Username: e.creds.Username,
				Password: e.creds.Password,
			},
		},
		Appointments: "",//how should I handel code here??,
		RequestType: req.appointmentType,
		Operation: req.Operation,
		Company: req.Company,
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	cyclopsURL, err := helpers.GetCyclopsURL()
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		cyclopsURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	var apptResp models.CyclopsMakeAppointmentResponse
	if err = json.Unmarshal(body, &apptResp); err != nil {
		return models.Appointment{}, fmt.Errorf("failed to unmarshal body: %w", err)
	}

	if !apptResp.Success {
		return models.Appointment{}, &models.CyclopsError{
			Message: apptResp.Message,
			Errors:  apptResp.Errors,
		}
	}

	return convertToAppointment(apptResp)
}
