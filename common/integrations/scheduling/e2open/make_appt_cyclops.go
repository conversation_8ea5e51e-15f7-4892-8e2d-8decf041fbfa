package e2open

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/drumkitai/drumkit/common/helpers"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/models"
)

type (
	CyclopsMakeAppointmentRequest struct {
		models.CyclopsBaseRequest
		Appointments []AppointmentData `json:"appointments"`
		RequestType  string            `json:"requestType"`
		Operation    string            `json:"operation"`
		Company      string            `json:"company"`
	}
)

func (e *E2open) MakeAppointmentWithCyclops(
	ctx context.Context,
	req models.MakeAppointmentRequest,
) (models.Appointment, error) {

	if req.RequestType == "" {
		req.RequestType = string(models.RequestTypePickup)
	}

	reqType := models.RequestType(req.RequestType)
	if !reqType.IsValid() {
		return models.Appointment{}, fmt.Errorf("invalid request type: %s", req.RequestType)
	}

	// Handle multiple appointments if provided
	var appointments []AppointmentData
	if len(req.Appointments) > 0 {
		// Process multiple appointments
		for _, apptReq := range req.Appointments {
			appointmentData, err := e.buildAppointmentData(ctx, apptReq, req)
			if err != nil {
				return models.Appointment{}, fmt.Errorf("failed to build appointment data for %s: %w", apptReq.FreightTrackingID, err)
			}
			appointments = append(appointments, appointmentData)
		}
	} else {
		return models.Appointment{}, fmt.Errorf("appointments not received")
	}

	cyclopsReq := CyclopsMakeAppointmentRequest{
		CyclopsBaseRequest: models.CyclopsBaseRequest{
			Integration: models.CyclopsSchedulingIntegration,
			Platform:    E2openPlatform,
			Action:      models.ActionMakeAppointment,
			UserID:      e.scheduler.Username,
			Credentials: models.CyclopsCredentials{
				Username: e.creds.Username,
				Password: e.creds.Password,
			},
		},
		Appointments: appointments,
		RequestType:  req.AppointmentType,
		Operation:    req.Operation,
		Company:      req.Company,
	}

	reqBody, err := json.Marshal(cyclopsReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to marshal request: %w", err)
	}

	cyclopsURL, err := helpers.GetCyclopsURL()
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to get url: %w", err)
	}

	httpReq, err := http.NewRequestWithContext(
		ctx,
		http.MethodPost,
		cyclopsURL,
		bytes.NewBuffer(reqBody),
	)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to create request: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Accept", "application/json")

	resp, err := otel.TracingHTTPClient().Do(httpReq)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to execute request: %w", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return models.Appointment{}, fmt.Errorf("failed to read response: %w", err)
	}

	if len(req.Appointments) > 0 {
		// Multiple appointments response
		var multiApptResp CyclopsMultipleAppointmentResponse
		if err = json.Unmarshal(body, &multiApptResp); err != nil {
			return models.Appointment{}, fmt.Errorf("failed to unmarshal multiple appointments response: %w", err)
		}

		if !multiApptResp.Success {
			return models.Appointment{}, &models.CyclopsError{
				Message: multiApptResp.Message,
				Errors:  multiApptResp.Errors,
			}
		}

		// For now, return the first appointment for backward compatibility
		// In the future, this function might need to return multiple appointments
		appointments, err := convertToAppointments(multiApptResp)
		if err != nil {
			return models.Appointment{}, fmt.Errorf("failed to convert appointments: %w", err)
		}

		if len(appointments) == 0 {
			return models.Appointment{}, fmt.Errorf("no appointments returned from cyclops")
		}

		return appointments[0], nil
	} 
	
	return models.Appointment{}, fmt.Errorf("appointments not received")
}


// buildAppointmentData builds appointment data for multiple appointments
func (e *E2open) buildAppointmentData(ctx context.Context, apptReq models.AppointmentData, globalReq models.MakeAppointmentRequest) (AppointmentData, error) {
	// Parse the start time from the appointment request
	startTime, err := time.Parse("2006-01-02T15:04:05Z07:00", apptReq.Start)
	if err != nil {
		// Try alternative time formats
		if startTime, err = time.Parse("2006-01-02T15:04:05", apptReq.Start); err != nil {
			if startTime, err = time.Parse(time.RFC3339, apptReq.Start); err != nil {
				return AppointmentData{}, fmt.Errorf("invalid start time format: %s", apptReq.Start)
			}
		}
	}

	appointmentData := AppointmentData{
		ProID:           apptReq.FreightTrackingID,
		AppointmentTime: startTime.Format("2006-01-02T15:04:05Z07:00"),
	}

	// Build warehouse information if WarehouseID is available
	if globalReq.WarehouseID != "" {
		warehouse := &models.CyclopsWarehouseInfo{}

		// Try to get additional warehouse details from database
		warehouse = e.getWarehouseDetails(ctx, globalReq)

		appointmentData.Warehouse = warehouse
	}

	return appointmentData, nil
}


// getWarehouseDetails retrieves warehouse details from database
func (e *E2open) getWarehouseDetails(ctx context.Context, globalReq models.MakeAppointmentRequest) (*models.CyclopsWarehouseInfo) {
	// TODO: Implement actual database lookup for warehouse details
	// For now, return a placeholder implementation
	return &models.CyclopsWarehouseInfo{
		Name:         globalReq.WarehouseID,
		City:         "",
		State:        "",
		ZipCode:      "",
		Country:      "",
		Website:      "",
		StopType:     globalReq.RequestType,
	}
}
