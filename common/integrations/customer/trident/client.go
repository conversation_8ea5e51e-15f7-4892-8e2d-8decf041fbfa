package trident

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"go.uber.org/zap"

	"github.com/drumkitai/drumkit/common/errtypes"
	"github.com/drumkitai/drumkit/common/helpers/otel"
	"github.com/drumkitai/drumkit/common/log"
	"github.com/drumkitai/drumkit/common/models"
)

type Trident struct {
	customer models.Integration
	host     string
}

type API interface {
	PostGSQuote(ctx context.Context, quote GreenscreensSubmission) (*GreenscreensSubmissionResponse, error)
	PostUserQuote(ctx context.Context, quote UserSubmission) (*UserSubmissionResponse, error)
	GetLaneRate(ctx context.Context, req LaneRateRequest) (*LaneRateResponse, error)
}

func New(ctx context.Context, customer models.Integration) (API, error) {
	log.With(ctx, zap.Uint("drumkitCustomerID", customer.ID), zap.String("customerName", "trident"))

	if customer.Disabled {
		return nil, errtypes.DisabledIntegrationError(customer)
	}

	var client Trident

	client.customer = customer
	client.host = customer.Tenant

	return &client, nil
}

func get(ctx context.Context, address string, headers map[string]string, reqBody any, dst any) error {

	// Create a new GET request with an empty body (to be overridden if reqBody is not nil)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, address, nil)
	if err != nil {
		return fmt.Errorf("failed to build GET request: %w", err)
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	if reqBody != nil {
		bodyBytes, err := json.Marshal(reqBody)
		if err != nil {
			return fmt.Errorf("failed to marshal request body: %w", err)
		}

		req.Header.Set("Content-Type", "application/json")
		req.Body = io.NopCloser(bytes.NewReader(bodyBytes))

		// set the Content-Length header explicitly to ensure compatibility
		req.ContentLength = int64(len(bodyBytes))
	}

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		return fmt.Errorf("failed to send GET request: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, err := io.ReadAll(resp.Body)
		if err != nil {
			return fmt.Errorf("failed to read response body: %w", err)
		}
		return fmt.Errorf("unexpected status code: %d, body: %s", resp.StatusCode, string(body))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if err = json.Unmarshal(body, &dst); err != nil {
		return fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return nil
}

func post(ctx context.Context, address string, headers map[string]string, reqBody any, dst any) error {

	req, err := http.NewRequestWithContext(ctx, http.MethodPost, address, nil)
	if err != nil {
		return fmt.Errorf("failed to build POST request: %w", err)
	}

	for key, value := range headers {
		req.Header.Set(key, value)
	}

	bodyBytes, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %w", err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Body = io.NopCloser(bytes.NewReader(bodyBytes))

	resp, err := otel.TracingHTTPClient().Do(req)
	if err != nil {
		return fmt.Errorf("failed to send POST request: %w", err)
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if err = json.Unmarshal(respBody, &dst); err != nil {
		return fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return nil
}
