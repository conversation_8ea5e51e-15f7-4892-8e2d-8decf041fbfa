package models

import (
	"time"

	"github.com/lib/pq"
	"gorm.io/gorm"
)

type (
	// Appointment is a delivery appt which has been successfully submitted via Drumkit.
	Appointment struct {
		gorm.Model

		// The email address of the inbox account owner that scheduled the appt (will match the users table)
		// e.g. "<EMAIL>"
		Account   string `gorm:"index"`
		ServiceID uint   `gorm:"index"`

		FreightTrackingID string `gorm:"index"`

		// Appointment ID in the external system (e.g. Opendock)
		ExternalID string `validate:"required"`
		// Confirmation number may be different from ID (e.g. Opendock)
		ConfirmationNo string

		// Delivery details
		WarehouseID string
		LoadTypeID  string
		DockID      string
		Date        string
		PONums      string
		RefNumber   string
		CcEmails    pq.StringArray `gorm:"type:text[]"`
		StartTime   time.Time
		Notes       string
		Source      IntegrationName

		Status string
	}

	// A scheduled appointment.
	ScheduledAppointment struct {
		Request            MakeAppointmentRequest
		ID                 string `json:"id"`
		ConfirmationNumber string `json:"confirmationNumber"`

		CreateDateTime       time.Time                 `json:"createDateTime"`
		CreatedBy            string                    `json:"createdBy"`
		LastChangedDateTime  time.Time                 `json:"lastChangedDateTime"`
		LastChangedBy        string                    `json:"lastChangedBy"`
		IsActive             bool                      `json:"isActive"`
		Tags                 []string                  `json:"tags"`
		Type                 string                    `json:"type"`
		Status               string                    `json:"status"`
		StatusTimeline       AppointmentStatusTimeline `json:"statusTimeline"`
		OrgID                string                    `json:"orgId"`
		Eta                  any                       `json:"eta"`
		RecurringParentID    any                       `json:"recurringParentId"`
		RecurringPattern     any                       `json:"recurringPattern"`
		Reschedules          any                       `json:"reschedules"`
		MuteNotifications    bool                      `json:"muteNotifications"`
		IsCheckedInByCarrier bool                      `json:"isCheckedInByCarrier"`
		CheckInAcknowledged  bool                      `json:"checkInAcknowledged"`
		RecurringParent      any                       `json:"recurringParent"`
	}

	AppointmentStatusTimeline struct {
		NoShow     time.Time `json:"noShow"`
		Arrived    time.Time `json:"arrived"`
		Cancelled  time.Time `json:"cancelled"`
		Completed  time.Time `json:"completed"`
		Requested  time.Time `json:"requested"`
		Scheduled  time.Time `json:"scheduled"`
		InProgress time.Time `json:"inProgress"`
	}

	// AppointmentData represents a single appointment request for bulk scheduling
	AppointmentData struct {
		Start             string `json:"start" validate:"required"`
		FreightTrackingID string `json:"freightTrackingId" validate:"required"`
	}

	// MakeAppointmentRequest encapsulates details for scheduling appointments.
	MakeAppointmentRequest struct {
		// User-provided
		StartTime         time.Time  `json:"start" validate:"required"`
		EndTime           *time.Time `json:"end,omitempty"`
		WarehouseID       string     `json:"warehouseID,omitempty"`
		WarehouseTimezone string     `json:"warehouseTimezone"`
		DockID            string     `json:"dockId" validate:"required"`
		LoadTypeID        string     `json:"loadTypeId" validate:"required"`
		Operation         string     `json:"operation,omitempty"`
		Company           string     `json:"company,omitempty"`
		// Automatically filled by MakeAppointment
		UserID    string   `json:"userId,omitempty"`
		RefNumber string   `json:"refNumber,omitempty"`
		PONums    string   `json:"poNums"`
		Notes     string   `json:"notes,omitempty"`
		CcEmails  []string `json:"ccEmails,omitempty"`
		// Hard-coded defaults for 7/11; provided by user for other warehouses
		CustomFields CustomApptFieldsTemplate `json:"customFields,omitempty"`
		//nolint:lll // Specific to 7/11
		TrailerType string `json:"trailerType,omitempty" validate:"required,oneof='Cargo Van' 'LTL/FTL Trailer' 'Passenger Vehicle' 'Straight Truck'"`
		RequestType string `json:"requestType,omitempty"`

		// For bulk support
		AppointmentType string            `json:"appointmentType,omitempty"`
		Appointments    []AppointmentData `json:"appointments,omitempty"`
	}

	// Dock represents a loading dock within a warehouse.
	Dock struct {
		ID          string   `json:"id"`
		Name        string   `json:"name"`
		WarehouseID string   `json:"warehouseId"`
		LoadTypeIDs []string `json:"loadTypeIds"`
	}

	// LoadType encapsulates details related to a loading type.
	LoadType struct {
		ID                     string           `json:"id"`
		Name                   string           `json:"name"`
		WarehouseID            string           `json:"warehouseId"`
		EquipmentType          string           `json:"equipmentType"`
		CreateDateTime         time.Time        `json:"createDateTime"`
		CreatedBy              string           `json:"createdBy"`
		LastChangedDateTime    time.Time        `json:"lastChangedDateTime"`
		LastChangedBy          string           `json:"lastChangedBy"`
		IsActive               bool             `json:"isActive"`
		Tags                   any              `json:"tags"`
		AllowCarrierScheduling bool             `json:"allowCarrierScheduling"`
		DurationMin            int              `json:"duration_min"`
		OrgID                  string           `json:"orgId"`
		Direction              string           `json:"direction"`
		Operation              string           `json:"operation"`
		TransportationMode     string           `json:"transportationMode"`
		Description            string           `json:"description"`
		Settings               LoadTypeSettings `json:"settings"`
		Schedule               LoadTypeSchedule `json:"schedule"`
	}

	// LoadTypeSettings configures load type-specific settings.
	LoadTypeSettings struct {
		AllowCarrierDockSelection bool `json:"allowCarrierDockSelection"`
	}

	// LoadTypeSchedule defines schedules for different days.
	LoadTypeSchedule struct {
		Friday          []ScheduleHours `json:"friday"`
		Monday          []ScheduleHours `json:"monday"`
		Sunday          []ScheduleHours `json:"sunday"`
		Tuesday         []ScheduleHours `json:"tuesday"`
		Saturday        []ScheduleHours `json:"saturday"`
		Thursday        []ScheduleHours `json:"thursday"`
		Wednesday       []ScheduleHours `json:"wednesday"`
		ClosedIntervals []any           `json:"closedIntervals"`
		Version         int             `json:"version"`
	}

	// ScheduleHours defines the working hours for a day.
	ScheduleHours struct {
		End   string `json:"end"`
		Start string `json:"start"`
	}

	// Slot represents an available time slot with associated dock and warehouse.
	Slot struct {
		Dock       Dock        `json:"dock"`
		StartTimes []time.Time `json:"startTimes"`
		Warehouse  Warehouse   `json:"warehouse"`
	}

	SchedulingUser struct {
		ID                       string    `json:"id"`
		CreateDateTime           time.Time `json:"createDateTime"`
		LastChangedDateTime      time.Time `json:"lastChangedDateTime"`
		IsActive                 bool      `json:"isActive"`
		Tags                     any       `json:"tags"`
		Email                    string    `json:"email"`
		FirstName                string    `json:"firstName"`
		LastName                 string    `json:"lastName"`
		IsEmailVerified          bool      `json:"isEmailVerified"`
		Role                     string    `json:"role"`
		OrgID                    any       `json:"orgId"`
		CompanyID                string    `json:"companyId"`
		Phone                    string    `json:"phone"`
		CreatedBy                any       `json:"createdBy"`
		LastChangedBy            string    `json:"lastChangedBy"`
		InvalidLoginAttempts     int       `json:"invalidLoginAttempts"`
		WarehouseAccessList      any       `json:"warehouseAccessList"`
		Extension                any       `json:"extension"`
		TcConfirmedAt            any       `json:"tcConfirmedAt"`
		LastLoginAt              any       `json:"lastLoginAt"`
		PasswordResetRequired    bool      `json:"passwordResetRequired"`
		PasswordResetEmailSentAt any       `json:"passwordResetEmailSentAt"`
		OrgIsActive              any       `json:"orgIsActive"`
		OrgName                  any       `json:"orgName"`
		OrgCreateDateTime        any       `json:"orgCreateDateTime"`
		OrgType                  any       `json:"orgType"`
	}

	// GetDocksRequest encapsulates parameters for fetching docks.
	GetDocksRequest struct {
		Search string // required - never fetch all docks for all warehouses
		Page   int
		Limit  int
	}

	// GetLoadTypesRequest encapsulates parameters for fetching load types.
	GetLoadTypesRequest struct {
		// (Required)
		WarehouseID string
		Page        int
		Limit       int
		Search      string // EX: {"allowCarrierScheduling":true}
	}

	// GetOpenSlotsRequest encapsulates parameters for fetching open slots.
	GetOpenSlotsRequest struct {
		Start             time.Time   `json:"start" validate:"required"` // RFC3339
		End               time.Time   `json:"end" validate:"required"`   // RFC3339
		IncludeStartTimes bool        `json:"includeStartTimes"`
		WarehouseID       string      `json:"warehouseId"`
		Warehouse         *Warehouse  `json:"warehouse,omitempty"`
		PONumbers         []string    `json:"poNums"`
		RequestType       RequestType `json:"requestType,omitempty"`
		Operation         string      `json:"operation,omitempty"`
		Company           string      `json:"company,omitempty"`
		AppointmentType   string      `json:"appointmentType,omitempty"`
		// `Inbound`, `TargetAirFreightOnly`, `TaxAirFreight`, or `Elwood`
		FilterType string `json:"filterType,omitempty"`
	}

	// WarehouseDetails contains warehouse data for responses.
	WarehouseDetails struct {
		// Neutron's ID for the warehouse, equal to SevenElevenNeutronWarehouseID
		ID                       string                   `json:"id"`
		Name                     string                   `json:"name"`
		Street                   string                   `json:"street"`
		City                     string                   `json:"city"`
		State                    string                   `json:"state"`
		Zip                      string                   `json:"zip"`
		Email                    string                   `json:"email"`
		Phone                    string                   `json:"phone"`
		Timezone                 string                   `json:"timezone"`
		CustomApptFieldsTemplate CustomApptFieldsTemplate `json:"customApptFieldsTemplate"`
		Settings                 WarehouseSettings        `json:"settings"`
		DefaultSubscribedEmail   string                   `json:"defaultSubscribedEmail"`
	}

	ValidatedPONumber struct {
		PONumber string `json:"poNumber"`
		IsValid  bool   `json:"isValid"`
		Error    string `json:"error"`
	}
)
