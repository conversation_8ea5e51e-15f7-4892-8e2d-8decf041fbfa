package models

// Struct with same structure as Load{} to indicate field attributes,
// such as if it's supported by the TMS, if it's read-only
// NOTE: Order of definition matters; must be the same order as Load{}
type (
	FieldAttributes struct {
		// TMS does not support this field
		IsNotSupported bool `json:"isNotSupported"`
		// This field is either always or temporarily read-only
		IsReadOnly      bool             `json:"isReadOnly"`
		MaxLength       int              `json:"maxLength"`
		DropdownOptions []DropdownOption `json:"dropdownOptions"`
	}

	DropdownOption struct {
		Value string `json:"value"` // If none provided, label is used
		Label string `json:"label"`
	}

	LoadCoreInfoAttributes struct {
		Status           FieldAttributes    `json:"status"`
		Mode             FieldAttributes    `json:"mode"`
		MoreThanTwoStops FieldAttributes    `json:"moreThanTwoStops"` // LTL not necessarily more than 2 stops
		PONums           FieldAttributes    `json:"poNums"`
		Operator         FieldAttributes    `json:"operator"`
		Customer         CustomerAttributes `json:"customer"`
		BillTo           BillToAttributes   `json:"billTo"`
		RateData         RateDataAttributes `json:"rateData"`

		Stops          []StopAttributes         `json:"stops"`
		Pickup         PickupAttributes         `json:"pickup"`
		Consignee      ConsigneeAttributes      `json:"consignee"`
		Carrier        CarrierAttributes        `json:"carrier"`
		Specifications SpecificationsAttributes `json:"specifications"`
		Notes          NoteAttributes           `json:"notes"`

		PickupDate  FieldAttributes `json:"pickupDate"`
		DropoffDate FieldAttributes `json:"dropoffDate"`
	}

	// NOTE: Order of definition matters; fields must in be the same order as Load{}
	LoadAttributes struct {
		// Internals
		Model FieldAttributes `json:"-"` // matching gorm.Model{}; not relevant

		LoadCoreInfoAttributes

		ServiceID FieldAttributes `json:"-"`     // not relevant
		Service   FieldAttributes `json:"-"`     // not relevant
		TMSID     FieldAttributes `json:"tmsID"` // not relevant
		TMS       FieldAttributes `json:"-"`     // not relevant

		Emails          FieldAttributes `gorm:"many2many:email_loads;" json:"-"`
		GeneratedEmails FieldAttributes `gorm:"many2many:generated_email_loads;" json:"-"`
		IsPlaceholder   FieldAttributes `json:"-"` // not relevant

		ExternalTMSID     FieldAttributes `json:"externalTMSID"`
		FreightTrackingID FieldAttributes `json:"freightTrackingID"`
		Commodities       FieldAttributes `json:"-"`
		DeclaredValueUSD  FieldAttributes `json:"declaredValueUSD"`

		PickupWarehouseID  FieldAttributes `json:"pickupWarehouseID"`  // not relevant
		PickupWarehouse    FieldAttributes `json:"-"`                  // not relevant
		DropoffWarehouseID FieldAttributes `json:"dropoffWarehouseID"` // not relevant
		DropoffWarehouse   FieldAttributes `json:"-"`                  // not relevant
	}

	CustomerAttributes struct {
		// NOTE: JSON marshaling flattens anonymous structs; this behavior is required for front-end attribute logic
		// Defining the field as "CompanyCoreInfoAttributes CompanyCoreInfoAttributes" breaks the front-end.
		CompanyCoreInfoAttributes
		RefNumber FieldAttributes `json:"refNumber"`
	}

	BillToAttributes struct {
		CompanyCoreInfoAttributes
	}

	// NOTE: Order of definition matters; fields must in be the same order as Pickup{}
	PickupAttributes struct {
		CompanyCoreInfoAttributes
		ExternalTMSStopID    FieldAttributes
		BusinessHours        FieldAttributes `json:"businessHours"`
		RefNumber            FieldAttributes `json:"refNumber"`
		AdditionalReferences FieldAttributes `json:"additionalReferences"`
		ReadyTime            FieldAttributes `json:"readyTime"`
		ApptRequired         FieldAttributes `json:"apptRequired"`
		ApptConfirmed        FieldAttributes `json:"apptConfirmed"`
		ApptType             FieldAttributes `json:"apptType"`
		ApptStartTime        FieldAttributes `json:"apptStartTime"`
		ApptEndTime          FieldAttributes `json:"apptEndTime"`
		ApptNote             FieldAttributes `json:"apptNote"`
		Timezone             FieldAttributes `json:"timezone"`
		ZipPrefix            FieldAttributes `json:"zipPrefix"`
	}

	AddressAttributes struct {
		Name         FieldAttributes `json:"name"`
		AddressLine1 FieldAttributes `json:"addressLine1"`
		AddressLine2 FieldAttributes `json:"addressLine2"`
		City         FieldAttributes `json:"city"`
		State        FieldAttributes `json:"state"`
		Zip          FieldAttributes `json:"zip"`
		Timezone     FieldAttributes `json:"timezone"`
		Country      FieldAttributes `json:"country"`
		IsVerified   FieldAttributes `json:"isVerified"`
	}

	// NOTE: Order of definition matters; fields must in be the same order as Stop{}
	StopAttributes struct {
		// Internals
		Model FieldAttributes `json:"-"` // matching gorm.Model{}; not relevant
		// Regular fields
		LoadID FieldAttributes `json:"loadID"`
		Load   FieldAttributes `json:"-"` // Relationship field

		StopType             FieldAttributes   `json:"stopType"`
		StopNumber           FieldAttributes   `json:"stopNumber"`
		QuoteID              FieldAttributes   `json:"quoteID"`
		Quote                FieldAttributes   `json:"-"` // Relationship field
		Order                FieldAttributes   `json:"order"`
		Address              AddressAttributes `json:"address"`
		ExternalTMSID        FieldAttributes   `json:"externalTMSID"`
		Contact              FieldAttributes   `json:"contact"`
		Phone                FieldAttributes   `json:"phone"`
		Email                FieldAttributes   `json:"email"`
		ExternalTMSStopID    FieldAttributes   `json:"externalTMSStopID"`
		BusinessHours        FieldAttributes   `json:"businessHours"`
		RefNumber            FieldAttributes   `json:"refNumber"`
		ReadyTime            FieldAttributes   `json:"readyTime"`
		MustDeliver          FieldAttributes   `json:"mustDeliver"`
		ApptRequired         FieldAttributes   `json:"apptRequired"`
		ApptType             FieldAttributes   `json:"apptType"`
		ApptStartTime        FieldAttributes   `json:"apptStartTime"`
		ApptEndTime          FieldAttributes   `json:"apptEndTime"`
		ActualStartTime      FieldAttributes   `json:"actualStartTime"`
		ActualEndTime        FieldAttributes   `json:"actualEndTime"`
		ExpectedStartTime    FieldAttributes   `json:"expectedStartTime"`
		ApptNote             FieldAttributes   `json:"apptNote"`
		Timezone             FieldAttributes   `json:"timezone"`
		AdditionalReferences FieldAttributes   `json:"additionalReferences"`
	}

	// NOTE: Order of definition matters; fields must in be the same order as Consignee{}
	ConsigneeAttributes struct {
		CompanyCoreInfoAttributes
		ExternalTMSStopID    FieldAttributes
		BusinessHours        FieldAttributes `json:"businessHours"`
		RefNumber            FieldAttributes `json:"refNumber"`
		AdditionalReferences FieldAttributes `json:"additionalReferences"`
		MustDeliver          FieldAttributes `json:"mustDeliver"`
		ApptRequired         FieldAttributes `json:"apptRequired"`
		ApptConfirmed        FieldAttributes `json:"apptConfirmed"`
		ApptType             FieldAttributes
		ApptStartTime        FieldAttributes `json:"apptStartTime"`
		ApptEndTime          FieldAttributes `json:"apptEndTime"`
		ApptNote             FieldAttributes `json:"apptNote"`
		Timezone             FieldAttributes `json:"timezone"`
		ZipPrefix            FieldAttributes `json:"zipPrefix"`
	}

	// NOTE: Order of definition matters; fields must in be the same order as CompanyCoreInfo{}
	CompanyCoreInfoAttributes struct {
		ExternalTMSID FieldAttributes `json:"externalTMSID"`
		Name          FieldAttributes `json:"name"`
		AddressLine1  FieldAttributes `json:"addressLine1"`
		AddressLine2  FieldAttributes `json:"addressLine2"`
		City          FieldAttributes `json:"city"`
		State         FieldAttributes `json:"state"`
		Zipcode       FieldAttributes `json:"zipCode"`
		Country       FieldAttributes `json:"country"`
		Contact       FieldAttributes `json:"contact"`
		Phone         FieldAttributes `json:"phone"`
		Email         FieldAttributes `json:"email"`
	}

	// NOTE: Order of definition matters; fields must in be the same order as RateData{}
	RateDataAttributes struct {
		CollectionMethod       FieldAttributes `json:"collectionMethod"`
		RevenueCode            FieldAttributes `json:"revenueCode"`
		Salesperson1           FieldAttributes `json:"salesperson1"`
		Salesperson1Percent    FieldAttributes `json:"salesperson1Percent"`
		CustomerRateType       FieldAttributes `json:"customerRateType"`
		CustomerLineHaulCharge FieldAttributes `json:"customerLineHaulCharge"`
		CustomerRateNumUnits   FieldAttributes `json:"customerRateNumUnits"`
		CustomerLineHaulRate   FieldAttributes `json:"customerLineHaulRate"`
		CustomerLineHaulUnit   FieldAttributes `json:"customerLineHaulUnit"`
		CustomerTotalCharge    FieldAttributes `json:"customerTotalCharge"`

		FSCPercent  FieldAttributes `json:"fscPercent"`
		FSCPerMile  FieldAttributes `json:"fscPerMile"`
		FSCFlatRate FieldAttributes `json:"fscFlatRate"`

		CarrierRateType       FieldAttributes `json:"carrierRateType"`
		CarrierLineHaulCharge FieldAttributes `json:"carrierLineHaulCharge"`
		CarrierRateNumUnits   FieldAttributes `json:"carrierRateNumUnits"`
		CarrierLineHaulRate   FieldAttributes `json:"carrierLineHaulRate"`
		CarrierLineHaulUnit   FieldAttributes `json:"carrierLineHaulUnit"`
		CarrierTotalCost      FieldAttributes `json:"carrierTotalCost"` // Deprecated, TODO: remove
		CarrierCost           FieldAttributes `json:"carrierCost"`
		CarrierCostCurrency   FieldAttributes `json:"carrierCostCurrency"`
		CarrierMaxRate        FieldAttributes `json:"carrierMaxRate"`

		NetProfitUSD  FieldAttributes `json:"netProfitUSD"`
		ProfitPercent FieldAttributes `json:"profitPercent"`
	}

	// NOTE: Order of definition matters; fields must in be the same order as Carrier{}
	CarrierAttributes struct {
		ExternalTMSID FieldAttributes `json:"externalTMSID"`
		MovementID    FieldAttributes `json:"movementID"` // Primarily for Mcleod
		Name          FieldAttributes `json:"name"`
		MCNumber      FieldAttributes `json:"mcNumber"`
		DOTNumber     FieldAttributes `json:"dotNumber"`
		SealNumber    FieldAttributes `json:"sealNumber"`
		SCAC          FieldAttributes `json:"scac"`
		Phone         FieldAttributes `json:"phone"`
		Email         FieldAttributes `json:"email"`
		Notes         FieldAttributes `json:"notes"`

		FirstDriverName      FieldAttributes `json:"firstDriverName"`
		FirstDriverPhone     FieldAttributes `json:"firstDriverPhone"`
		SecondDriverName     FieldAttributes `json:"secondDriverName"`
		SecondDriverPhone    FieldAttributes `json:"secondDriverPhone"`
		Dispatcher           FieldAttributes `json:"dispatcher"`
		DispatchSource       FieldAttributes `json:"dispatchSource"`
		DispatchCity         FieldAttributes `json:"dispatchCity"`
		DispatchState        FieldAttributes `json:"dispatchState"`
		ExternalTMSTruckID   FieldAttributes `json:"truckNumber"`
		ExternalTMSTrailerID FieldAttributes `json:"trailerNumber"`

		RateConfirmationSent     FieldAttributes `json:"rateConfirmationSent"`
		ConfirmationSentTime     FieldAttributes `json:"confirmationSentTime"`
		ConfirmationReceivedTime FieldAttributes `json:"confirmationReceivedTime"`
		DispatchedTime           FieldAttributes `json:"dispatchedTime"`
		ExpectedPickupTime       FieldAttributes `json:"expectedPickupTime"`
		PickupStart              FieldAttributes `json:"pickupStart"`
		PickupEnd                FieldAttributes `json:"pickupEnd"`
		ExpectedDeliveryTime     FieldAttributes `json:"expectedDeliveryTime"`
		DeliveryStart            FieldAttributes `json:"deliveryStart"`
		DeliveryEnd              FieldAttributes `json:"deliveryEnd"`
		SignedBy                 FieldAttributes `json:"signedBy"`
	}

	// NOTE: Order of definition matters; fields must in be the same order as Specifications{}
	SpecificationsAttributes struct {
		OrderType           FieldAttributes `json:"orderType"`
		PalletsRequired     FieldAttributes `json:"palletsRequired"`
		TotalInPalletCount  FieldAttributes `json:"totalInPalletCount"`
		TotalOutPalletCount FieldAttributes `json:"totalOutPalletCount"`
		TotalPieces         FieldAttributes `json:"totalPieces"` // Pieces go into pallets
		Commodities         FieldAttributes `json:"commodities"`
		NumCommodities      FieldAttributes `json:"numCommodities"`
		TotalWeight         FieldAttributes `json:"totalWeight"`
		NetWeight           FieldAttributes `json:"netWeight"`
		BillableWeight      FieldAttributes `json:"billableWeight"`
		TotalDistance       FieldAttributes `json:"totalDistance"`
		ServiceType         FieldAttributes `json:"serviceType"`
		TransportType       FieldAttributes `json:"transportType"`
		TransportTypeEnum   FieldAttributes `json:"transportTypeEnum"`
		TransportSize       FieldAttributes `json:"transportSize"`
		MinTempFahrenheit   FieldAttributes `json:"minTempFahrenheit"`
		MaxTempFahrenheit   FieldAttributes `json:"maxTempFahtrenheit"`
		IsRefrigerated      FieldAttributes `json:"isRefrigerated"`
		LiftgatePickup      FieldAttributes `json:"liftgatePickup"`
		LiftgateDelivery    FieldAttributes `json:"liftgateDelivery"`
		InsidePickup        FieldAttributes `json:"insidePickup"`
		InsideDelivery      FieldAttributes `json:"insideDelivery"`
		Tarps               FieldAttributes `json:"tarps"`
		Oversized           FieldAttributes `json:"oversized"`
		Hazmat              FieldAttributes `json:"hazmat"`
		Straps              FieldAttributes `json:"straps"`
		Permits             FieldAttributes `json:"permits"`
		Escorts             FieldAttributes `json:"escorts"`
		Seal                FieldAttributes `json:"seal"`
		CustomBonded        FieldAttributes `json:"customBonded"`
		Labor               FieldAttributes `json:"labor"`
	}

	// NOTE: Order of definition matters; fields must in be the same order as Notes{}
	NoteAttributes struct {
		CreatedAt FieldAttributes `json:"createdAt"`
		UpdatedBy FieldAttributes `json:"updatedBy"`
		Note      FieldAttributes `json:"note"`
	}
)

var InitUnsupportedBillTo = BillToAttributes{
	CompanyCoreInfoAttributes: CompanyCoreInfoAttributes{
		ExternalTMSID: FieldAttributes{IsNotSupported: true},
		Name:          FieldAttributes{IsNotSupported: true},
		AddressLine1:  FieldAttributes{IsNotSupported: true},
		AddressLine2:  FieldAttributes{IsNotSupported: true},
		City:          FieldAttributes{IsNotSupported: true},
		State:         FieldAttributes{IsNotSupported: true},
		Zipcode:       FieldAttributes{IsNotSupported: true},
		Country:       FieldAttributes{IsNotSupported: true},
		Contact:       FieldAttributes{IsNotSupported: true},
		Phone:         FieldAttributes{IsNotSupported: true},
		Email:         FieldAttributes{IsNotSupported: true},
	},
}

var InitUnsupportedSpecs = SpecificationsAttributes{
	OrderType:           FieldAttributes{IsNotSupported: true},
	TotalInPalletCount:  FieldAttributes{IsNotSupported: true},
	TotalOutPalletCount: FieldAttributes{IsNotSupported: true},
	PalletsRequired:     FieldAttributes{IsNotSupported: true},
	TotalPieces:         FieldAttributes{IsNotSupported: true},
	Commodities:         FieldAttributes{IsNotSupported: true},
	NumCommodities:      FieldAttributes{IsNotSupported: true},
	TotalWeight:         FieldAttributes{IsNotSupported: true},
	NetWeight:           FieldAttributes{IsNotSupported: true},

	ServiceType:       FieldAttributes{IsNotSupported: true},
	TransportType:     FieldAttributes{IsNotSupported: true},
	TransportTypeEnum: FieldAttributes{IsNotSupported: true},
	TransportSize:     FieldAttributes{IsNotSupported: true},

	BillableWeight: FieldAttributes{IsNotSupported: true},
	TotalDistance:  FieldAttributes{IsNotSupported: true},

	MinTempFahrenheit: FieldAttributes{IsNotSupported: true},
	MaxTempFahrenheit: FieldAttributes{IsNotSupported: true},
	IsRefrigerated:    FieldAttributes{IsNotSupported: true},
	LiftgatePickup:    FieldAttributes{IsNotSupported: true},
	LiftgateDelivery:  FieldAttributes{IsNotSupported: true},
	InsidePickup:      FieldAttributes{IsNotSupported: true},
	InsideDelivery:    FieldAttributes{IsNotSupported: true},
	Tarps:             FieldAttributes{IsNotSupported: true},
	Oversized:         FieldAttributes{IsNotSupported: true},
	Hazmat:            FieldAttributes{IsNotSupported: true},
	Straps:            FieldAttributes{IsNotSupported: true},
	Permits:           FieldAttributes{IsNotSupported: true},
	Escorts:           FieldAttributes{IsNotSupported: true},
	Seal:              FieldAttributes{IsNotSupported: true},
	CustomBonded:      FieldAttributes{IsNotSupported: true},
	Labor:             FieldAttributes{IsNotSupported: true},
}

var InitUnsupportedRateData = RateDataAttributes{
	CollectionMethod:       FieldAttributes{IsNotSupported: true},
	RevenueCode:            FieldAttributes{IsNotSupported: true},
	Salesperson1:           FieldAttributes{IsNotSupported: true},
	Salesperson1Percent:    FieldAttributes{IsNotSupported: true},
	CustomerRateType:       FieldAttributes{IsNotSupported: true},
	CustomerLineHaulCharge: FieldAttributes{IsNotSupported: true},
	CustomerRateNumUnits:   FieldAttributes{IsNotSupported: true},
	CustomerLineHaulRate:   FieldAttributes{IsNotSupported: true},
	CustomerLineHaulUnit:   FieldAttributes{IsNotSupported: true},
	CustomerTotalCharge:    FieldAttributes{IsNotSupported: true},
	FSCPercent:             FieldAttributes{IsNotSupported: true},
	FSCPerMile:             FieldAttributes{IsNotSupported: true},
	FSCFlatRate:            FieldAttributes{IsNotSupported: true},
	CarrierRateType:        FieldAttributes{IsNotSupported: true},
	CarrierLineHaulCharge:  FieldAttributes{IsNotSupported: true},
	CarrierRateNumUnits:    FieldAttributes{IsNotSupported: true},
	CarrierLineHaulRate:    FieldAttributes{IsNotSupported: true},
	CarrierLineHaulUnit:    FieldAttributes{IsNotSupported: true},
	CarrierTotalCost:       FieldAttributes{IsNotSupported: true}, // Deprecated, TODO: remove
	CarrierCost:            FieldAttributes{IsNotSupported: true},
	CarrierCostCurrency:    FieldAttributes{IsNotSupported: true},
	CarrierMaxRate:         FieldAttributes{IsNotSupported: true},
	NetProfitUSD:           FieldAttributes{IsNotSupported: true},
	ProfitPercent:          FieldAttributes{IsNotSupported: true},
}
