package models

import (
	"fmt"

	"github.com/lib/pq"
	"gorm.io/gorm"
)

// In the future, can add additional service-specific config and a template form for customers
// to fill out (subject template, body template, etc)
type QuickQuoteConfig struct {
	gorm.Model
	// Foreign key to User leads to invalid recursive, use *User
	SenderID  *uint
	Sender    *User `json:"-"`
	ServiceID uint
	// NOTE: MUST define Service.QuickQuoteConfigID; Gorm hook below verifies this
	Service                   Service        `json:"-"`
	CC                        pq.StringArray `gorm:"type:text[]"`
	BCC                       pq.StringArray `gorm:"type:text[]"`
	LowConfidenceThreshold    int
	MediumConfidenceThreshold int
	OmitUnderLowThreshold     bool `gorm:"default:false;"`
	BelowThresholdMessage     string
	// SpecialEquipment is a list of unallowed transport types
	SpecialEquipment pq.StringArray `gorm:"type:text[]"`
	// OtherTransportTypes is a list of additional allowed transport types (e.g. box truck, hotshot)
	OtherTransportTypes  pq.StringArray `json:"otherTransportTypes" gorm:"type:text[]"`
	DefaultMarginType    MarginType     `json:"defaultMarginType" gorm:"default:Percentage"`
	DefaultPercentMargin int            `json:"defaultPercentMargin" gorm:"default:10"`
	DefaultFlatMargin    int            `json:"defaultFlatMargin" gorm:"default:100"`
	DefaultTransportType TransportType  `json:"defaultTransportType" gorm:"default:VAN"`
	// If false, FE buttons will input quote into form but not submit it. User must submit manually
	IsSubmitOnPortalEnabled bool `json:"isSubmitOnPortalEnabled" gorm:"default:false"`
	// FSCProvider is the provider of the FSC (Fuel Surcharge) feature in the
	// FE CarrierPriceCalculator. Currently we support DAT, DOE and quoting portals like E2Open
	FSCProvider string `json:"fscProvider" gorm:"default:''"`
}

func (q *QuickQuote) BeforeSave(tx *gorm.DB) (err error) {
	var service Service
	result := tx.Model(&Service{}).First(&service, q.ServiceID)
	if result.Error != nil {
		return fmt.Errorf("quickQuoteConfig.BeforeSave hook - failed to find service: %w", result.Error)
	}

	return nil
}

// Carrier Quote product configuration for a service
type CarrierQuoteConfig struct {
	gorm.Model

	ServiceID uint `gorm:"index:idx_service_id"`
	Service   Service

	CarrierQuoteConfigOptions
}

type CarrierQuoteConfigOptions struct {
	From        *string        `json:"from,omitempty"`
	CC          pq.StringArray `gorm:"type:text[]" json:"cc,omitempty"`
	BCC         pq.StringArray `gorm:"type:text[]" json:"bcc,omitempty"`
	BCCCarriers bool           `gorm:"default:true;" json:"bccCarriers"`
	// NOTE: Subject is currently set to name of Customer and using string on FE to set subject templates
	// TODO: support dynamic mustache variables for subject lines so we store the template in config
	Subject string `gorm:"default:''" json:"subject"`
	// NOTE: EmailBody is currently unused
	// TODO: support dynamic mustache variables for email body so we store the template in config
	EmailBody string `gorm:"default:''" json:"emailBody"`

	// NOTE: These flags are used to determine the flow for finding and contacting carriers
	// At least one should be set to true for the carrier quote "Email Carriers" section to be shown
	IsFindCarrierByGroupEnabled bool `gorm:"default:true;" json:"isFindCarrierByGroupEnabled"`
	// Location based carrier flow is primarily for SDS, it uses TMSLocations to find carriers
	IsFindCarrierByLocationEnabled bool `gorm:"default:false;" json:"isFindCarrierByLocationEnabled"`

	// Request Carrier Quote Form Options
	ShowCustomerSearch       bool `gorm:"default:false;" json:"showCustomerSearch"`
	ShowItemDescription      bool `gorm:"default:true;" json:"showItemDescription"`
	ShowDeliverySection      bool `gorm:"default:true;" json:"showDeliverySection"`
	RequireDeliveryLocation  bool `gorm:"default:true;" json:"requireDeliveryLocation"`
	ShowPickupAddressLine1   bool `gorm:"default:true;" json:"showPickupAddressLine1"`
	ShowDeliveryAddressLine1 bool `gorm:"default:true;" json:"showDeliveryAddressLine1"`

	// Custom name for the carrier quote feature
	// Ex: "White Glove" for SDS
	CustomName string `gorm:"default:''" json:"customName"`
}
